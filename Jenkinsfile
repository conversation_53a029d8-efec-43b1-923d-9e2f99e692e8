pipeline {
    agent any

    environment {
        DOCKER_COMPOSE_FILE = 'docker-compose.yml' // File docker-compose
        GITLAB_TOKEN = '**************************'
        PROJECT_ID = '62601003'  // ID của dự án GitLab
    }

    stages {
        stage('Set Permissions') {
            steps {
                script {
                    sh 'sudo chown -R jenkins:jenkins .'
                }
            }
        }
        stage('Build Angular Docker Image') {
            steps {
                script {
                    // Giả sử tất cả các file Dockerfile và Angular đều nằm ở thư mục gốc
                    echo "Building Docker image for Angular service"

                    try {
                        // Build Docker image cho Angular
                        sh """
                            docker build -t portal-admin-frontend:latest .
                        """
                    } catch (Exception e) {
                        echo "Build failed for Angular service. Error: ${e.getMessage()}"
                        error("Stopping pipeline due to build failure.")
                    }
                }
            }
        }


        stage('Deploy Docker') {
            steps {
                script {
                    sh """
                        # Dừng và xóa container cũ nếu tồn tại
                        docker rm -f portal-admin-frontend || true

                        # Xóa image cũ nếu tồn tại
                        docker rmi \$(docker images -q portal-admin-frontend) || true

                        # Chạy lại container với docker-compose
                        docker-compose -f ${DOCKER_COMPOSE_FILE} up -d --force-recreate portal-admin-frontend
                    """
                }
            }
        }
    }

    post {
        success {
            script {
                // Cập nhật trạng thái "success" cho GitLab
                updateCommitStatus("success")
            }
        }

        failure {
            script {
                // Cập nhật trạng thái "failed" cho GitLab
                updateCommitStatus("failed")
            }
        }
    }
}

def updateCommitStatus(status) {
    try {
        // Lấy commit SHA hiện tại
        def commitSha = sh(script: "git rev-parse HEAD", returnStdout: true).trim()
        echo "Commit SHA: ${commitSha}"

        // Lấy URL của build Jenkins tự động
        def buildUrl = env.BUILD_URL
        def buildNumber = env.BUILD_NUMBER
        echo "Build URL: ${buildUrl}"
        echo "Build Number: ${buildNumber}"

        // Kiểm tra thông tin project và token
        echo "Project ID: ${PROJECT_ID}"
        echo "GitLab Token: ${GITLAB_TOKEN ? 'set' : 'not set'}"

        // URL API GitLab để cập nhật trạng thái commit
        def url = "https://gitlab.com/api/v4/projects/${PROJECT_ID}/statuses/${commitSha}"

        // Cấu hình message với markdown để tạo liên kết clickable
        def message = "[Build status: ${status}](${buildUrl})"
        echo "Message: ${message}"

        // Gửi yêu cầu HTTP POST với curl và kiểm tra phản hồi
        def response = sh(script: """
            curl --header "PRIVATE-TOKEN: ${GITLAB_TOKEN}" \
                 --data "state=${status}&description=${message}&target_url=${buildUrl}" \
                 "${url}"
        """, returnStdout: true).trim()

        echo "Response from GitLab: ${response}"

        // Kiểm tra nếu có lỗi trong phản hồi
        if (response.contains("error")) {
            error "Failed to update commit status. Response: ${response}"
        }

        echo "Commit status updated to: ${status} with Jenkins build URL"
    } catch (Exception e) {
        // Nếu có lỗi, in ra thông báo và tiếp tục pipeline mà không làm gián đoạn quá trình build
        echo "An error occurred while updating commit status: ${e.message}. Continuing with the build..."
    }
}
