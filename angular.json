{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"AdminPronexus": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["apexcharts", "bezier-easing", "moment", "dropzone", "lodash", "moment", "j<PERSON>y", "js-sha256", "file-saver"], "outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/assets/images/icons", {"glob": "**/*", "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "/assets/"}, {"glob": "**/*", "input": "node_modules/monaco-editor/", "output": "/assets/monaco/"}], "styles": ["src/styles.scss", "src/assets/scss/style.scss", "node_modules/angular-calendar/css/angular-calendar.css", "node_modules/dropzone/dist/dropzone.css", "@ng-select/ng-select/themes/default.theme.css"], "stylePreprocessorOptions": {"includePaths": ["./node_modules", "src", "src/assets/scss", "src/assets/scss/ui-remove"]}, "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "5mb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true}, "uat": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.uat.ts"}], "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "5mb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "AdminPronexus:build:production"}, "uat": {"buildTarget": "AdminPronexus:build:uat"}, "dev": {"buildTarget": "AdminPronexus:build:dev"}, "development": {"buildTarget": "AdminPronexus:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "AdminPronexus:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}, "dynamic-builder-ui": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/dynamic-builder-ui", "sourceRoot": "projects/dynamic-builder-ui/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser-esbuild", "options": {"outputPath": "dist/dynamic-builder-ui", "index": "projects/dynamic-builder-ui/src/index.html", "main": "projects/dynamic-builder-ui/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/dynamic-builder-ui/tsconfig.app.json", "inlineStyleLanguage": "scss", "stylePreprocessorOptions": {"includePaths": ["./node_modules", "src"]}, "assets": ["projects/dynamic-builder-ui/src/favicon.ico", "projects/dynamic-builder-ui/src/assets"], "styles": ["projects/dynamic-builder-ui/src/styles.scss"], "scripts": []}, "configurations": {"production": {"tsConfig": "projects/dynamic-builder-ui/tsconfig.lib.prod.json", "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "200kb"}], "outputHashing": "all"}, "development": {"tsConfig": "projects/dynamic-builder-ui/tsconfig.lib.json", "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "dynamic-builder-ui:build:production"}, "development": {"buildTarget": "dynamic-builder-ui:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "dynamic-builder-ui:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/dynamic-builder-ui/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["projects/dynamic-builder-ui/src/favicon.ico", "projects/dynamic-builder-ui/src/assets"], "styles": ["projects/dynamic-builder-ui/src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["projects/dynamic-builder-ui/**/*.ts", "projects/dynamic-builder-ui/**/*.html"]}}}}}, "cli": {"analytics": false}}