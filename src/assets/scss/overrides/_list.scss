.mat-mdc-menu-item:hover:not([disabled]) {
  background: $hoverBgColor;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.w-80 {
  width: 80%;
}

.text-hover-primary:hover {
  .hover-text {
    color: $primary;
  }
}

.z-1 {
  z-index: 1;
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.mt-auto {
  margin-top: auto;
}

.shadow-none {
  box-shadow: none !important;
}

.overflow-hidden {
  overflow: hidden;
}

.text-decoration-none {
  text-decoration: none;
}

.position-relative {
  position: relative;
}

.op-5 {
  opacity: 0.5;
}

.op-75 {
  opacity: 0.75;
}

.cursor-pointer {
  cursor: pointer;
}
