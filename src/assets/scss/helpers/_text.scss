@use 'sass:map';
@use 'variables';

$utilities: (
  'font-size': (
    property: font-size,
    class: f-s,
    values: variables.$font-sizes
  ),
  'font-weight': (
    property: font-weight,
    class: f-w,
    values: variables.$font-wieghts
  ),
  'font-style': (
    property: font-style,
    class: font,
    values: italic normal
  ),
  'text-align': (
    property: text-align,
    class: text,
    values: center right left
  ),
  'text-decoration': (
    property: text-decoration,
    class: text,
    values: none underline line-through
  ),
  'text-transform': (
    property: text-transform,
    class: text,
    values: capitalize uppercase lowercase
  ),
  'white-space': (
    property: white-space,
    class: text,
    values: (
      wrap: normal,
      nowrap: nowrap
    )
  ),
  'word-wrap': (
    property: word-wrap word-break,
    class: text,
    values: (
      break: break-word
    )
  ),
  'text-overflow': (
    property: text-overflow,
    class: text,
    values: ellipsis
  ),
  'text-color': (
    property: color,
    class: text,
    values: (
      reset: inherit,
      current: currentColor
    )
  )
);

.lh-lg {
  line-height: 36px !important;
}

.lh-sm {
  line-height: 20px !important;
}

.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.panel-heading-label {
  font-size: 20px;
  font-weight: 600;
  line-height: 24px;
  text-align: left;
}

.app-status-dot {
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.dot-status {
  width: 8px;
  height: 8px;
  display: block;
  border-radius: 50%;
  margin-right: 8px;

  &.app-status-inprocess {
    background-color: #007bff; // $color-blue-primary
  }

  &.app-status-approved {
    background-color: #28a745; // $color-sematic-success
  }
  &.app-status-completed {
    background-color: #28a745; // $color-sematic-success
  }
  &.app-status-waiting {
    background-color: #e59d01;
  }

  &.app-status-warning {
    background-color: #ffc107; // $sematic-warning
  }

  &.app-status-reject {
    background-color: #dc3545; // $color-sematic-error
  }

  &.app-status-unlock {
    background-color: #7b5fff;
  }
}
