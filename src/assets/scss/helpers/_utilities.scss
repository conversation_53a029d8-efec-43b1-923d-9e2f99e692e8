@use 'sass:math';

@for $i from 1 through 100 {
  .w-#{ '' + $i*1} {
    width: math.percentage(math.div(($i * 1), 100)) !important;
  }

  .h-#{ '' + $i*1} {
    height: math.percentage(math.div(($i * 1), 100)) !important;
  }
}

.w-33 {
  width: 33.33333333% !important;
}

/* max width - max height*/
@for $i from 1 through 100 {
  .max-w-#{ '' + $i*1} {
    width: math.percentage(math.div(($i * 1), 100)) !important;
  }

  .max-h-#{ '' + $i*1} {
    height: math.percentage(math.div(($i * 1), 100)) !important;
  }
}

@for $i from 1 through 100 {
  .flex-#{ '' + $i*1} {
    width: math.percentage(math.div(($i * 1), 100)) !important;
    max-width: math.percentage(math.div(($i * 1), 100)) !important;
  }
}
