import { CommonModule } from '@angular/common'
import { Component, Inject, Optional } from '@angular/core'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog'
import { TablerIconsModule } from 'angular-tabler-icons'
import { MaterialModule } from 'src/app/material.module'

@Component({
  selector: 'app-task-dialog',
  templateUrl: './task-dialog.component.html',
  standalone: true,
  imports: [MaterialModule, CommonModule, TablerIconsModule, FormsModule, ReactiveFormsModule]
})
export class TaskDialogComponent {
  action: string
  local_data: any

  constructor(
    public dialogRef: MatDialogRef<TaskDialogComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.local_data = { ...data }
    this.action = this.local_data.action
  }

  doAction(): void {
    this.dialogRef.close({ event: this.action, data: this.local_data })
  }

  closeDialog(): void {
    this.dialogRef.close({ event: 'Cancel' })
  }
}
