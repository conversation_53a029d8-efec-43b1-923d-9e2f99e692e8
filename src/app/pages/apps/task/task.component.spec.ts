import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing'
import { FormsModule } from '@angular/forms'

import { AppTaskComponent } from './task.component'

describe('AppTaskComponent', () => {
  let component: AppTaskComponent
  let fixture: ComponentFixture<AppTaskComponent>

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [AppTaskComponent],
      imports: [FormsModule]
    }).compileComponents()
  }))

  beforeEach(() => {
    fixture = TestBed.createComponent(AppTaskComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  // it('should create', () => {
  //   expect(component).toBeTruthy();
  // });
})
