<!-- listing and total no.of counts -->
<div class="row">
  <div class="col-sm-6 col-lg-3">
    <mat-card class="shadow-none">
      <div class="p-30 bg-primary text-center cursor-pointer rounded" (click)="btnCategoryClick('')">
        <h3 class="f-s-24 text-white f-w-600 m-0 m-b-4">{{ totalCount }}</h3>
        <h6 class="text-white f-w-600 m-0 f-s-16">Total Tickets</h6>
      </div>
    </mat-card>
  </div>
  <div class="col-sm-6 col-lg-3">
    <mat-card class="shadow-none">
      <div class="p-30 bg-warning text-center cursor-pointer rounded" (click)="btnCategoryClick('InProgress')">
        <h3 class="f-s-24 text-white m-0 m-b-4">{{ Inprogress }}</h3>
        <h6 class="text-white f-w-600 m-0 f-s-16">In Progress</h6>
      </div>
    </mat-card>
  </div>
  <div class="col-sm-6 col-lg-3">
    <mat-card class="shadow-none">
      <div class="p-30 bg-success text-center cursor-pointer rounded" (click)="btnCategoryClick('Open')">
        <h3 class="f-s-24 text-white m-0 m-b-4">{{ Open }}</h3>
        <h6 class="text-white f-w-600 m-0 f-s-16">Open</h6>
      </div>
    </mat-card>
  </div>
  <div class="col-sm-6 col-lg-3">
    <mat-card class="shadow-none">
      <div class="p-30 bg-error text-center cursor-pointer rounded" (click)="btnCategoryClick('Closed')">
        <h3 class="f-s-24 text-white m-0 m-b-4">{{ Closed }}</h3>
        <h6 class="text-white f-w-600 m-0 f-s-16">Closed</h6>
      </div>
    </mat-card>
  </div>
</div>
<!-- add and search tickets -->
<mat-card class="cardWithShadow">
  <mat-card-content>
    <div class="row justify-content-between">
      <div class="col-sm-4">
        <mat-form-field appearance="outline" class="hide-hint">
          <input matInput (keyup)="applyFilter($any($event.target).value)" placeholder="Search Tickets" />
          <mat-icon matSuffix>
            <i-tabler name="search" class="icon-20"></i-tabler>
          </mat-icon>
        </mat-form-field>
      </div>
      <div class="col-sm-4 d-flex justify-content-end align-items-center mt-xs-12 mt-lg-0">
        <button mat-flat-button (click)="openDialog('Add', {})" color="primary">Add Ticket</button>
      </div>
    </div>
  </mat-card-content>
</mat-card>
<!-- table -->
<mat-card class="cardWithShadow">
  <mat-card-content>
    <div class="table-responsive">
      <table mat-table [dataSource]="dataSource" class="w-100 f-w-500 no-wrap">
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef class="f-s-16 f-w-600">Id</th>
          <td mat-cell *matCellDef="let element">
            {{ element.id }}
          </td>
        </ng-container>

        <ng-container matColumnDef="title">
          <th mat-header-cell *matHeaderCellDef class="f-s-16 f-w-600">Title</th>
          <td mat-cell *matCellDef="let element">
            <h6 class="m-0 f-s-16 mat-subtitle-1 f-w-500 m-b-4">{{ element.title }}</h6>
            <span class="m-0 f-s-14 f-w-400 max-text d-block">{{ element.subtext }}</span>
          </td>
        </ng-container>

        <ng-container matColumnDef="assignee">
          <th mat-header-cell *matHeaderCellDef class="f-s-16 f-w-600">Assigned To</th>
          <td mat-cell *matCellDef="let element">
            <div class="d-flex align-items-center">
              <img [src]="element.imgSrc" alt="user" width="35" class="rounded-circle m-r-8" />
              <span class="f-w-600 f-s-16 mat-subtitle-1">{{ element.assignee }}</span>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef class="f-s-16 f-w-600">Status</th>
          <td mat-cell *matCellDef="let element">
            <!-- <span
              class="bg-light mat-body-2 f-w-500 p-x-8 p-y-4 rounded-pill f-s-12"
              >{{ element.status }}</span
            > -->

            @if (element.status == 'inprogress') {
              <span class="bg-light-warning text-warning mat-body-2 f-w-500 p-x-8 p-y-4 f-s-12 rounded-pill">In Progress</span>
            }

            @if (element.status == 'open') {
              <span class="bg-light-success text-success mat-body-2 f-w-500 p-x-8 p-y-4 f-s-12 rounded-pill">Open</span>
            }

            @if (element.status == 'closed') {
              <span class="bg-light-error text-error mat-body-2 f-w-500 p-x-8 p-y-4 f-s-12 rounded-pill">Closed</span>
            }
          </td>
        </ng-container>

        <ng-container matColumnDef="date">
          <th mat-header-cell *matHeaderCellDef class="f-s-16 f-w-600">Date</th>
          <td mat-cell *matCellDef="let element" class="f-s-14">
            {{ element.date | date: 'fullDate' }}
          </td>
        </ng-container>

        <ng-container matColumnDef="action">
          <th mat-header-cell *matHeaderCellDef class="f-s-16 f-w-600">Action</th>
          <td mat-cell *matCellDef="let element" class="action-link">
            <a (click)="openDialog('Update', element)" class="m-r-10 cursor-pointer"><i-tabler name="edit" class="icon-18"></i-tabler></a>
            <a (click)="openDialog('Delete', element)" class="m-r-10 cursor-pointer">
              <i-tabler name="trash" class="icon-18"></i-tabler>
            </a>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
    </div>

    <mat-paginator [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>
  </mat-card-content>
</mat-card>
