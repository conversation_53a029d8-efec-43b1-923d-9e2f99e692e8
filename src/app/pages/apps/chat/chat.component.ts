import { Component, ElementRef, ViewChild } from '@angular/core'
import { messages } from './chat-data'
import { CommonModule } from '@angular/common'
import { NgScrollbarModule } from 'ngx-scrollbar'
import { TablerIconsModule } from 'angular-tabler-icons'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MaterialModule } from 'src/app/material.module'

@Component({
  selector: 'app-chat',
  standalone: true,
  imports: [CommonModule, NgScrollbarModule, TablerIconsModule, FormsModule, ReactiveFormsModule, MaterialModule],
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.scss']
})
export class AppChatComponent {
  sidePanelOpened = true
  msg = ''

  // MESSAGE
  selectedMessage: any

  public messages: Array<any> = messages
  // tslint:disable-next-line - Disables all
  // messages: Object[] = messages;

  constructor() {
    this.selectedMessage = this.messages[0]
  }

  @ViewChild('myInput', { static: true }) myInput: ElementRef = Object.create(null)

  isOver(): boolean {
    return window.matchMedia(`(max-width: 960px)`).matches
  }

  // tslint:disable-next-line - Disables all
  onSelect(message: Object[]): void {
    this.selectedMessage = message
  }

  OnAddMsg(): void {
    this.msg = this.myInput.nativeElement.value

    if (this.msg !== '') {
      this.selectedMessage.chat.push({
        type: 'even',
        msg: this.msg,
        date: new Date()
      })
    }

    this.myInput.nativeElement.value = ''
  }
}
