<mat-card class="cardWithShadow">
  <mat-card-content>
    <mat-card-title>Table with multiple header and footer rows</mat-card-title>
    <mat-card-subtitle class="mat-body-1">Pagination and search is avail</mat-card-subtitle>
    <div class="table-responsive">
      <table mat-table [dataSource]="transactions" class="w-100">
        <!-- Item Column -->
        <ng-container matColumnDef="item">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-16">Item</th>
          <td mat-cell *matCellDef="let transaction">
            <div class="d-flex align-items-center gap-12">
              <img [src]="transaction.img" alt="product" width="45" class="rounded" />
              <span class="f-s-16 f-w-600">{{ transaction.item }}</span>
            </div>
          </td>
          <td mat-footer-cell *matFooterCellDef class="f-w-600 mat-subtitle-1 f-s-16">Total</td>
        </ng-container>

        <!-- Cost Column -->
        <ng-container matColumnDef="cost">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-16">Cost</th>
          <td mat-cell *matCellDef="let transaction">
            {{ transaction.cost | currency }}
          </td>
          <td mat-footer-cell *matFooterCellDef>
            {{ getTotalCost() | currency }}
          </td>
        </ng-container>

        <!-- Item Description Column -->
        <ng-container matColumnDef="item-description">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">Name of the item purchased</th>
        </ng-container>

        <!-- Cost Description Column -->
        <ng-container matColumnDef="cost-description">
          <th mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">Cost of the item in USD</th>
        </ng-container>

        <!-- Disclaimer column -->
        <ng-container matColumnDef="disclaimer">
          <td mat-footer-cell *matFooterCellDef colspan="2" class="text-danger fw-medium">
            Please note that the cost of items displayed are completely and totally made up.
          </td>
        </ng-container>

        <!-- The table will render two header rows, one data row per data object, and two footer rows. -->
        <tr mat-header-row *matHeaderRowDef="displayedColumns" class="example-first-header-row"></tr>
        <tr mat-header-row *matHeaderRowDef="['item-description', 'cost-description']" class="example-second-header-row"></tr>

        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>

        <tr mat-footer-row *matFooterRowDef="displayedColumns" class="example-first-footer-row"></tr>
        <tr mat-footer-row *matFooterRowDef="['disclaimer']" class="example-second-footer-row"></tr>
      </table>
    </div>
  </mat-card-content>
</mat-card>
