<mat-card class="cardWithShadow">
  <mat-card-content>
    <mat-card-title>Pagination Table</mat-card-title>
    <mat-card-subtitle class="mat-body-1">get nore item with pagination</mat-card-subtitle>
    <div class="table-responsive">
      <mat-table #table [dataSource]="dataSource">
        <!-- Position Column -->
        <ng-container matColumnDef="assigned">
          <mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14 p-l-0">Assigned</mat-header-cell>
          <mat-cell class="p-16 p-l-0" *matCellDef="let element">
            <div class="d-flex align-items-center">
              <img [src]="element.imagePath" alt="users" width="40" class="rounded-circle" />
              <div class="m-l-16">
                <h6 class="mat-subtitle-1 f-s-14 f-w-600">
                  {{ element.uname }}
                </h6>
                <span class="mat-body-1 f-s-12">
                  {{ element.position }}
                </span>
              </div>
            </div>
          </mat-cell>
        </ng-container>

        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">Name</mat-header-cell>
          <mat-cell class="p-16" *matCellDef="let element" class="mat-body-1">
            {{ element.productName }}
          </mat-cell>
        </ng-container>

        <!-- Weight Column -->
        <ng-container matColumnDef="priority">
          <mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14">Priority</mat-header-cell>
          <mat-cell class="p-16" *matCellDef="let element">
            @if (element.priority == 'low') {
              <span class="bg-light-accent text-accent rounded f-w-600 p-6 p-y-4 f-s-12">
                {{ element.priority | titlecase }}
              </span>
            }

            @if (element.priority == 'medium') {
              <span class="bg-light-primary text-primary rounded f-w-600 p-6 p-y-4 f-s-12">
                {{ element.priority | titlecase }}
              </span>
            }

            @if (element.priority == 'high') {
              <span class="bg-light-warning text-warning rounded f-w-600 p-6 p-y-4 f-s-12">
                {{ element.priority | titlecase }}
              </span>
            }

            @if (element.priority == 'critical') {
              <span class="bg-light-error text-error rounded f-w-600 p-6 p-y-4 f-s-12">
                {{ element.priority | titlecase }}
              </span>
            }

            @if (element.priority == 'moderate') {
              <span class="bg-light-success text-success rounded f-w-600 p-6 p-y-4 f-s-12">
                {{ element.priority | titlecase }}
              </span>
            }
          </mat-cell>
        </ng-container>

        <!-- Symbol Column -->
        <ng-container matColumnDef="budget">
          <mat-header-cell *matHeaderCellDef class="f-w-600 mat-subtitle-1 f-s-14 text-right p-r-0">Budget</mat-header-cell>
          <mat-cell class="p-16 mat-body-1 p-r-0 text-right" *matCellDef="let element">${{ element.budget }}k</mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
      </mat-table>

      <mat-paginator #paginator [pageSize]="10" [pageSizeOptions]="[5, 10, 20]"></mat-paginator>
    </div>
  </mat-card-content>
</mat-card>
