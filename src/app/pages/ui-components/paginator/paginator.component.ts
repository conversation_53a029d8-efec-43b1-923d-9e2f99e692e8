import { Component } from '@angular/core'
import { MatPaginatorModule } from '@angular/material/paginator'
import { MatSlideToggleModule } from '@angular/material/slide-toggle'
import { FormsModule } from '@angular/forms'
import { MatInputModule } from '@angular/material/input'
import { MatFormFieldModule } from '@angular/material/form-field'
import { MatCardModule } from '@angular/material/card'

@Component({
  selector: 'app-paginator',
  standalone: true,
  imports: [MatFormFieldModule, MatInputModule, FormsModule, MatSlideToggleModule, MatPaginatorModule, MatCardModule],
  templateUrl: './paginator.component.html'
})
export class AppPaginatorComponent {
  constructor() {}
}
