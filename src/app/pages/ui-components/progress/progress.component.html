<mat-card class="cardWithShadow theme-card">
  <mat-card-header>
    <mat-card-title class="m-b-0">Progress bar</mat-card-title>
  </mat-card-header>
  <mat-card-content class="b-t-1">
    <mat-card class="b-1 shadow-none">
      <mat-card-header>
        <mat-card-title>Determinate</mat-card-title>
      </mat-card-header>
      <mat-card-content class="b-t-1">
        <mat-progress-bar mode="determinate" value="40"></mat-progress-bar>
      </mat-card-content>
    </mat-card>

    <mat-card class="b-1 shadow-none">
      <mat-card-header>
        <mat-card-title>Indeterminate</mat-card-title>
      </mat-card-header>
      <mat-card-content class="b-t-1">
        <mat-progress-bar mode="indeterminate"></mat-progress-bar>
      </mat-card-content>
    </mat-card>

    <mat-card class="b-1 shadow-none">
      <mat-card-header>
        <mat-card-title>Query</mat-card-title>
      </mat-card-header>
      <mat-card-content class="b-t-1">
        <mat-progress-bar mode="query"></mat-progress-bar>
      </mat-card-content>
    </mat-card>

    <mat-card class="b-1 shadow-none">
      <mat-card-header>
        <mat-card-title>Buffer</mat-card-title>
      </mat-card-header>
      <mat-card-content class="b-t-1">
        <mat-progress-bar mode="buffer"></mat-progress-bar>
      </mat-card-content>
    </mat-card>

    <mat-card class="b-1 shadow-none">
      <mat-card-header>
        <mat-card-title>Configurable</mat-card-title>
      </mat-card-header>
      <mat-card-content class="b-t-1">
        <div>
          <label class="mat-body-2 f-w-600">Color:</label>
          <mat-radio-group [(ngModel)]="color">
            <mat-radio-button value="primary">Primary</mat-radio-button>
            <mat-radio-button value="accent">Accent</mat-radio-button>
            <mat-radio-button value="warn">Warn</mat-radio-button>
          </mat-radio-group>
        </div>
        <div>
          <label class="mat-body-2 f-w-600">Mode:</label>
          <mat-radio-group [(ngModel)]="mode">
            <mat-radio-button value="determinate">Determinate</mat-radio-button>
            <mat-radio-button value="indeterminate">Indeterminate</mat-radio-button>
            <mat-radio-button value="buffer">Buffer</mat-radio-button>
            <mat-radio-button value="query">Query</mat-radio-button>
          </mat-radio-group>
        </div>
        @if (mode === 'determinate' || mode === 'buffer') {
          <div class="example-section">
            <label class="mat-body-2 f-w-600">Progress:</label>
            <mat-slider>
              <input type="range" [(ngModel)]="value" matSliderThumb />
            </mat-slider>
          </div>
        }
        @if (mode === 'buffer') {
          <div class="example-section">
            <label class="mat-body-2 f-w-600">Buffer:</label>
            <mat-slider>
              <input type="range" [(ngModel)]="bufferValue" matSliderThumb />
            </mat-slider>
          </div>
        }

        <div class="p-24 rounded bg-light-primary m-t-16">
          <h2 class="mat-body-2 f-w-600 m-b-16">Result</h2>

          <mat-progress-bar [color]="color" [mode]="mode" [value]="value" [bufferValue]="bufferValue"></mat-progress-bar>
        </div>
      </mat-card-content>
    </mat-card>
  </mat-card-content>
</mat-card>
