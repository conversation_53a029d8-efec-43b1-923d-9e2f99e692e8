import { Component, OnInit } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { MatCardModule } from '@angular/material/card'
import { ThemePalette } from '@angular/material/core'
import { MatProgressSpinnerModule, ProgressSpinnerMode } from '@angular/material/progress-spinner'
import { MatRadioModule } from '@angular/material/radio'
import { MatSliderModule } from '@angular/material/slider'

@Component({
  selector: 'app-progress-snipper',
  standalone: true,
  imports: [MatCardModule, MatRadioModule, FormsModule, MatSliderModule, MatProgressSpinnerModule],
  templateUrl: './progress-snipper.component.html'
})
export class AppProgressSnipperComponent implements OnInit {
  constructor() {}

  ngOnInit(): void {}

  color: ThemePalette = 'primary'
  mode: ProgressSpinnerMode = 'determinate'
  value = 50
}
