import { Component, OnInit } from '@angular/core'
import { MatSliderModule } from '@angular/material/slider'
import { MatCheckboxModule } from '@angular/material/checkbox'
import { FormsModule } from '@angular/forms'
import { MatInputModule } from '@angular/material/input'
import { MatFormFieldModule } from '@angular/material/form-field'
import { MatCardModule } from '@angular/material/card'

@Component({
  selector: 'app-slider',
  standalone: true,
  imports: [MatCardModule, MatFormFieldModule, MatInputModule, FormsModule, MatCheckboxModule, MatSliderModule],
  templateUrl: './slider.component.html'
})
export class AppSliderComponent implements OnInit {
  disabled = false
  max = 100
  min = 0
  showTicks = false
  step = 1
  thumbLabel = false
  value = 0

  // 2
  formatLabel(value: number): string {
    if (value >= 1000) {
      return Math.round(value / 1000) + 'k'
    }

    return `${value}`
  }

  constructor() {}

  ngOnInit(): void {}
}
