<div class="blank-layout-container justify-content-center">
  <div class="position-relative row w-100 h-100">
    <div class="col-lg-4">
      <div class="d-flex align-items-start align-items-lg-center justify-content-center h-100">
        <div class="row justify-content-center w-100 h-100 align-items-center">
          <div class="col-lg-9 max-width-form">
            <a [routerLink]="['/']">
              <img src="./assets/images/logos/logo-icon.svg" class="align-middle m-2" alt="logo" />
            </a>
            <h4 class="f-w-700 f-s-24 m-t-24 m-b-8">Sign Up</h4>
            <span class="f-s-14 d-block mat-body-1 m-t-8">Your Admin Dashboard</span>

            <div class="row m-t-24">
              <div class="col-6">
                <button mat-stroked-button class="w-100">
                  <div class="d-flex align-items-center">
                    <img src="/assets/images/svgs/google-icon.svg" alt="google" width="16" class="m-r-8" />
                    Google
                  </div>
                </button>
              </div>
              <div class="col-6">
                <button mat-stroked-button class="w-100 d-flex align-items-center">
                  <div class="d-flex align-items-center">
                    <img src="/assets/images/svgs/facebook-icon.svg" alt="facebook" width="40" class="m-r-4" />
                    Facebook
                  </div>
                </button>
              </div>
            </div>

            <div class="or-border m-t-30">or Sign Up with</div>

            <form class="m-t-30" [formGroup]="form" (ngSubmit)="submit()">
              <mat-label class="f-s-14 f-w-600 m-b-12 d-block">Name</mat-label>
              <mat-form-field appearance="outline" class="w-100" color="primary">
                <input matInput formControlName="uname" />
                @if (f['uname'].touched && f['uname'].invalid) {
                  <mat-hint class="m-b-16 error-msg">
                    @if (f['uname'].errors && f['uname'].errors['required']) {
                      <div class="text-error">Name is required.</div>
                    }
                    @if (f['uname'].errors && f['uname'].errors['minlength']) {
                      <div class="text-error">Name should be 6 character.</div>
                    }
                  </mat-hint>
                }
              </mat-form-field>
              <!-- email -->
              <mat-label class="f-s-14 f-w-600 m-b-12 d-block">Email Address</mat-label>
              <mat-form-field appearance="outline" class="w-100" color="primary">
                <input matInput type="email" formControlName="email" />
                @if (f['email'].touched && f['email'].invalid) {
                  <mat-hint class="m-b-16 error-msg">
                    @if (f['email'].errors && f['email'].errors['required']) {
                      <div class="text-error">Email is required.</div>
                    }
                  </mat-hint>
                }
              </mat-form-field>
              <!-- password -->
              <mat-label class="f-s-14 f-w-600 m-b-12 d-block">Password</mat-label>
              <mat-form-field appearance="outline" class="w-100" color="primary">
                <input matInput type="password" formControlName="password" />
                @if (f['password'].touched && f['password'].invalid) {
                  <mat-hint class="m-b-16 error-msg">
                    @if (f['password'].errors && f['password'].errors['required']) {
                      <div class="text-error">Password is required.</div>
                    }
                  </mat-hint>
                }
              </mat-form-field>

              <button mat-flat-button color="primary" class="w-100" [disabled]="!form.valid">Sign Up</button>
              <!-- input -->
            </form>
            <span class="d-block f-w-500 d-block m-t-24">
              Already have an Account?
              <a [routerLink]="['/authentication/login']" class="text-decoration-none text-primary f-w-500 f-s-14">Sign In</a>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="col-lg-8 position-relative overflow-hidden bg-dark-200 d-none d-lg-block">
      <div class="circle-top"></div>
      <div>
        <img src="/assets/images/logos/logo-icon.svg" class="circle-bottom" alt="Logo-Dark" />
      </div>
      <div class="d-flex align-items-center z-1 position-relative h-n80">
        <div class="row justify-content-center w-100">
          <div class="col-lg-6">
            <h2 class="text-white f-s-40 m-b-16">
              Welcome to
              <br />
              MatDash
            </h2>
            <span class="op-75 f-s-16 text-white d-block m-b-16">
              MatDash helps developers to build organized and well
              <br />
              coded dashboards full of beautiful and rich modules.
            </span>
            <button mat-flat-button color="primary">Learn More</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
