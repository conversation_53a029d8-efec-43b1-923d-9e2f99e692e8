<mat-card class="cardWithShadow">
  <mat-card-content>
    <mat-card-title>Daily activities</mat-card-title>

    <div class="timeline m-t-24">
      @for (stat of stats; track stat.id) {
        <div class="timeline-item d-flex overflow-hidden">
          <div class="time text-right mat-subtitle-1 f-s-14">{{ stat.time }}</div>
          <div class="point d-flex align-items-center">
            <span class="timeline-badge bg-{{ stat.color }} m-y-8"></span>
            <span class="timline-border d-block"></span>
          </div>
          <div class="desc">
            @if (stat.subtext) {
              <span class="mat-subtitle-1 f-s-14 lh-20">{{ stat.subtext }}</span>
            }

            @if (stat.title) {
              <span class="mat-subtitle-1 f-s-14 lh-20 f-w-600 d-block">{{ stat.title }}</span>
            }

            @if (stat.link) {
              <a href="#" class="text-primary text-decoration-none mat-body-1">#ML-3467</a>
            }
          </div>
        </div>
      }
    </div>
  </mat-card-content>
</mat-card>
