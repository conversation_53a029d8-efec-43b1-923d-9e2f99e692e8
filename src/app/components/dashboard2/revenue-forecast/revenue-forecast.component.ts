import { Component, ViewChild } from '@angular/core'
import { MaterialModule } from '../../../material.module'
import { TablerIconsModule } from 'angular-tabler-icons'
import {
  ApexAxisChartSeries,
  ApexChart,
  ApexDataLabels,
  ApexFill,
  ApexLegend,
  ApexPlotOptions,
  ApexStroke,
  ApexTooltip,
  ChartComponent,
  NgApexchartsModule
} from 'ng-apexcharts'

export interface revenueForecastChart {
  series: ApexAxisChartSeries
  chart: ApexChart
  dataLabels: ApexDataLabels
  plotOptions: ApexPlotOptions
  tooltip: ApexTooltip
  stroke: ApexStroke
  legend: ApexLegend
  fill: ApexFill
}

interface performanceLists {
  id: number
  color: string
  textcolor: string
  icon: string
  title: string
  subtext: string
}

@Component({
  selector: 'app-revenue-forecast',
  standalone: true,
  imports: [MaterialModule, TablerIconsModule, NgApexchartsModule],
  templateUrl: './revenue-forecast.component.html'
})
export class AppRevenueForecastComponent {
  @ViewChild('chart') chart: ChartComponent = Object.create(null)
  public revenueForecastChart!: Partial<revenueForecastChart> | any

  constructor() {
    this.revenueForecastChart = {
      series: [
        {
          name: '2024',
          data: [1.2, 2.7, 1, 3.6, 2.1, 2.7, 2.2, 1.3, 2.5]
        },
        {
          name: '2023',
          data: [-2.8, -1.1, -2.5, -1.5, -2.3, -1.9, -1, -2.1, -1.3]
        }
      ],

      chart: {
        type: 'bar',
        fontFamily: 'inherit',
        foreColor: '#adb0bb',
        height: 295,
        stacked: true,
        offsetX: -15,
        toolbar: {
          show: false
        }
      },
      colors: ['rgba(99, 91, 255, 1)', 'rgba(255, 102, 146,1)'],
      plotOptions: {
        bar: {
          horizontal: false,
          barHeight: '60%',
          columnWidth: '15%',
          borderRadius: [6],
          borderRadiusApplication: 'end',
          borderRadiusWhenStacked: 'all'
        }
      },
      dataLabels: {
        enabled: false
      },
      legend: {
        show: false
      },
      grid: {
        show: true,
        padding: {
          top: 0,
          bottom: 0,
          right: 0
        },
        borderColor: 'rgba(0,0,0,0.05)',
        xaxis: {
          lines: {
            show: true
          }
        },
        yaxis: {
          lines: {
            show: true
          }
        }
      },

      yaxis: {
        min: -5,
        max: 5,
        tickAmount: 4
      },
      xaxis: {
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        },
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'July', 'Aug', 'Sep'],
        labels: {
          style: { fontSize: '13px', colors: '#adb0bb', fontWeight: '400' }
        }
      },
      tooltip: {
        theme: 'dark',
        x: {
          show: false
        }
      }
    }
  }

  performanceLists: performanceLists[] = [
    {
      id: 1,
      color: 'light',
      textcolor: 'dark',
      icon: 'solar:pie-chart-2-linear',
      title: '$96,640',
      subtext: 'Total'
    },
    {
      id: 2,
      color: 'light-primary',
      textcolor: 'primary',
      icon: 'solar:dollar-minimalistic-linear',
      title: '$48,820',
      subtext: 'Profit'
    },
    {
      id: 3,
      color: 'light-error',
      textcolor: 'error',
      icon: 'solar:database-linear',
      title: '$48,450',
      subtext: 'Earnings'
    }
  ]
}
