<mat-card class="cardWithShadow">
  <mat-card-content class="p-b-8">
    <div class="d-flex align-items-center">
      <mat-card-title>Revenue by Product</mat-card-title>
      <div class="m-l-auto">
        <mat-form-field class="theme-select" appearance="outline">
          <mat-select value="mar">
            @for (month of months; track month.viewValue) {
              <mat-option [value]="month.value">
                {{ month.viewValue }}
              </mat-option>
            }
          </mat-select>
        </mat-form-field>
      </div>
    </div>

    <mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start" class="theme-tab m-t-20">
      <mat-tab>
        <ng-template mat-tab-label>
          <i class="iconify f-s-20 d-flex m-r-8" data-icon="solar:widget-linear"></i>
          App
        </ng-template>
        <div class="table-responsive">
          <table mat-table [dataSource]="dataSource" class="w-100">
            <!-- Profile Column -->
            <ng-container matColumnDef="assigned">
              <th mat-header-cell *matHeaderCellDef class="f-w-400 mat-subtitle-1 f-s-14 p-l-0">Assigned</th>
              <td mat-cell *matCellDef="let element" class="p-l-0">
                <div class="d-flex align-items-center">
                  <img [src]="element.imagePath" alt="users" width="48" class="rounded" />
                  <div class="m-l-12">
                    <h6 class="mat-subtitle-1 text-truncate-2 f-s-15 f-w-600">
                      {{ element.uname }}
                    </h6>
                    <span class="f-s-14 mat-body-1">{{ element.position }}</span>
                  </div>
                </div>
              </td>
            </ng-container>

            <!-- Hour Rate Column -->
            <ng-container matColumnDef="progress">
              <th mat-header-cell *matHeaderCellDef class="f-w-400 mat-subtitle-1 f-s-14">Progress</th>
              <td mat-cell *matCellDef="let element" class="mat-body-1">
                <span>
                  <span class="f-w-600 f-s-14">{{ element.hrate }}</span>
                  %
                </span>
              </td>
            </ng-container>

            <!-- Symbol Column -->
            <ng-container matColumnDef="priority">
              <th mat-header-cell *matHeaderCellDef class="f-w-400 mat-subtitle-1 f-s-14">Priority</th>
              <td mat-cell *matCellDef="let element">
                <span class="bg-light-{{ element.progress }} text-{{ element.progress }} rounded-sm f-w-600 p-6 p-y-4 f-s-12">
                  {{ element.priority | titlecase }}
                </span>
              </td>
            </ng-container>

            <!-- Weight Column -->
            <ng-container matColumnDef="budget">
              <th mat-header-cell *matHeaderCellDef class="f-w-400 mat-subtitle-1 f-s-14">Budget</th>
              <td mat-cell *matCellDef="let element">
                <span class="f-s-14">${{ element.skills }}k</span>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
        </div>
      </mat-tab>
      <mat-tab>
        <ng-template mat-tab-label>
          <i class="iconify f-s-20 d-flex m-r-8" data-icon="solar:smartphone-line-duotone"></i>
          Mobile
        </ng-template>
        <div class="table-responsive">
          <table mat-table [dataSource]="dataSource" class="w-100">
            <!-- Profile Column -->
            <ng-container matColumnDef="assigned">
              <th mat-header-cell *matHeaderCellDef class="f-w-400 mat-subtitle-1 f-s-14 p-l-0">Assigned</th>
              <td mat-cell *matCellDef="let element" class="p-l-0">
                <div class="d-flex align-items-center">
                  <img [src]="element.imagePath" alt="users" width="48" class="rounded" />
                  <div class="m-l-12">
                    <h6 class="mat-subtitle-1 text-truncate-2 f-s-15 f-w-600">
                      {{ element.uname }}
                    </h6>
                    <span class="f-s-14 mat-body-1">{{ element.position }}</span>
                  </div>
                </div>
              </td>
            </ng-container>

            <!-- Hour Rate Column -->
            <ng-container matColumnDef="progress">
              <th mat-header-cell *matHeaderCellDef class="f-w-400 mat-subtitle-1 f-s-14">Progress</th>
              <td mat-cell *matCellDef="let element" class="mat-body-1">
                <span>
                  <span class="f-w-600 f-s-14">{{ element.hrate }}</span>
                  %
                </span>
              </td>
            </ng-container>

            <!-- Symbol Column -->
            <ng-container matColumnDef="priority">
              <th mat-header-cell *matHeaderCellDef class="f-w-400 mat-subtitle-1 f-s-14">Priority</th>
              <td mat-cell *matCellDef="let element">
                <span class="bg-light-{{ element.progress }} text-{{ element.progress }} rounded-sm f-w-600 p-6 p-y-4 f-s-12">
                  {{ element.priority | titlecase }}
                </span>
              </td>
            </ng-container>

            <!-- Weight Column -->
            <ng-container matColumnDef="budget">
              <th mat-header-cell *matHeaderCellDef class="f-w-400 mat-subtitle-1 f-s-14">Budget</th>
              <td mat-cell *matCellDef="let element">
                <span class="f-s-14">${{ element.skills }}k</span>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
        </div>
      </mat-tab>
      <mat-tab>
        <ng-template mat-tab-label>
          <i class="iconify f-s-20 d-flex m-r-8" data-icon="solar:calculator-linear"></i>
          SaaS
        </ng-template>
        <div class="table-responsive">
          <table mat-table [dataSource]="dataSource" class="w-100">
            <!-- Profile Column -->
            <ng-container matColumnDef="assigned">
              <th mat-header-cell *matHeaderCellDef class="f-w-400 mat-subtitle-1 f-s-14 p-l-0">Assigned</th>
              <td mat-cell *matCellDef="let element" class="p-l-0">
                <div class="d-flex align-items-center">
                  <img [src]="element.imagePath" alt="users" width="48" class="rounded" />
                  <div class="m-l-12">
                    <h6 class="mat-subtitle-1 text-truncate-2 f-s-15 f-w-600">
                      {{ element.uname }}
                    </h6>
                    <span class="f-s-14 mat-body-1">{{ element.position }}</span>
                  </div>
                </div>
              </td>
            </ng-container>

            <!-- Hour Rate Column -->
            <ng-container matColumnDef="progress">
              <th mat-header-cell *matHeaderCellDef class="f-w-400 mat-subtitle-1 f-s-14">Progress</th>
              <td mat-cell *matCellDef="let element" class="mat-body-1">
                <span>
                  <span class="f-w-600 f-s-14">{{ element.hrate }}</span>
                  %
                </span>
              </td>
            </ng-container>

            <!-- Symbol Column -->
            <ng-container matColumnDef="priority">
              <th mat-header-cell *matHeaderCellDef class="f-w-400 mat-subtitle-1 f-s-14">Priority</th>
              <td mat-cell *matCellDef="let element">
                <span class="bg-light-{{ element.progress }} text-{{ element.progress }} rounded-sm f-w-600 p-6 p-y-4 f-s-12">
                  {{ element.priority | titlecase }}
                </span>
              </td>
            </ng-container>

            <!-- Weight Column -->
            <ng-container matColumnDef="budget">
              <th mat-header-cell *matHeaderCellDef class="f-w-400 mat-subtitle-1 f-s-14">Budget</th>
              <td mat-cell *matCellDef="let element">
                <span class="f-s-14">${{ element.skills }}k</span>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
        </div>
      </mat-tab>
      <mat-tab>
        <ng-template mat-tab-label>
          <i class="iconify f-s-20 d-flex m-r-8" data-icon="solar:folder-open-outline"></i>
          Others
        </ng-template>
        <div class="table-responsive">
          <table mat-table [dataSource]="dataSource" class="w-100">
            <!-- Profile Column -->
            <ng-container matColumnDef="assigned">
              <th mat-header-cell *matHeaderCellDef class="f-w-400 mat-subtitle-1 f-s-14 p-l-0">Assigned</th>
              <td mat-cell *matCellDef="let element" class="p-l-0">
                <div class="d-flex align-items-center">
                  <img [src]="element.imagePath" alt="users" width="48" class="rounded" />
                  <div class="m-l-12">
                    <h6 class="mat-subtitle-1 text-truncate-2 f-s-15 f-w-600">
                      {{ element.uname }}
                    </h6>
                    <span class="f-s-14 mat-body-1">{{ element.position }}</span>
                  </div>
                </div>
              </td>
            </ng-container>

            <!-- Hour Rate Column -->
            <ng-container matColumnDef="progress">
              <th mat-header-cell *matHeaderCellDef class="f-w-400 mat-subtitle-1 f-s-14">Progress</th>
              <td mat-cell *matCellDef="let element" class="mat-body-1">
                <span>
                  <span class="f-w-600 f-s-14">{{ element.hrate }}</span>
                  %
                </span>
              </td>
            </ng-container>

            <!-- Symbol Column -->
            <ng-container matColumnDef="priority">
              <th mat-header-cell *matHeaderCellDef class="f-w-400 mat-subtitle-1 f-s-14">Priority</th>
              <td mat-cell *matCellDef="let element">
                <span class="bg-light-{{ element.progress }} text-{{ element.progress }} rounded-sm f-w-600 p-6 p-y-4 f-s-12">
                  {{ element.priority | titlecase }}
                </span>
              </td>
            </ng-container>

            <!-- Weight Column -->
            <ng-container matColumnDef="budget">
              <th mat-header-cell *matHeaderCellDef class="f-w-400 mat-subtitle-1 f-s-14">Budget</th>
              <td mat-cell *matCellDef="let element">
                <span class="f-s-14">${{ element.skills }}k</span>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
        </div>
      </mat-tab>
    </mat-tab-group>
  </mat-card-content>
</mat-card>
