import { Injectable } from '@angular/core'
import Keycloak from 'keycloak-js'
import { BehaviorSubject, Observable } from 'rxjs'
import { inject } from '@angular/core'
import { KEYCLOAK_EVENT_SIGNAL, KeycloakEventType, typeEventArgs, ReadyArgs } from 'keycloak-angular'
import { effect } from '@angular/core'

/**
 * Service quản lý roles của người dùng từ Keycloak
 */
@Injectable({
  providedIn: 'root'
})
export class KeycloakRolesService {
  // State
  private _roles: string[] = []
  private _isAuthenticated: boolean = false
  
  // Observable để component có thể đăng ký theo dõi
  private rolesSubject = new BehaviorSubject<string[]>([])
  private authStateSubject = new BehaviorSubject<boolean>(false)

  // Keycloak instance và signal
  private readonly keycloak = inject(Keycloak)
  private readonly keycloakSignal = inject(KEYCLOAK_EVENT_SIGNAL)

  constructor() {
    // Lấy roles ban đầu nếu đã đăng nhập
    this.updateRoles()
    
    // <PERSON> dõi các sự kiện Keycloak
    effect(() => {
      const keycloakEvent = this.keycloakSignal()
      
      // Khi keycloak đã sẵn sàng
      if (keycloakEvent.type === KeycloakEventType.Ready) {
        this._isAuthenticated = typeEventArgs<ReadyArgs>(keycloakEvent.args)
        this.authStateSubject.next(this._isAuthenticated)
        if (this._isAuthenticated) {
          this.updateRoles()
        }
      }
      
      // Khi xác thực thành công
      if (keycloakEvent.type === KeycloakEventType.AuthSuccess) {
        this._isAuthenticated = true
        this.authStateSubject.next(this._isAuthenticated)
        this.updateRoles()
      }

      // Khi đăng xuất
      if (keycloakEvent.type === KeycloakEventType.AuthLogout) {
        this._isAuthenticated = false
        this._roles = []
        this.authStateSubject.next(this._isAuthenticated)
        this.rolesSubject.next(this._roles)
      }
    })
  }

  /**
   * Cập nhật danh sách roles từ token
   */
  updateRoles(): void {
    try {
      if (this.keycloak) {
        // Cập nhật trạng thái xác thực
        this._isAuthenticated = this.keycloak.authenticated || false
        this.authStateSubject.next(this._isAuthenticated)
        
        if (this._isAuthenticated) {
          // Mảng để lưu trữ tất cả roles
          const roles: string[] = []
          
          // Lấy thông tin từ token
          const tokenParsed = this.keycloak['tokenParsed']
          
          if (tokenParsed) {
            // Lấy realm roles
            const realmAccess = tokenParsed.realm_access
            if (realmAccess && realmAccess.roles) {
              roles.push(...realmAccess.roles)
            }
            
            // Lấy resource roles nếu có
            const resourceAccess = tokenParsed.resource_access
            if (resourceAccess) {
              // Thêm các resource roles vào mảng roles
              Object.values(resourceAccess).forEach((resource: any) => {
                if (resource.roles && Array.isArray(resource.roles)) {
                  roles.push(...resource.roles)
                }
              })
            }
          }
          
          // Cập nhật state
          this._roles = [...new Set(roles)] // Loại bỏ các roles trùng lặp
          this.rolesSubject.next(this._roles)
          console.log('Updated user roles:', this._roles)
        } else {
          this._roles = []
          this.rolesSubject.next(this._roles)
        }
      }
    } catch (error) {
      console.error('Error getting user roles:', error)
      this._roles = []
      this.rolesSubject.next(this._roles)
    }
  }

  /**
   * Kiểm tra xem người dùng có role được yêu cầu hay không
   * @param requiredRoles Danh sách roles được yêu cầu
   * @returns true nếu người dùng có ít nhất một trong các roles được yêu cầu, ngược lại trả về false
   */
  hasRequiredRoles(requiredRoles?: string[]): boolean {
    // Nếu không có roles được cấu hình, cho phép hiển thị
    if (!requiredRoles || !Array.isArray(requiredRoles) || requiredRoles.length === 0) {
      return true
    }

    // Nếu user không đăng nhập, không cho phép xem
    if (!this._isAuthenticated) {
      return false
    }

    // Kiểm tra nếu user có ít nhất một trong các role được yêu cầu
    return requiredRoles.some(role => this._roles.includes(role))
  }

  /**
   * Kiểm tra xem người dùng có role cụ thể hay không
   * @param role Role cần kiểm tra
   * @returns true nếu người dùng có role, ngược lại trả về false
   */
  hasRole(role: string): boolean {
    return this._isAuthenticated && this._roles.includes(role)
  }

  /**
   * Getter cho danh sách roles của người dùng
   */
  get roles(): string[] {
    return this._roles
  }

  /**
   * Observable cho danh sách roles
   */
  get roles$(): Observable<string[]> {
    return this.rolesSubject.asObservable()
  }

  /**
   * Getter cho trạng thái xác thực
   */
  get isAuthenticated(): boolean {
    return this._isAuthenticated
  }

  /**
   * Observable cho trạng thái xác thực
   */
  get isAuthenticated$(): Observable<boolean> {
    return this.authStateSubject.asObservable()
  }
}
