import { AfterViewInit, Component, ElementRef, EventEmitter, Injector, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core'
import { FormGroupAbstractComponent } from '../form-group.abstract.component'
import { TranslateModule } from '@ngx-translate/core'
import { MatFormFieldModule } from '@angular/material/form-field'
import { MatDatepickerModule } from '@angular/material/datepicker'
import { MatInputModule } from '@angular/material/input'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MatIconModule } from '@angular/material/icon'
import { ExtendedModule } from '@angular/flex-layout/extended'
import { NgClass, NgIf } from '@angular/common'
import { DaterangepickerDirective, LocaleConfig, NgxDaterangepickerMd } from 'ngx-daterangepicker-material'
import dayjs from 'dayjs/esm'
import { MatIconButton } from '@angular/material/button'
import { MatTooltip } from '@angular/material/tooltip'
import { DateRanges } from 'node_modules/ngx-daterangepicker-material/daterangepicker.component'
import { TablerIconComponent } from 'angular-tabler-icons'
import { ButtonClearComponent } from '@shared/components/data-input/shared/button-clear/button-clear.component'

@Component({
  selector: 'app-date-range-control',
  templateUrl: './date-range-control.component.html',
  styleUrls: ['./date-range-control.component.scss'],
  standalone: true,
  imports: [
    NgClass,
    ExtendedModule,
    NgIf,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    MatDatepickerModule,
    MatFormFieldModule,
    TranslateModule,
    NgxDaterangepickerMd,
    MatIconButton,
    MatTooltip,
    TablerIconComponent,
    ButtonClearComponent
  ]
})
export class DateRangeControlComponent extends FormGroupAbstractComponent implements OnInit, AfterViewInit, OnChanges {
  // minDate: moment.Moment
  // maxDate: moment.Moment
  @ViewChild('inputElementDateRange', { static: true }) inputElement: ElementRef
  @ViewChild(DaterangepickerDirective, { static: true }) pickerDirective: DaterangepickerDirective
  @Output() onClearAllFilter = new EventEmitter<any>()
  isShowClear = false
  isPickerOpening = false

  options = {
    autoApply: true,
    alwaysShowCalendars: false,
    showCancel: false,
    showClearButton: false,
    linkedCalendars: true,
    singleDatePicker: false,
    showWeekNumbers: false,
    showISOWeekNumbers: false,
    customRangeDirection: false,
    lockStartDate: false,
    closeOnAutoApply: false,
    showCustomRangeLabel: false
  }

  minDate: dayjs.Dayjs = dayjs().subtract(48, 'month') //TODO
  maxDate: dayjs.Dayjs = dayjs().add(48, 'month') // TODO
  locale: LocaleConfig = {
    format: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
    displayFormat: 'DD/MM/YYYY',
    separator: ' - ',
    cancelLabel: 'Cancel',
    applyLabel: 'Okay',
    customRangeLabel: 'Tùy chọn'
  }

  opens: string
  drops: string
  timePicker: boolean
  dateLimit: number
  errorDateRange: boolean
  ranges: DateRanges = {}
  invalidDates: dayjs.Dayjs[] = []
  start: dayjs.Dayjs
  isInvalidDate = (m: dayjs.Dayjs): boolean => {
    if (this.dateLimit) {
      return m.subtract(this.dateLimit, 'day').isAfter(this.start)
    }
    return false
  }

  constructor(protected override injector: Injector) {
    super(injector)
    this.timePicker = false
    this.options.closeOnAutoApply = !this.timePicker
    this.opens = 'right'
    this.drops = 'down'
  }

  override ngOnInit() {
    this.f[this.item.key].valueChanges.subscribe((values) => {
      // console.log(values)
    })
  }

  override ngAfterViewInit() {
    if (this.item.focus) {
      setTimeout(() => {
        // this.inputElement.nativeElement.focus()
      }, 100)
    }

    // console.log('date-range', this.form.get(this.item.key).patchValue({ start: dayjs().subtract(3, 'days'), end: dayjs().add(3, 'days') }))
  }

  override ngOnChanges(changes: SimpleChanges) {
    super.ngOnChanges(changes)
    console.log('DateRangeControlComponent', changes)
    if (changes?.item?.currentValue) {
      const currentItem = changes.item.currentValue

      if (currentItem?.displayFormat) {
        this.locale.displayFormat = currentItem.displayFormat
      }

      if (currentItem?.format) {
        this.locale.format = currentItem.format
      }

      if (currentItem?.dateLimit !== undefined) {
        this.dateLimit = currentItem.dateLimit
      }

      if (currentItem?.separator) {
        this.locale.separator = currentItem.separator
      }

      if (currentItem?.cancelLabel) {
        this.locale.cancelLabel = currentItem.cancelLabel
      }

      if (currentItem?.applyLabel) {
        this.locale.applyLabel = currentItem.applyLabel
      }

      if (currentItem?.autoApply !== undefined) {
        this.options.autoApply = currentItem.autoApply
      }

      if (currentItem?.showDateRange !== undefined && Boolean(currentItem.showDateRange)) {
        this.options.showCustomRangeLabel = Boolean(currentItem.showDateRange)
        this.ranges = {
          ['Hôm nay']: [dayjs().startOf('day'), dayjs().endOf('day')],
          ['Hôm qua']: [dayjs().subtract(1, 'days').startOf('day'), dayjs().subtract(1, 'days').endOf('day')],
          ['7 Ngày trước']: [dayjs().subtract(6, 'days').startOf('day'), dayjs().endOf('day')],
          ['30 Ngày trước']: [dayjs().subtract(29, 'days').startOf('day'), dayjs().endOf('day')],
          ['Trong tháng']: [dayjs().startOf('month').startOf('day'), dayjs().endOf('month').endOf('day')],
          ['Tháng trước']: [dayjs().subtract(1, 'month').startOf('month').startOf('day'), dayjs().subtract(1, 'month').endOf('month').endOf('day')]
          // ['Last 3 Month']: [dayjs().subtract(3, 'month').startOf('month').startOf('day'), dayjs().subtract(1, 'month').endOf('month').endOf('day')]
        }
      }

      if (currentItem?.showCancel !== undefined) {
        this.options.showCancel = currentItem.showCancel
      }

      if (currentItem?.showClearButton !== undefined) {
        this.options.showClearButton = currentItem.showClearButton
      }

      if (currentItem?.linkedCalendars !== undefined) {
        this.options.linkedCalendars = currentItem.linkedCalendars
      }

      if (currentItem?.singleDatePicker !== undefined) {
        this.options.singleDatePicker = Boolean(currentItem.singleDatePicker)
      }

      if (currentItem?.showWeekNumbers !== undefined) {
        this.options.showWeekNumbers = currentItem.showWeekNumbers
      }

      if (currentItem?.showISOWeekNumbers !== undefined) {
        this.options.showISOWeekNumbers = Boolean(currentItem.showISOWeekNumbers)
      }

      if (currentItem?.lockStartDate !== undefined) {
        this.options.lockStartDate = Boolean(currentItem.lockStartDate)
      }

      if (currentItem?.timePicker !== undefined) {
        this.timePicker = Boolean(currentItem?.timePicker)
        this.options.closeOnAutoApply = !this.timePicker
      }
    }
  }

  openPicker(e: any) {
    if (this.isPickerOpening) {
      return // Đã có một lần mở picker đang được thực hiện
    }

    this.isPickerOpening = true // Đánh dấu rằng picker đang được mở

    setTimeout(() => {
      this.pickerDirective.open(e)
      this.isPickerOpening = false // Đặt lại cờ sau khi picker đã mở
    }, 100)
  }

  eventClicked(e: any): void {
    // eslint-disable-next-line no-console
    console.log({ ['eventClicked()']: e })
    if (e?.startDate) {
      this.start = e?.startDate
      // console.log('startDate', this.start)
    }
    // if (e?.endDate)
    // {
    //   const endDayjs = e.endDate.endOf('day')
    //   console.log(endDayjs)
    //   // this.f[this.item.key].patchValue({
    //   //   end: endDayjs
    //   // }, { emitEvent: false });
    // }
    // Tính khoảng cách giữa start và end
    this.isShowClear = true
    // this.cdRef.detectChanges()
    this.emitOnChanged({ e })
  }

  startDateChanged(e: any) {
    // if(e?.endDate)
    // {
    //   this.f[this.item.key].patchValue({
    //     end: dayjs(e.endDate).endOf('day')
    //   })
    // }

    console.log({ ['eventClicked()']: e })
    this.isShowClear = true
    // this.cdRef.detectChanges()
    this.emitOnChanged({ e })
  }

  endDateChanged(e: any) {
    // if(e?.endDate)
    // {
    //   this.f[this.item.key].patchValue({
    //     end: dayjs(e.endDate).endOf('day')
    //   }, { emitEvent: false })
    // }

    console.log({ ['eventClicked()']: e })
    console.log(this.f[this.item.key].value)
    this.isShowClear = true
    // this.cdRef.detectChanges()
    this.emitOnChanged({ e })
  }

  eventCleared(): void {
    // eslint-disable-next-line no-console
    this.isShowClear = false
    this.cdRef.detectChanges()
    console.log('datepicker cleared')
  }

  onClearText() {
    this.form.get(this.item.key)?.patchValue('')
    this.isShowClear = false
    this.onClearAllFilter.emit(true)
    this.cdRef.detectChanges()
  }

  preventFocus(event: FocusEvent) {
    event.preventDefault()
    ;(event.target as HTMLInputElement).blur()
  }
}
