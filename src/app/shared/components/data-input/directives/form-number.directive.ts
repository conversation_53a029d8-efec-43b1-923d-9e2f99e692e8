import { Directive, ElementRef, HostListener, Input, OnChanges, OnInit } from '@angular/core'
import { NgControl } from '@angular/forms'
import { D_NUMBER } from '@shared'

/**
 * nhập liệu number
 * @description directive common for number input
 */

@Directive({
  selector: 'input[formItem], textarea[formItem]',
  standalone: true
})
export class FormNumberDirective implements OnChanges, OnInit {
  @Input() formItem: any

  constructor(
    private _el: ElementRef,
    private control: NgControl
  ) {}

  ngOnInit() {
    // console.log('formItem:', this.formItem);
  }

  ngOnChanges() {
    // console.log('formItem:', this.formItem);
  }

  @HostListener('paste', ['$event']) onPaste(event: KeyboardEvent) {}

  @HostListener('input', ['$event']) onInputChange(event: KeyboardEvent) {
    if (this.formItem === D_NUMBER) {
      if (!event) {
        return
      }

      let initialValue = this._el.nativeElement.value

      // Chỉ cho phép các ký tự số, loại bỏ tất cả các ký tự khác
      this._el.nativeElement.value = this._el.nativeElement.value.replace(/[^0-9]*/g, '')

      // Nếu giá trị thay đổi, dừng sự kiện để ngăn các trigger khác
      if (initialValue !== this._el.nativeElement.value) {
        event.stopPropagation()
      }
    }
  }
}
