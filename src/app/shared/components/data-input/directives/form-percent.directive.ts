import { Directive, ElementRef, HostListener, Input, OnChanges, OnInit } from '@angular/core';
import { NgControl } from '@angular/forms';
import { D_PERCENT } from '@shared/constants';

/**
 * nhập liệu phần trăm
 * @description directive common for percent input, không cho nhập quá 100
 */
@Directive({
  selector: 'input[formItem]',
  standalone: true
})
export class FormPercentDirective implements OnChanges, OnInit {
  @Input() formItem: any;

  constructor(
    private _el: ElementRef,
    private control: NgControl
  ) {}

  ngOnInit() {
    // console.log('formPercent:', this.formPercent);
  }

  ngOnChanges() {
    // console.log('formPercent:', this.formPercent);
  }

  @HostListener('paste', ['$event']) onPaste(event: KeyboardEvent) {}

  @HostListener('input', ['$event']) onInputChange(event: KeyboardEvent) {  
    if (this.formItem === D_PERCENT) {    
      if (!event) {
        return;
      }
      let initialValue = this._el.nativeElement.value;
      // Cho phép số, dấu chấm và dấu phẩy
      this._el.nativeElement.value = initialValue.replace(/[^0-9.,]/g, '');
      // Thay dấu phẩy thành dấu chấm để chuẩn hóa
      this._el.nativeElement.value = this._el.nativeElement.value.replace(',', '.');
      // Chỉ cho phép một dấu phân cách thập phân
      const parts = this._el.nativeElement.value.split('.');
      if (parts.length > 2) {
        this._el.nativeElement.value = parts[0] + '.' + parts.slice(1).join('');
      }
      // Giới hạn tối đa 100
      let value = this._el.nativeElement.value;
      // Nếu giá trị không kết thúc bằng dấu chấm thì mới parseFloat
      if (value !== '' && !/\.$/.test(value)) {
        let num = parseFloat(value);
        if (!isNaN(num) && num > 100) {
          num = 100;
        }
        if (isNaN(num)) {
          this._el.nativeElement.value = '';
        } else {
          this._el.nativeElement.value = num.toString();
        }
      } else {
        // Nếu kết thúc bằng dấu chấm, giữ nguyên giá trị nhập
        this._el.nativeElement.value = value;
      }
      if (initialValue !== this._el.nativeElement.value) {
        event.stopPropagation();
      }
    }
  }
}
