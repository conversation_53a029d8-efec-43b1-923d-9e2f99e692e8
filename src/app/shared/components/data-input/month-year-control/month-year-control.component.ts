import { AfterViewInit, Component, Injector, OnInit, ViewChild } from '@angular/core'
import moment, { Moment } from 'moment'
import { FormGroupAbstractComponent } from '../form-group.abstract.component'
import { TranslateModule } from '@ngx-translate/core'
import { MatFormFieldModule } from '@angular/material/form-field'
import { Mat<PERSON>ate<PERSON>ker, MatDatepickerInputEvent, MatDatepickerModule } from '@angular/material/datepicker'
import { MatInputModule } from '@angular/material/input'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MatIconModule } from '@angular/material/icon'
import { ExtendedModule } from '@angular/flex-layout/extended'
import { NgClass, NgIf } from '@angular/common'
import { MatTooltip } from '@angular/material/tooltip'
import { MatIconButton } from '@angular/material/button'
import { TablerIconComponent } from 'angular-tabler-icons'

@Component({
  selector: 'app-month-year-control',
  templateUrl: './month-year-control.component.html',
  styleUrls: ['./month-year-control.component.scss'],
  standalone: true,
  imports: [
    NgClass,
    ExtendedModule,
    NgIf,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    MatDatepickerModule,
    MatFormFieldModule,
    TranslateModule,
    MatTooltip,
    TablerIconComponent
  ]
})
export class MonthYearControlComponent extends FormGroupAbstractComponent implements OnInit, AfterViewInit {
  minDate: moment.Moment
  maxDate: moment.Moment
  @ViewChild('inputElement') inputElement: any

  constructor(protected override injector: Injector) {
    super(injector)
  }

  override ngOnInit() {
    // this.minDate = this.item.minDate ? moment(this.item.minDate) : moment(new Date().setDate(new Date().getDate()))
    // this.maxDate = this.item.maxDate ? moment(this.item.maxDate) : moment(new Date().setDate(new Date().getDate() + 365 * 100))
    //TODO: truong hop edit thi ko fire event nen bo
    // this.form.valueChanges.subscribe((value) => {
    //   this.emitOnChanged(value)
    // })
  }

  override ngAfterViewInit() {
    if (this.item.focus) {
      setTimeout(() => {
        this.inputElement.nativeElement.focus()
      }, 100)
    }
  }

  onDateChange(event: MatDatepickerInputEvent<Date>): void {
    this.emitOnChanged(event.value)
  }

  onDateInput(event: MatDatepickerInputEvent<Date>): void {
    this.emitOnChanged(event.value)
  }

  setMonthAndYear(normalizedMonthAndYear: Moment, datepicker: MatDatepicker<Moment>) {
    const ctrlValue = moment.isMoment(this.form.get(this.item.key).value) ? this.form.get(this.item.key).value : moment()
    ctrlValue.month(normalizedMonthAndYear.month())
    ctrlValue.year(normalizedMonthAndYear.year())
    this.form.get(this.item.key).setValue(ctrlValue)
    datepicker.close()
  }
}
