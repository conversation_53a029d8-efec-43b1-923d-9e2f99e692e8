import { AfterViewInit, Component, ElementRef, EventEmitter, forwardRef, Injector, Input, isDevMode, Output, ViewChild } from '@angular/core'
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms'
import { FormGroupAbstractComponent } from '../form-group.abstract.component'
import { TranslateModule } from '@ngx-translate/core'
import { MatTooltipModule } from '@angular/material/tooltip'
import { FlexModule } from '@angular/flex-layout/flex'
import { FormMicroDirective } from '../directives/form-micro.directive'
import { FormCurrencyDirective } from '../directives/form-currency.directive'
import { FormTextDirective } from '../directives/form-text.directive'
import { FormNumberPhoneDirective } from '../directives/form-number-phone.directive'
import { MatInputModule } from '@angular/material/input'
import { MatIconModule } from '@angular/material/icon'
import { ExtendedModule } from '@angular/flex-layout/extended'
import { NgClass, NgIf } from '@angular/common'
import { MatIconButton } from '@angular/material/button'
import { keydownEvent } from '@shared/components/data-input/directives/keydown.directive'
import _ from 'lodash'

@Component({
  selector: 'app-text-float-control',
  templateUrl: './text-float-control.component.html',
  styleUrls: ['./text-float-control.component.scss'],
  standalone: true,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TextFloatControlComponent),
      multi: true
    }
  ],
  imports: [
    NgIf,
    NgClass,
    ExtendedModule,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    FormNumberPhoneDirective,
    FormTextDirective,
    FormCurrencyDirective,
    FormMicroDirective,
    FlexModule,
    MatTooltipModule,
    TranslateModule,
    MatIconButton,
    keydownEvent
  ]
})
export class TextFloatControlComponent extends FormGroupAbstractComponent implements AfterViewInit, ControlValueAccessor {
  @Input() typeForm = 'vertical'
  @Input() classes = ''
  @Output() focusOutEvent = new EventEmitter()
  @Output() focusEvent = new EventEmitter()
  @Output() onRightClickButton = new EventEmitter()
  @Output() onLeftClickButton = new EventEmitter()
  @Output() onClearAllFilter = new EventEmitter<any>()
  @ViewChild('inputElement') inputElement!: ElementRef
  @Input() tooltipMessage
  @Input() leftIcon = ''
  @Input() leftIconClasses = ''
  @Input() rightIcon = ''
  @Input() leftIconPath: string
  @Input() rightIconPath: string
  phoneNumber = ''
  isShowClear = false
  inputFocused = false
  inputNotEmpty = false
  /**
   * tên class icon
   * example: ic-search
   */
  @Input() iconRight = ''

  constructor(protected override injector: Injector) {
    super(injector)
    this.form.valueChanges.subscribe((value) => {
      isDevMode() && console.log(value)
    })
  }

  override ngAfterViewInit() {
    if (this.item.focus) {
      setTimeout(() => {
        this.inputElement.nativeElement.focus()
      }, 100)
    }
  }

  override ngOnChanges(changes: any) {
    super.ngOnChanges(changes)
    if (changes?.item?.currentValue.value) {
      this.inputNotEmpty = !_.isEmpty(changes?.item?.currentValue?.value)
    }
  }

  /**
   * Event out focus
   * @param $event
   */
  focusOutFunction($event: any) {
    this.inputFocused = false
    this.focusOutEvent.emit($event)
    this.onTouched()
  }

  /**
   * Event focus
   * @param $event
   */
  focusFunction($event: any) {
    if (this.item.readOnly) return
    this.inputFocused = true
    this.focusEvent.emit($event)
    this.onTouched()
  }

  change($event: any) {
    this.inputNotEmpty = $event.target.value !== ''
    this.emitOnChanged($event)
    this.searchSubject.next($event?.target?.value)
    this.isShowClear = $event?.target?.value?.length > 0
    this.onChange($event.target.value)
  }

  onClearText() {
    this.inputNotEmpty = false
    this.form.get(this.item.key)?.patchValue('')
    this.isShowClear = false
    this.onClearAllFilter.emit(true)
    this.onChange('')
  }

  onKeyDownPress($event: any) {
    if (this.item.clearable) {
      this.isShowClear = this.form.get(this.item.key).value?.length > 0
    }
  }

  private onChange: any = () => {}
  private onTouched: any = () => {}

  writeValue(value: any): void {
    this.form.get(this.item.key)?.setValue(value)
  }

  registerOnChange(fn: any): void {
    this.onChange = fn
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn
  }

  setDisabledState?(isDisabled: boolean): void {
    if (isDisabled) {
      this.form.disable()
    } else {
      this.form.enable()
    }
  }

  onLeftClickButtonHandler() {
    this.onLeftClickButton.emit(true)
  }
}
