<div class="p-x-6" [ngClass]="{ 'control-error': isValidControl(item.key) }">
  <label *ngIf="item.label" class="form-label" [class.has-label]="item?.label?.length">
    {{ item.label | translate }}
    <!--    <mat-icon-->
    <!--      *ngIf="isValidControl(item.key)"-->
    <!--      class="ic-error sm text-error mrl-1"></mat-icon>-->
    <span *ngIf="item.required && item.label" class="text-error mrl-1">*</span>
  </label>
  <!-- CASE TEXTBOX -->
  <div
    class="form-group {{ formGroupClass }}"
    [formGroup]="form"
    [class.error]="isValidControl(item.key) || isValidCustom(item.key, item.customValidate)?.key">
    <div class="form-input-row">
      <mat-form-field appearance="outline" class="w-100">
        <input
          #inputElement
          class="form-control input-date"
          matInput
          [matDatepicker]="picker"
          autocomplete="false"
          [max]="maxDate"
          [min]="minDate"
          [readonly]="item.readOnly"
          [formControlName]="item.key"
          (dateInput)="onDateInput($event)"
          (dateChange)="onDateChange($event)"
          [placeholder]="item.placeholder | translate" />
        <mat-datepicker-toggle matIconSuffix [for]="picker" [matTooltip]="item.tooltipMessage">
          <i-tabler matDatepickerToggleIcon class="mrr-0 pdb-1" name="calendar"></i-tabler>
        </mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
      </mat-form-field>
    </div>
    <div *ngIf="isValidControlDate(item.key, 'required')" class="form-remark">
      {{ 'validations.required' | translate }}
    </div>
    <div *ngIf="isValidControlDate(item.key, 'max')" class="form-remark">
      {{ 'validations.maxlength' | translate: { number: item.max } }}
    </div>
    <div *ngIf="isValidControlDate(item.key, 'min')" class="form-remark">
      {{ 'validations.minlength' | translate: { number: item.min } }}
    </div>
    <div *ngIf="isValidControlDate(item.key, 'matDatepickerMin')" class="form-remark">
      {{ 'validations.min' | translate: { number: minDate?.format('DD/MM/YYYY') } }}
    </div>
    <div *ngIf="isValidControlDate(item.key, 'matDatepickerParse')" class="form-remark">
      {{ 'validations.errortype' | translate }}
    </div>
    <div *ngIf="isValidCustom(item.key, item.customValidate)?.key" class="form-remark">
      {{ isValidCustom(item.key, item.customValidate)?.value | translate }}
    </div>
    <div *ngIf="isValidControlDate(item.key, 'matDatepickerMax')" class="form-remark">
      {{ 'validations.max' | translate: { number: maxDate?.format('DD/MM/YYYY') } }}
    </div>
  </div>
</div>
