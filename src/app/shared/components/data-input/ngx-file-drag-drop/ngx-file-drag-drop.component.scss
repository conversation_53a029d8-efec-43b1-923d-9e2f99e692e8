input {
  width: 0;
  height: 0px;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  z-index: -1;
}

:host {
  display: block;
  min-height: 50px;
  margin: 10px auto;
  width: 100%;
  cursor: pointer;
  border-radius: 5px;
  border: 1px dashed #e2e2e2;
  background: #f4f6fa;
  padding: 10px;
}
/* :host:active, */
/* :host.drag-over {
  border-color: purple;
} */
:host.disabled {
  opacity: 0.5;
  cursor: unset;
}

.title-placeholder {
  color: #081735;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
}
.content-placeholder {
  color: #9aa1bc;
  font-size: 12px;
  margin-top: 10px;
  font-style: normal;
}
.placeholder {
  /* opacity: 0.9; */
  color: grey;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  text-align: center;
  margin-top: 10px;
}
.icon-upload {
  font-size: 38px;
  //width: 40px;
  //height: 40px;
}

mat-chip {
  max-width: 100%;
}
.filename {
  max-width: calc(100% - 2em);
  white-space: normal;
  word-break: break-word;
  font-size: 14px;
  display: flex;
  align-items: center;
}

:host.empty-input {
  display: flex;
  align-items: center;
  justify-content: center;
}

.mat-mdc-chip.mat-mdc-standard-chip.mat-focus-indicator {
  /* transition: none !important; */
  box-shadow: none;
}

.mat-mdc-chip.mat-mdc-standard-chip::after {
  background: unset;
}

/* :host.ng-invalid.ng-touched {
  border-color: red !important;
} */
