<ng-container *ngIf="files.length > 0 && !turnOffPreView">
  <div
    class="d-flex flex-row align-items-center flex-wrap justify-content-center"
    [matTooltip]="file.size | byte"
    matTooltipPosition="below"
    [matTooltipDisabled]="displayFileSize"
    *ngFor="let file of files">
    <div class="filename fc-primary">
      <mat-icon class="ic-report mrl-2"></mat-icon>
      {{ getFileName(file) }} | {{ file.size | byte }}
    </div>
    <button mat-icon-button *ngIf="!disabled" (click)="removeFile(file)" class="d-flex" aria-label="delete icon">
      <i-tabler class="icon-20" name="trash"></i-tabler>
    </button>
  </div>
</ng-container>
<div *ngIf="!(files.length > 0 && !turnOffPreView)" class="d-flex flex-row align-items-center justify-content-center" (click)="open()">
  <mat-icon class="icon-upload fc-disabled mrr-4 ic-upload-cloud"></mat-icon>
  <div class="d-flex flex-column align-items-center">
    <div class="title-placeholder">Kéo & thả tệp excel cần để import dữ liệu</div>
    <div class="content-placeholder" [innerHTML]="emptyPlaceholder"></div>
    <!--    <div class="placeholder">-->
    <!--      <div [innerHTML]="emptyPlaceholder"></div>-->
    <!--    </div>-->
  </div>
</div>
<input #fileInputEl id="fileInputEl" class="hidden" #fileInput type="file" [attr.multiple]="multiple ? '' : null" [attr.accept]="accept" />
