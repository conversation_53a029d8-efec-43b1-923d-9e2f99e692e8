import { AfterViewInit, Component, ElementRef, EventEmitter, Injector, Input, isDevMode, Output, ViewChild } from '@angular/core'
import { FormGroupAbstractComponent } from '../form-group.abstract.component'
import { TranslateModule } from '@ngx-translate/core'
import { MatTooltipModule } from '@angular/material/tooltip'
import { FlexModule } from '@angular/flex-layout/flex'
import { MatInputModule } from '@angular/material/input'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MatIconModule } from '@angular/material/icon'
import { ExtendedModule } from '@angular/flex-layout/extended'
import { NgClass, NgForOf, NgIf } from '@angular/common'
import { MatIconButton } from '@angular/material/button'

@Component({
  selector: 'app-rating-control',
  templateUrl: './rating-control.component.html',
  styleUrls: ['./rating-control.component.scss'],
  standalone: true,
  imports: [
    NgIf,
    NgClass,
    ExtendedModule,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    FlexModule,
    MatTooltipModule,
    TranslateModule,
    MatIconButton,
    NgForOf
  ]
})
export class RatingControlComponent extends FormGroupAbstractComponent implements AfterViewInit {
  @Input() typeForm = 'vertical'
  @Input() classes = ''
  @Output() onRightClickButton = new EventEmitter()
  @Output() onClearAllFilter = new EventEmitter<any>()
  @ViewChild('inputElement') inputElement!: ElementRef
  @Input() tooltipMessage

  @Input('rating') private rating: number = 0
  @Input('starCount') private starCount: number = 5
  @Input('color') protected color: string = 'primary'
  @Output() private ratingUpdated = new EventEmitter()

  ratingArr = []

  /**
   * tên class icon
   * example: ic-search
   */
  @Input() iconRight = ''

  constructor(protected override injector: Injector) {
    super(injector)
    this.form.valueChanges.subscribe((value) => {
      //TODO: not work
      isDevMode() && console.log(value)
    })
  }

  override ngOnInit() {
    super.ngOnInit()
    console.log('a ' + this.starCount)
    for (let index = 0; index < this.starCount; index++) {
      this.ratingArr.push(index)
    }
  }
  onClick(rating: number) {
    this.rating = rating
    this.ratingUpdated.emit(rating)
    this.form.get(this.item.key).patchValue(String(rating))
    this.emitOnChanged(rating)
    return false
  }

  showIcon(index: number) {
    if (this.rating >= index + 1) {
      return 'star'
    } else {
      return 'star_border'
    }
  }

  override ngAfterViewInit() {
    if (this.item.focus) {
      setTimeout(() => {
        this.inputElement.nativeElement.focus()
      }, 100)
    }
  }

  override ngOnChanges(changes: any) {
    super.ngOnChanges(changes)
    this.rating = Number(this.form.get(this.item.key).value)
    // isDevMode() && console.log('RatingControlComponent', changes)
    // // console.log('RatingControlComponent', this.form)
    // if (!changes?.item?.firstChange) {
    //   this.form.get(this.item.key).patchValue(changes?.item?.currentValue.value)
    // }
  }

  change($event: any) {
    this.emitOnChanged($event)
    this.searchSubject.next($event?.target?.value)
  }
}
