import { After<PERSON><PERSON>ntInit, AfterViewInit, Component, Injector, isDevMode, OnInit } from '@angular/core'
import { distinctUntilChanged } from 'rxjs/operators'
import { FormGroupAbstractComponent } from '../form-group.abstract.component'
import { TranslateModule } from '@ngx-translate/core'
import { MatCheckboxModule } from '@angular/material/checkbox'
import { FlexModule } from '@angular/flex-layout/flex'
import { MatIconModule } from '@angular/material/icon'
import { ExtendedModule } from '@angular/flex-layout/extended'
import { NgClass, NgFor, NgIf } from '@angular/common'
import _ from 'lodash'
import { EVENT_FORM_CONTROL } from '@shared'

@Component({
  selector: 'app-list-checkbox-control',
  templateUrl: './list-checkbox-control.component.html',
  styleUrls: ['./list-checkbox-control.component.scss'],
  standalone: true,
  imports: [Ng<PERSON>lass, ExtendedModule, NgI<PERSON>, MatIconModule, FlexModule, Ng<PERSON>or, MatCheckboxModule, TranslateModule]
})
export class ListCheckboxControlComponent extends FormGroupAbstractComponent implements OnInit, AfterViewInit, AfterContentInit {
  constructor(protected override injector: Injector) {
    super(injector)
  }

  override ngOnInit(): void {
    this.form
      .get(this.item.key)
      ?.valueChanges.pipe(distinctUntilChanged())
      .subscribe((val) => {
        this.item.options.map((element: any) => {
          element.checked = this.checkData(element[this.item?.checkBoxKey || 'key'], val)
          return element
        })
      })
  }

  override ngOnChanges(changes: any) {
    super.ngOnChanges(changes)
    isDevMode() && console.log('changes CheckboxControlComponent 1', changes)
    console.log('check-box-control', this.form)
    //Case patch form
    if (
      changes?.form?.firstChange ||
      (!changes?.form?.firstChange &&
        !_.isEqual(changes?.form?.currentValue?.get(this.item.key).value, changes?.form?.previousValue?.get(this.item.key).value))
    ) {
      isDevMode() && console.log('changes CheckboxControlComponent 2', changes)
      this.item.options.map((element: any) => {
        element.checked = this.checkData(element[this.item?.checkBoxKey || 'key'], changes.form.currentValue.get(this.item.key).value)
        return element
      })
    }
    //case patch item builder
    if (
      changes?.item?.firstChange ||
      (!changes?.item?.firstChange && !_.isEqual(changes?.item?.currentValue?.value, changes?.item?.previousValue?.value))
    ) {
      isDevMode() && console.log('changes CheckboxControlComponent 3', changes)
      this.item.options.map((element: any) => {
        element.checked = this.checkData(element[this.item?.checkBoxKey || 'key'], changes.item.currentValue.value)
        return element
      })
    }
  }

  override ngAfterViewInit() {
    console.log('ngAfterViewInit', new Date())
    this.emitOnChanged(EVENT_FORM_CONTROL.CREATE_CONTROL)
  }

  onCheckboxChange(e: any) {
    const value = this.form.get(this.item.key)?.value
    let checked = value && value != '' ? value.split(',') : []
    if (e.checked) {
      checked.push(e.source.value)
    } else {
      checked = checked.filter((res: any) => res != e.source.value)
    }
    this.form.get(this.item.key)?.patchValue(checked.toString())
    this.onChanged.emit(this.form.get(this.item.key).value)
  }

  checkData(key: any, checkboxValue: any) {
    const checked = checkboxValue && checkboxValue != '' ? checkboxValue.split(',') : []
    return checked.indexOf(key.toString()) >= 0
  }
}
