<div class="p-x-6" [ngClass]="{ 'control-error': isValidControl(item.key) }">
  <label class="form-label" [class.has-label]="item?.label?.length">
    {{ item.label | translate }}
    <!--    <mat-icon-->
    <!--      *ngIf="isValidControl(item.key) && item.label"-->
    <!--      class="ic-error sm text-error mrl-1"></mat-icon>-->
    <span *ngIf="item.required && item.label" class="text-error mrl-1">*</span>
  </label>
  <div [attr.fxLayout]="item.layout" [class.no-label]="item.hideValueCheckBox" *ngIf="item.options?.length">
    <mat-checkbox
      fxFlex="100"
      color="primary"
      *ngFor="let control of item.options; let i = index"
      [checked]="control.checked"
      [value]="control[item.checkBoxKey || 'value']"
      [disabled]="item.readOnly ?? item.type === 'disabled'"
      (change)="onCheckboxChange($event)">
      {{ !item.hideValueCheckBox ? control.value : '' }}
    </mat-checkbox>
  </div>
  <div class="form-group" [class.error]="isValidControl(item.key)">
    <div *ngIf="isValidControl(item.key, 'required')" class="form-remark">{{ item.label }} không được để trống</div>
  </div>
</div>
