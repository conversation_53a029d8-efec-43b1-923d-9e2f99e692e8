<div class="p-x-6" [ngClass]="{ 'control-error': isValidControl(item.key) }">
  <label *ngIf="item.label" class="form-label" [class.has-label]="item?.label?.length" style="display: flex">
    {{ item.label | translate }}
    <span *ngIf="item.required && item.label" class="text-error mrl-1">*</span>
    <i-tabler
      *ngIf="item.tooltipMessage"
      matTooltip="{{ item.tooltipMessage }}"
      [matTooltipPosition]="'above'"
      matTooltipShowDelay="0"
      name="info-circle"
      class="icon-18 cursor-pointer mrl-1 fc-gray-500"></i-tabler>
  </label>
  <!-- CASE TEXTBOX -->
  <div
    class="form-group {{ formGroupClass }}"
    [formGroup]="form"
    [class.error]="isValidControl(item.key) || isValidCustom(item.key, item.customValidate)?.key || errorDateRange">
    <div class="form-input-row">
      <mat-form-field appearance="outline" class="w-100">
        <input
          matInput
          #inputElementDateRange
          class="form-control input-date"
          ngxDaterangepickerMd
          [autoApply]="options.autoApply"
          [linkedCalendars]="options.linkedCalendars"
          [singleDatePicker]="options.singleDatePicker"
          [locale]="locale"
          [showDropdowns]="true"
          [ranges]="ranges"
          [alwaysShowCalendars]="true"
          [showCustomRangeLabel]="options.showCustomRangeLabel"
          startKey="start"
          endKey="end"
          [minDate]="minDate"
          [maxDate]="maxDate"
          [showWeekNumbers]="options.showWeekNumbers"
          [showCancel]="options.showCancel"
          [showClearButton]="options.showClearButton"
          [showISOWeekNumbers]="options.showISOWeekNumbers"
          [customRangeDirection]="options.customRangeDirection"
          [lockStartDate]="options.lockStartDate"
          [closeOnAutoApply]="options.closeOnAutoApply"
          firstMonthDayClass="first-day"
          lastMonthDayClass="last-day"
          emptyWeekRowClass="empty-week-row"
          emptyWeekColumnClass="empty-week-column"
          lastDayOfPreviousMonthClass="last-previous-day"
          firstDayOfNextMonthClass="first-next-day"
          [opens]="opens"
          [drops]="drops"
          [timePicker]="timePicker"
          [timePicker24Hour]="true"
          [timePickerSeconds]="false"
          [isInvalidDate]="isInvalidDate"
          name="daterange"
          (focus)="preventFocus($event)"
          (startDateChanged)="eventClicked($event)"
          (endDateChanged)="eventClicked($event)"
          (clearClicked)="eventCleared()"
          [readonly]="item.readOnly"
          [formControlName]="item.key"
          [placeholder]="item.placeholder | translate" />
        <ng-container matSuffix>
          <!-- <mat-icon class="md ic-close_blue" matSuffix #picker></mat-icon> -->
          <ng-container *ngIf="f[item.key].value && isShowClear && this.item.clearable">
            <app-button-clear (buttonClick)="onClearText()"></app-button-clear>
          </ng-container>
          <button type="button" class="button-picker" (click)="openPicker($event)" mat-icon-button>
            <i-tabler class="mrr-0 pdb-1" name="calendar"></i-tabler>
          </button>
        </ng-container>
      </mat-form-field>
    </div>
    <div *ngIf="isValidControlDate(item.key, 'required')" class="form-remark">
      {{ 'validations.required' | translate }}
    </div>
    <div *ngIf="isValidControlDate(item.key, 'max')" class="form-remark">
      {{ 'validations.maxlength' | translate: { number: item.max } }}
    </div>
    <div *ngIf="isValidControlDate(item.key, 'min')" class="form-remark">
      {{ 'validations.minlength' | translate: { number: item.min } }}
    </div>
    <div *ngIf="errorDateRange" class="form-remark">
      {{ 'validations.maxDateRange' | translate: { number: 90 } }}
    </div>
  </div>
</div>
