<div>
  <div class="d-flex flex-column align-items-center">
    <app-ngx-file-drag-drop
      #fileDragDropComponent
      [accept]="accept"
      [formControl]="fileControl"
      activeBorderColor="#3F51B5"
      [emptyPlaceholder]="emptyPlaceholder"
      (valueChanged)="onValueChange($event)"></app-ngx-file-drag-drop>
    <!--    <div *ngIf="fileControl?.errors?.maxFileSize" class="text-error">Dung lượng file không vượt quá {{maxSize | byte}}</div>-->
    <!--    <div *ngIf="fileControl?.errors?.fileType" class="text-error">Vui lòng tải đúng định dạng file {{ fileControl?.errors?.fileType?.requiredType }}</div>-->
    <!--    <div *ngIf="fileControl?.errors?.fileExtension" class="text-error">Vui lòng tải đúng định dạng file {{ fileControl?.errors?.fileExtension?.requiredExtension }}</div>-->
  </div>
  <div class="panel" *ngIf="showTablePreview && fileUpload?.length && !Object.keys(fileControl?.errors || {})?.length">
    <app-table-tree #tableComponent [configComponent]="tableComponentConfig"></app-table-tree>
  </div>

  <!--  <div class="d-flex justify-content-center mrt-4">-->
  <!--    <button [disabled]="!fileUpload?.length" class="btn btn-primary btn-md mrl-4 mrb-4" (click)="onImportFile()">-->
  <!--      {{ 'Tải lên' | translate }}  <mat-icon class="ic-upload mrl-2"></mat-icon>-->
  <!--    </button>-->
  <!--  </div>-->
</div>
