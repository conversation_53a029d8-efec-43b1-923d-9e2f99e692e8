// form-control-renderer.component.ts
import { Component, EventEmitter, Injector, isDevMode, Output, SimpleChang<PERSON>, ViewChild, ViewEncapsulation } from '@angular/core'
import { TextareaControlComponent } from '../../data-input/textarea-control/textarea-control.component'
import { TreeSelectControlComponent } from '../../data-input/tree-select-control/tree-select-control.component'
import { cbbDistrictField, cbbProvinceField, cbbWardField, txtAddressField } from './app-select-address-control.model'
import { environment } from '@env/environment'
import { FormGroupAbstractComponent } from '@shared/components/data-input/form-group.abstract.component'
import _ from 'lodash'
import { EVENT_FORM_CONTROL } from '@shared'
import { FormControl, FormGroup } from '@angular/forms'
import { debounceTime, filter, take } from 'rxjs/operators'
import { CommonModule } from '@angular/common'

@Component({
  selector: 'app-select-address-control',
  templateUrl: './app-select-address-control.component.html',
  standalone: true,
  imports: [TextareaControlComponent, TreeSelectControlComponent, CommonModule],
  styles: [``]
})
export class AppSelectAddressControlComponent extends FormGroupAbstractComponent {
  @ViewChild('province') province: TreeSelectControlComponent
  @ViewChild('district') district: TreeSelectControlComponent
  @ViewChild('ward') ward: TreeSelectControlComponent

  @Output() clickMessageError: EventEmitter<any> = new EventEmitter<any>()
  @Output() formChanged: EventEmitter<any> = new EventEmitter<any>()

  cbbProvince = cbbProvinceField()
  cbbDistrict = cbbDistrictField()
  cbbWard = cbbWardField()
  txtAddress = txtAddressField()
  flagLoadEdit = false
  flagLoadEditDistrict = false

  strProvince: string
  strDistrict: string
  strWard: string
  formAddressGroup = new FormGroup({})

  constructor(protected override injector: Injector) {
    super(injector)
  }

  override ngOnInit(): void {
    super.ngOnInit()
    this.cbbProvince.required = this.item.required
    this.cbbDistrict.required = this.item.required
    this.cbbWard.required = this.item.required

    this.txtAddress.readOnly = this.item.readOnly
    this.cbbProvince.readOnly = this.item.readOnly
    this.cbbDistrict.readOnly = this.item.readOnly
    this.cbbWard.readOnly = this.item.readOnly

    this.formAddressGroup = this.itemControl.toFormGroup([this.cbbProvince, this.cbbDistrict, this.cbbWard, this.txtAddress])
    this.formAddressGroup.addControl('fullAddress', new FormControl(''))
    this.form.setControl(this.item.key, this.formAddressGroup)
    this.form
      .get(this.item.key)
      .valueChanges.pipe(
        debounceTime(300),
        filter((value) => value && Object.keys(value).length > 0),
        take(1)
      )
      .subscribe((value) => {
        console.log('AppSelectAddressControlComponent - Initial Value:', value)
        this.onLoadEditByPatchForm(value)
      })
  }

  override ngOnChanges(changes: SimpleChanges) {
    super.ngOnChanges(changes)
    if (
      changes?.item?.firstChange ||
      (!changes?.item?.firstChange && !_.isEqual(changes?.item?.currentValue?.value, changes?.item?.previousValue?.value))
    ) {
     
    }
  }


  onChangedProvince($event: any) {
    if (!Array.isArray($event?.data) || _.isEmpty($event?.data) || $event?.data === EVENT_FORM_CONTROL.CREATE_CONTROL) return
    if (this.flagLoadEdit) {
      this.flagLoadEdit = !this.flagLoadEdit
      return
    }
    this.cbbDistrict.paramData = {
      ...this.cbbDistrict.paramData,
      url: `${environment.services.portal}/v1/district?province_code=${$event?.data[0].id}`
    }

    this.district.searchDataOption('')
    this.onClearProvince()

    this.strProvince = $event?.data[0].data.value
    this.updateFullAddress()
  }

  onChangedDistrict($event: any) {
    if (!Array.isArray($event?.data) || _.isEmpty($event?.data) || $event?.data === EVENT_FORM_CONTROL.CREATE_CONTROL) return
    if (this.flagLoadEditDistrict) {
      this.flagLoadEditDistrict = !this.flagLoadEditDistrict
      return
    }
    this.cbbWard.paramData = {
      ...this.cbbWard.paramData,
      url: `${environment.services.portal}/v1/ward?district_code=${$event?.data[0].id}`
    }
    this.ward.searchDataOption('')
    this.onClearDistrict()

    this.strDistrict = $event?.data[0].data.value
    this.updateFullAddress()
  }

  onChangeWard($event: any) {
    if (!Array.isArray($event?.data) || _.isEmpty($event?.data) || $event?.data === EVENT_FORM_CONTROL.CREATE_CONTROL) return
    this.strWard = $event?.data[0].data.value
    this.updateFullAddress()
  }

  updateFullAddress() {
    const addressValue = this.formAddressGroup.get(this.txtAddress.key).value
    const fullAddress = [addressValue, this.strWard, this.strDistrict, this.strProvince].filter(Boolean).join(', ')
    this.form.get(this.item.key).get('fullAddress').patchValue(fullAddress)
    // Emit sự kiện thay đổi để cập nhật UI
    this.formChanged.emit(this.formAddressGroup.value)
  }

  onClearProvince() {
    this.strProvince = ''
    if (this.flagLoadEdit) {
      this.flagLoadEdit = !this.flagLoadEdit
      return
    }
    this.itemControl.resetValueFormControlUntouched(this.formAddressGroup, this.cbbDistrict.key)
    this.district.item.options = []
    this.district.item.value = undefined
    this.onClearDistrict()
  }

  onClearDistrict() {
    this.strDistrict = ''
    if (this.flagLoadEditDistrict) {
      this.flagLoadEditDistrict = !this.flagLoadEditDistrict
      return
    }
    this.itemControl.resetValueFormControlUntouched(this.formAddressGroup, this.cbbWard.key)
    this.ward.item.options = []
    this.ward.item.value = undefined
  }

  onChangeAddress($event: any) {
    console.log('[AppSelectAddressControlComponent] onChangeAddress event:', $event)
    // Đảm bảo giá trị đã được cập nhật trong formAddressGroup trước khi gọi updateFullAddress
    if ($event && $event.target && $event.target.value !== undefined) {
      console.log('[AppSelectAddressControlComponent] Address value changed to:', $event.target.value)
    }
    this.updateFullAddress()
    // Log giá trị form sau khi cập nhật
    console.log('[AppSelectAddressControlComponent] formAddressGroup value after update:', this.formAddressGroup.value)
  }

  private onLoadEditByPatchForm(addressObject: any) {
    const province = addressObject.province
    const district = addressObject.district
    const ward = addressObject.ward
    const address = addressObject.address

    //patch province via item
    if (province) {
      this.flagLoadEdit = true
      this.cbbProvince = {
        ...this.cbbProvince,
        paramData: {
          ...this.cbbProvince.paramData,
          url: `${environment.services.portal}/v1/province`,
          queryStringOnInit: `code=${province || ''}`
        },
        value: province
      }
    }

    //patch district
    if (district) {
      this.flagLoadEditDistrict = true
      this.cbbDistrict = {
        ...this.cbbDistrict,
        paramData: {
          ...this.cbbDistrict.paramData,
          url: `${environment.services.portal}/v1/district?province_code=${province}`
        },
        value: district
      }
    }

    //patch ward
    if (ward) {
      this.cbbWard = {
        ...this.cbbWard,
        paramData: {
          ...this.cbbWard.paramData,
          url: `${environment.services.portal}/v1/ward?district_code=${district}`
        },
        value: ward
      }
    }

    //patch address
    if (address) {
      this.txtAddress = {
        ...this.txtAddress,
        value: address
      }
    }
  }
}
