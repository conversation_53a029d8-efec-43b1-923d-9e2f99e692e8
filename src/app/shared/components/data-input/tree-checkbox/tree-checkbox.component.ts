import { SelectionModel } from '@angular/cdk/collections'
import { FlatTreeControl } from '@angular/cdk/tree'
import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges, ViewChild } from '@angular/core'
import { MatTreeFlatDataSource, MatTreeFlattener, MatTreeModule } from '@angular/material/tree'
import { debounceTime, Subject } from 'rxjs'
import { MatIconModule } from '@angular/material/icon'
import { MatCheckboxModule } from '@angular/material/checkbox'
import { MatButtonModule } from '@angular/material/button'
import { ChecklistDatabase } from '@shared/components/data-input/tree-select-control/tree-checkbox/tree-checkbox.component'
import { ItemFlatNode, ItemNode } from '../../../models'
import { NgIf } from '@angular/common'

@Component({
  selector: 'app-tree-checkbox',
  templateUrl: './tree-checkbox.component.html',
  styleUrls: ['./tree-checkbox.component.scss'],
  providers: [ChecklistDatabase],
  standalone: true,
  imports: [MatTreeModule, MatButtonModule, MatCheckboxModule, MatIconModule, NgIf]
})
export class TreeCheckboxComponent implements OnChanges {
  @ViewChild('tree') tree
  @Output() onSelected = new EventEmitter<any>()
  @Input() readOnly = false
  @Input() debounceEmitTime = 500 // must be greater than 500ms
  private dataChangeSubject = new Subject<void>()

  /** Map from flat node to nested node. This helps us finding the nested node to be modified */
  flatNodeMap = new Map<ItemFlatNode, ItemNode>()

  /** Map from nested node to flattened node. This helps us to keep the same object for selection */
  nestedNodeMap = new Map<ItemNode, ItemFlatNode>()
  treeControl: FlatTreeControl<ItemFlatNode>

  treeFlattener: MatTreeFlattener<ItemNode, ItemFlatNode>

  dataSource: MatTreeFlatDataSource<ItemNode, ItemFlatNode>

  /** The selection for checklist */
  checklistSelection = new SelectionModel<ItemFlatNode>(true /* multiple */)

  /**
   * mảng flat data để buildTree
   */
  @Input()
  public data = []
  /**
   * array ID checked
   */
  @Input()
  public defaults = []
  /**
   * id của cấp 1
   */
  @Input()
  public parentId: string

  constructor(private _database: ChecklistDatabase) {
    this.treeFlattener = new MatTreeFlattener(this.transformer, this.getLevel, this.isExpandable, this.getChildren)
    this.treeControl = new FlatTreeControl<ItemFlatNode>(this.getLevel, this.isExpandable)
    this.dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener)
    _database.dataChange.subscribe((data) => {
      this.dataSource.data = data
    })

    this.dataChangeSubject.pipe(debounceTime(this.debounceEmitTime)).subscribe(() => {
      this.emitDataChange()
    })
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.data?.length) {
      this._database.buildFileTree(this.data, this.parentId, 'keySearch')
      this.checklistSelection.clear()
    }
  }
  ngAfterViewInit() {
    this.checklistSelection.clear()
    this.tree.treeControl.expandAll()
    // checked default
    this.defaultChecked(this.treeControl.dataNodes)
  }

  defaultChecked(lists: any[]) {
    try {
      const findChecked = (arr) =>
        arr.forEach((child) => {
          if (this.defaults.find((x) => String(x) === String(child.data.id))) {
            if (child.data.children?.length) {
              this.todoItemSelectionToggle(child)
            } else {
              this.todoLeafItemSelectionToggle(child)
            }
          }
        })
      findChecked(lists)
    } catch (err) {}
  }

  getLevel = (node: ItemFlatNode) => node.level

  isExpandable = (node: ItemFlatNode) => node.expandable

  getChildren = (node: ItemNode): ItemNode[] => node.children

  hasChild = (_: number, _nodeData: ItemFlatNode) => {
    return _nodeData.expandable
  }

  hasNoContent = (_: number, _nodeData: ItemFlatNode) => _nodeData.item === ''

  /**
   * Transformer to convert nested node to flat node. Record the nodes in maps for later use.
   */
  transformer = (node: ItemNode, level: number) => {
    const existingNode = this.nestedNodeMap.get(node)
    const flatNode = existingNode && existingNode.item === node.item ? existingNode : new ItemFlatNode()
    flatNode.item = node.item
    flatNode.level = level
    flatNode.expandable = !!node.children?.length
    flatNode.data = node
    this.flatNodeMap.set(flatNode, node)
    this.nestedNodeMap.set(node, flatNode)
    return flatNode
  }

  /** Whether all the descendants of the node are selected. */
  descendantsAllSelected(node: ItemFlatNode): boolean {
    const descendants = this.treeControl.getDescendants(node)
    const descAllSelected = descendants.every((child) => this.checklistSelection.isSelected(child))
    return descAllSelected
  }

  /** Whether part of the descendants are selected */
  descendantsPartiallySelected(node: ItemFlatNode): boolean {
    const descendants = this.treeControl.getDescendants(node)
    const result = descendants.some((child) => this.checklistSelection.isSelected(child))
    return result && !this.descendantsAllSelected(node)
  }

  /** Toggle the to-do item selection. Select/deselect all the descendants node */
  todoItemSelectionToggle(node: ItemFlatNode): void {
    this.checklistSelection.toggle(node)
    const descendants = this.treeControl.getDescendants(node)
    this.checklistSelection.isSelected(node) ? this.checklistSelection.select(...descendants) : this.checklistSelection.deselect(...descendants)

    // Force update for the parent
    descendants.every((child) => this.checklistSelection.isSelected(child))
    this.checkAllParentsSelection(node)
    this.dataChangeSubject.next() // Gọi next để kích hoạt sự kiện debounce
  }
  emitDataChange() {
    this.onSelected.emit({
      selected: this.checklistSelection.selected,
      isCheckedAll: this.treeControl.dataNodes.length === this.checklistSelection.selected.length
    })
  }

  /** Toggle a leaf to-do item selection. Check all the parents to see if they changed */
  todoLeafItemSelectionToggle(node: ItemFlatNode): void {
    this.checklistSelection.toggle(node)
    this.checkAllParentsSelection(node)
    this.dataChangeSubject.next() // Gọi next để kích hoạt sự kiện debounce
  }

  /* Checks all the parents when a leaf node is selected/unselected */
  checkAllParentsSelection(node: ItemFlatNode): void {
    let parent: ItemFlatNode | null = this.getParentNode(node)
    while (parent) {
      this.checkRootNodeSelection(parent)
      parent = this.getParentNode(parent)
    }
  }

  /** Check root node checked state and change it accordingly */
  checkRootNodeSelection(node: ItemFlatNode): void {
    const nodeSelected = this.checklistSelection.isSelected(node)
    const descendants = this.treeControl.getDescendants(node)
    const descAllSelected = descendants.every((child) => this.checklistSelection.isSelected(child))
    if (nodeSelected && !descAllSelected) {
      this.checklistSelection.deselect(node)
    } else if (!nodeSelected && descAllSelected) {
      this.checklistSelection.select(node)
    }
    this.dataChangeSubject.next() // Gọi next để kích hoạt sự kiện debounce
  }

  /* Get the parent node of a node */
  getParentNode(node: ItemFlatNode): ItemFlatNode | null {
    const currentLevel = this.getLevel(node)

    if (currentLevel < 1) {
      return null
    }

    const startIndex = this.treeControl.dataNodes.indexOf(node) - 1

    for (let i = startIndex; i >= 0; i--) {
      const currentNode = this.treeControl.dataNodes[i]

      if (this.getLevel(currentNode) < currentLevel) {
        return currentNode
      }
    }
    return null
  }

  /** Select the category so we can insert the new item. */
  addNewItem(node: ItemFlatNode) {
    const parentNode = this.flatNodeMap.get(node)
    this._database.insertItem(parentNode!, '')
    this.treeControl.expand(node)
  }

  /** Select the category so we can insert the new item. */
  addNewSubNode(node: ItemFlatNode) {
    const parentNode = this.flatNodeMap.get(node)
    this._database.insertItem(parentNode!, '')
    this.treeControl.expand(node)
  }

  /** Save the node to database */
  saveNode(node: ItemFlatNode, itemValue: string) {
    const nestedNode = this.flatNodeMap.get(node)
    this._database.updateItem(nestedNode!, itemValue)
    // console.log(_database);
  }
}
