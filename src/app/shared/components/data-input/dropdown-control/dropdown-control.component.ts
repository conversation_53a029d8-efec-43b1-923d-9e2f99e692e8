import { Component, Injector, ViewChild } from '@angular/core'
import { FormGroupAbstractComponent } from '../form-group.abstract.component'
import { TranslateModule } from '@ngx-translate/core'
import { MatOptionModule } from '@angular/material/core'
import { MatSelectModule } from '@angular/material/select'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { MatIconModule } from '@angular/material/icon'
import { ExtendedModule } from '@angular/flex-layout/extended'
import { NgClass, NgFor, NgIf } from '@angular/common'

@Component({
  selector: 'sm-dropdown-control',
  templateUrl: './dropdown-control.component.html',
  styleUrls: ['./dropdown-control.component.scss'],
  standalone: true,
  imports: [NgClass, ExtendedModule, NgIf, MatIconModule, FormsModule, ReactiveFormsModule, MatSelectModule, MatOptionModule, NgFor, TranslateModule]
})
export class DropdownControlComponent extends FormGroupAbstractComponent {
  @ViewChild('selectElement') selectElement: any
  constructor(protected override injector: Injector) {
    super(injector)
  }

  makeChoice(e) {
    if ((e.key === 'ArrowDown' || e.key === 'ArrowUp') && this.item.type !== 'multiple') {
      const totalOptions = this.selectElement.options._results
      for (let i = 0; i < totalOptions.length; i++) {
        if (totalOptions[i]._active === true) {
          this.f[this.item.key].patchValue(this.item.options[i].value)
        }
      }
    }
  }
}
