import { Component, ElementRef, Inject, Injector, ViewChild } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog'
import { HttpOptions, MessageSeverity, Verbs } from '@shared'
import FileSaver from 'file-saver'

import { environment } from '@env/environment'
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms'

import { TranslateModule } from '@ngx-translate/core'
import { NgxFileDragDropComponent } from '../ngx-file-drag-drop/ngx-file-drag-drop.component'
import { FlexModule } from '@angular/flex-layout/flex'
import { MatIconModule } from '@angular/material/icon'
import { MatButtonModule } from '@angular/material/button'
import { ComponentDialogAbstract } from '../../../abstract/component-dialog.abstract'
import { FileValidators } from '../../../validators'
import dayjs from 'dayjs/esm'

@Component({
  selector: 'app-import-excel-dialog',
  templateUrl: './app-import-excel-dialog.component.html',
  styleUrls: ['./app-import-excel-dialog.component.scss'],
  standalone: true,
  imports: [MatButtonModule, MatIconModule, MatDialogModule, FlexModule, NgxFileDragDropComponent, FormsModule, ReactiveFormsModule, TranslateModule]
})
export class AppImportExcelDialogComponent extends ComponentDialogAbstract {
  textButtonLeft = 'btn.cancel'
  textButtonRight = 'btn.accept'

  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>
  accept: string = '.xlsx'
  multiple: boolean
  fileUpload: File[]
  maxSize = 2e6
  fileControl = new FormControl([], [FileValidators.required, FileValidators.maxFileSize(this.maxSize), FileValidators.fileExtension(['xlsx'])])

  onValueChange(files: File[]) {
    this.fileUpload = files
  }

  override ngOnInit() {
    super.ngOnInit()
  }

  constructor(
    protected override injector: Injector,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<AppImportExcelDialogComponent>
  ) {
    super(injector)
    this.dialogRef.disableClose = true
  }

  onDownloadFile(fileName = null) {
    if (this.data?.urlDownload) {
      this.download(this.data?.urlDownload, {}).subscribe(
        (res) => {
          if (res) {
            FileSaver.saveAs(res.body, fileName || `template_${dayjs().format('YYYYMMDD')}.xlsx`)
            this.toastr.showToastri18n('dialog.export-data-success', '', MessageSeverity.success)
          } else {
            this.toastr.showToastri18n('dialog.export-data-error', '', MessageSeverity.error)
          }
        },
        (error) => {
          this.toastr.showToastri18n('dialog.export-data-error', '', MessageSeverity.error)
        }
      )
    }
  }

  closeDialog() {
    if (this.dialogRef.close) {
      this.dialogRef.close({ status: 0 })
    }
  }

  /**
   * download template
   * @param url
   * @param params
   */
  download(url, params = {}) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${url}`,
      params: params
    }
    return this.httpClient.download(Verbs.GET, options)
  }

  /**
   *
   * @param formData
   */
  importExcel(formData: FormData) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${this.data?.urlImport}`,
      params: {}
    }
    return this.httpClient.uploadFormData(options, formData)
  }

  onImportFile() {
    if (this.multiple) {
      this.uploadExcelImport(this.fileUpload)
    } else {
      this.uploadExcelImport(this.fileUpload[0])
    }
  }

  /**
   * import excel
   * @param $event
   */
  uploadExcelImport($event: any) {
    const formData: FormData = new FormData()
    formData.append('file', $event)
    this.importExcel(formData).subscribe(
      (res) => {
        if (res?.body?.type === 'application/octet-stream') {
          this.toastr.showToastri18n('dialog.import-data-error', '', MessageSeverity.success)
          FileSaver.saveAs(res.body, `error-import-${dayjs().format('YYYYMMDD')}.xlsx`)
        } else {
          this.toastr.showToastri18n('dialog.import-data-success', '', MessageSeverity.success)
          this.onCloseCancel()
        }
      },
      (error) => {}
    )
  }

  onCloseCancel() {
    this.dialogRef.close(true)
  }
}
