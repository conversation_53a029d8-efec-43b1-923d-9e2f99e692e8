<div class="table-over">
  <table
    matSort
    (matSortChange)="sortData($event)"
    class="app-table table-info w-full"
    element.htmlForm
    mat-table
    [dataSource]="datasourceTree"
    [@.disabled]="true">
    <ng-container *ngFor="let column of columns; let i = index" [cdkColumnDef]="column.field">
      <th mat-header-cell *matHeaderCellDef [class.action]="column.field === 'action'" class="f-w-600 f-s-15">
          <ng-container *ngIf="column.type === 'checkbox-selection-all'; else notSelect">
            <mat-checkbox
              *ngIf="!this.configComponent.data?.checkBoxOnePage"
              (change)="$event ? masterToggle() : null"
              [(ngModel)]="isCheckAll"
              [checked]="isCheckAll || (this.selectedCount === this.totalItem && this.selectedCount != 0)"
              [indeterminate]="selection?.hasValue() && !isAllSelected()">
              <strong>{{ column.name }}</strong>
            </mat-checkbox>
            <mat-checkbox
              *ngIf="this.configComponent.data?.checkBoxOnePage"
              (change)="$event ? masterToggle() : null"
              [disabled]="disabledCheckBoxAll"
              [(ngModel)]="isCheckAll"
              [checked]="isCheckAll || (this.selectedCount === this.totalItem && this.selectedCount != 0)"
              [indeterminate]="isSelectSomeCurrentPage() && !isSelectAllCurrentPage()">
              <strong>{{ column.name }}</strong>
            </mat-checkbox>
          </ng-container>
          <ng-template #notSelect>
            <app-shared-table-sort-header
              [column]="column"
              [sortColumn]="sortColumn"
              [sortDirection]="sortDirection"
              (sortChanged)="onSortChanged($event, column)"></app-shared-table-sort-header>
          </ng-template>
        <!--        <span class="resize-handle" (mousedown)="onResizeStart($event, i, column)"></span>-->
      </th>
      <td mat-cell *matCellDef="let element">
        <div [class.flex-center-row]="column.type !== 'decimal'" #parentEle>
          <ng-container *ngIf="i === 0 && configComponent?.data?.isTreeData">
            <button
              mat-icon-button
              [style.visibility]="!element.expandable ? 'hidden' : ''"
              [style.margin-left.px]="element.level * 32"
              (click)="treeControl.toggle(element)">
              <mat-icon class="mat-icon-rtl-mirror">
                {{ treeControl.isExpanded(element) ? 'expand_more' : 'chevron_right' }}
              </mat-icon>
            </button>
          </ng-container>
          <ng-container *ngIf="column.field === 'action'; else elseNotAction">
            <app-action-button-menu
              class="d-flex"
              [columnActionLists]="columnActionLists"
              [element]="element"
              [configComponent]="configComponent"
              [checkScope]="checkScope"
              [allowScopes]="objFunction?.allowScopes"
              (actionClick)="onActionColumnClick($event)" />
          </ng-container>
          <ng-template #elseNotAction>
            <ng-container *ngIf="column.type === 'checkbox-selection' || column.type === 'checkbox-selection-all'; else notCheckboxSelection">
              <mat-checkbox
                [disabled]="disabledCheckRow(element)"
                (click)="$event.stopPropagation()"
                (change)="$event ? toggle(element) : null"
                [checked]="isSelected(element)"></mat-checkbox>
            </ng-container>
            <ng-template #notCheckboxSelection>
              <app-table-cell-display
                #cell
                [class.text-ellipsis]="column && column.type !== 'map'"
                [element]="element"
                [column]="column"
                (elementChange)="elementChange($event)"
                (onCellClicked)="onCellClicked($event)"
                matTooltip="{{ getTooltipByPath | functionCaller: element : column | arrayToString }}"
                [matTooltipDisabled]="!getToolTipStatus(parentEle, cell)"
                (actionContextMenuClick)="actionContextMenuClick($event)"></app-table-cell-display>
            </ng-template>
          </ng-template>
        </div>
      </td>
    </ng-container>
    <tr class="header-cell" mat-header-row *matHeaderRowDef="displayedColumns | toggleColumns: columns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns | toggleColumns: columns"></tr>
  </table>
  <app-no-data *ngIf="!datasourceTree?.data?.length" [message]="noDataMessage"></app-no-data>
</div>
<div class="panel-footer" *ngIf="!configComponent.data.noPaging">
  <app-pagination
    class="fullWidth"
    #pagePage
    [pageIndex]="pageIndex"
    (page)="handlePageEvent($event)"
    [totalItem]="totalItem"
    [pageSize]="pageSize"
    [showTextPage]="true"
    [showPageSizeList]="true"></app-pagination>
</div>
