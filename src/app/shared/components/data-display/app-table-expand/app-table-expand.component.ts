import { AfterViewChecked, Component, EventEmitter, Injector, Input, isDevMode, Output, SimpleChanges } from '@angular/core'
import { MatTableModule } from '@angular/material/table'
import { AppPaginationComponent, ComponentAbstract, extractValueFromColon, HttpOptions, MessageSeverity, Scopes, Verbs } from '@shared'
import { finalize, Subject, takeUntil } from 'rxjs'
import { PageEvent } from '@angular/material/paginator'
import { environment } from '@env/environment'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { FunctionCallerPipe, ToggleColumnsPipe } from '../../../pipe'
import { MatTooltipModule } from '@angular/material/tooltip'
import { MatButtonModule } from '@angular/material/button'
import { CdkTableModule } from '@angular/cdk/table'
import { MatSortModule } from '@angular/material/sort'
import { MatExpansionModule } from '@angular/material/expansion'
import { MatIconModule } from '@angular/material/icon'
import { FlexModule } from '@angular/flex-layout/flex'
import { NgFor, NgIf } from '@angular/common'
import { NoDataComponent } from '../../section/no-data/no-data.component'
import { ConfigService } from '@tnx/ngx-ui-builder'
import { FlatTreeControl } from '@angular/cdk/tree'
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree'
import { getPathFromUrl, getQueryParams } from '../../../utils/string.utils'
import { DisplayedColumn, TableComponentConfig } from '@shared/components/data-display/app-table-tree/app-table.model'
import { getElementByPath, getTooltipByPath } from '../../../utils/table.utils'
import _ from 'lodash'
import { AppTableCellDisplayComponent } from '@shared/components/data-display/app-table-cell-display/app-table-cell-display.component'
import { checkSomeScopes, updatedArrayConfig } from '../../../utils/array.utils'
import { ArrayToStringPipe } from '../../../pipe/array-to-string-multiline'
import { ResizeHandleDirective } from '../../../directives/resize-handle.directive'

@Component({
  selector: 'app-table-expand',
  templateUrl: './app-table-expand.component.html',
  styleUrls: ['./app-table-expand.component.scss'],
  standalone: true,
  imports: [
    NgIf,
    FlexModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    NgFor,
    MatExpansionModule,
    MatTableModule,
    MatSortModule,
    CdkTableModule,
    MatButtonModule,
    MatTooltipModule,
    NoDataComponent,
    AppPaginationComponent,
    ToggleColumnsPipe,
    FunctionCallerPipe,
    AppTableCellDisplayComponent,
    ArrayToStringPipe,
    ResizeHandleDirective
  ]
})
export class AppTableExpandedComponent extends ComponentAbstract implements AfterViewChecked {
  @Input() configComponent: TableComponentConfig
  @Input() isSearchAdvanced = false
  @Input() isSearchByKeyword = false
  @Input() isTreeSource: boolean = true
  @Input() checkScope = false //TODO: true nếu check rule theo scope
  @Output() onButtonClick = new EventEmitter<any>()
  @Output() onTableActionClick = new EventEmitter<any>()
  @Output() onTableSetConfig = new EventEmitter<any>()
  visibleColumns: DisplayedColumn[] = []
  dataTable = []
  datasourceTree: MatTreeFlatDataSource<any, any, any>
  searchString: string = 'dt'
  configId: string = ''
  private unsubscribe$: Subject<void> = new Subject<void>()

  private transformer = (node: any, level: number) => {
    return {
      expandable: !!node[this.configComponent.data.childrenAttr],
      ...node,
      level: level
    }
  }
  ngAfterViewChecked() {
    this.cdRef.detectChanges()
  }
  treeControl = new FlatTreeControl<any>(
    (node) => node.level,
    (node) => node.expandable
  )

  treeFlattener = new MatTreeFlattener(
    this.transformer,
    (node) => node.level,
    (node) => node.expandable,
    (node) => node[this.configComponent.data.childrenAttr]
  )

  constructor(
    protected override injector: Injector,
    private serviceConfig: ConfigService
  ) {
    super(injector)
    this.initQuickSearchForm()
    this.form = this.itemControl.toFormGroup([])
    this.dataTable = []
    this.configId = this.serviceConfig.getComponentId()
  }

  initQuickSearchForm() {}

  get columns() {
    return this.visibleColumns
  }

  get isStaticTable() {
    return this.configComponent?.data.isStaticTable || false
  }

  get columnActionLists() {
    return this.configComponent?.data?.columnActionLists || []
  }

  get advancedSearchFields() {
    return this.configComponent?.data.displayedColumns || []
  }

  get displayedColumns(): any[] {
    return this.visibleColumns.map((c) => c.field)
  }

  componentInit(): void {
    if (this.checkScope) {
      let scopes = this.route.snapshot.data.scopes as Array<Scopes>
      let resources = this.route.snapshot.data.resources as Array<string>
      this.getUserPermissions(resources)
      console.log('app table expand', this.objFunction.allowScopes)
    }
    this.loadDataTable()
  }

  override ngOnDestroy() {
    this.unsubscribe$.next()
    this.unsubscribe$.complete()
  }

  /**
   * do not remove
   * @param changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    isDevMode() && console.log('app-table', this.configComponent)
    if (_.isEqual(changes.configComponent.currentValue, changes.configComponent.previousValue)) return
    if (!this.isStaticTable && this.configComponent?.data?.apiList) {
      this.search().then((r) => {})
    }
    this.setVisibleColumns()
  }

  setVisibleColumns() {
    const currConfig = JSON.parse(localStorage.getItem(`column-${this.configId}`))

    if (this.configComponent?.data?.displayedColumns) {
      this.visibleColumns = [] // trigger re-render live edit ux builder
      setTimeout(() => {
        const setDefaultColumnCell = this.configComponent?.data?.displayedColumns.map((e) => {
          if (e.type) {
            return e
          } else {
            return {
              ...e,
              type: 'textbox'
            }
          }
        })
        // this.visibleColumns = _.uniqBy([...this.visibleColumns, ...setDefaultColumnCell] || [], 'field')
        const columns = _.uniqBy([...this.visibleColumns, ...setDefaultColumnCell], 'field')

        this.visibleColumns = updatedArrayConfig(columns, currConfig)
        console.log('this.visibleColumns', this.visibleColumns)
        console.log('this.displayedColumns', this.displayedColumns)
      }, 100)
    }
  }
  handlePageEvent(e: PageEvent) {
    this.pageEvent = e
    this.pageSize = e.pageSize
    this.pageIndex = e.pageIndex
    this.saveStatePaging()
    if (!this.isStaticTable) {
      this.search(this.filterQuery)
    } else {
      this.datasourceTree = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener, this.getStaticRecords(this.pageIndex, this.pageSize))
    }
  }
  /**
   * call API get list
   * @param filter
   */
  search(filter = {}) {
    if (this.isStaticTable) {
      return Promise.resolve()
    }

    this.indicator.showActivityIndicator(true)

    return new Promise<void>((resolve, reject) => {
      this.getList(this.pageIndex, this.pageSize, filter)
        .pipe(
          takeUntil(this.unsubscribe$),
          finalize(() => this.indicator.hideActivityIndicator(true))
        )
        .subscribe((res: any) => {
          if (res) {
            this.totalItem = res.data.totalElements
            this.pageSize = res.data.size
            const page = this.pageIndex * this.pageSize
            this.dataTable = res.data.content.map((x, index) => {
              return { ...x, no: page + index + 1 }
            })
            this.setDataSource()
            this.expandNode(0)
            resolve() // Resolve the Promise on success
          } else {
            this.showErrorGetData()
            reject(new Error('Failed to fetch data')) // Reject the Promise on failure
          }
        })
    })
  }

  loadDataTable() {
    this.totalItem = this.dataTable.length
    this.pageSize = this.configComponent.data.pageSize ? Number(this.configComponent.data.pageSize) : 10
    this.setDataSource()
  }

  setDataSource() {
    this.datasourceTree = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener, this.dataTable)
  }

  expandNode(index: number) {
    if (this.treeControl.dataNodes?.length) {
      this.treeControl.expand(this.treeControl.dataNodes[index])
    }
  }

  /**
   * set source static
   * required call set .dataTable first
   */
  loadDataStaticTable(reloadColumn = true, data = [], keepPaging = false) {
    if (reloadColumn) {
      this.setVisibleColumns()
    }
    this.dataTable = data.map((x, index) => {
      return { ...x, no: index + 1 }
    })
    this.totalItem = this.dataTable.length

    if (!keepPaging) {
      this.pageIndex = 0
      if (this.queryParams?.page) {
        this.pageIndex = parseInt(this.queryParams.page) - 1
      }
      this.pageSize = this.configComponent.data.pageSize ? Number(this.configComponent.data.pageSize) : 10
      if (this.configComponent.data.noPaging) {
        this.pageSize = 999999
      }
    }
    this.datasourceTree = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener, this.getStaticRecords(this.pageIndex, this.pageSize))
  }

  getStaticRecords(pageIndex: number, pageSize: number) {
    const startIndex = pageIndex * pageSize
    const endIndex = startIndex + pageSize
    return this.dataTable.slice(startIndex, endIndex)
  }

  /**
   * call API DELETE
   * @param element
   */
  onDelete(key: string) {
    if (!this.configComponent?.data?.apiDelete) {
      this.toastr.showToastri18n('dialog.delete-data-error', 'Chưa cấu hình API DELETE', MessageSeverity.error)
    } else {
      this.dialogService.confirm(
        {
          title: `Bạn có chắc muốn xóa <br/>${this.configComponent?.data?.tableTitle}?`,
          message: '',
          textButtonRight: 'btn.delete'
        },
        (result) => {
          if (result) {
            this.delete(key)
              .pipe(
                takeUntil(this.ngUnsubscribe),
                finalize(() => this.indicator.hideActivityIndicator())
              )
              .subscribe(
                (res: any) => {
                  if (res) {
                    this.search(this.filterQuery)
                    this.showDialogSuccessI18n('', `Xóa ${this.configComponent?.data?.tableTitle} thành công`)
                  }
                },
                (error) => {
                  this.showDialogErrorI18n(error?.error?.message, error?.error?.error)
                }
              )
          }
        }
      )
    }
  }

  /**
   * call api delete
   * @param id
   */
  delete(id: string) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${this.configComponent?.data.apiDelete}/${id}`
    }
    return this.httpClient.delete(options)
  }

  getList(page, size, filter) {
    if (!this.configComponent?.data?.apiList) return
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${this.configComponent?.data.apiList}`,
      params: {
        ...filter,
        page,
        size
      }
    }
    return this.httpClient.get(options)
  }

  /**
   *
   * @param params
   */
  download(url, params = {}) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${url}`,
      params: params
    }
    return this.httpClient.download(Verbs.GET, options)
  }

  /**
   * thanhnx
   * Bấm vào button ở table column action
   * @param button
   */
  onActionColumnClick(button, element) {
    const url = getPathFromUrl(button?.routerName || '')
    if (button?.navigationType === 'nav' && button?.routerName) {
      const queryObject = getQueryParams(button?.routerName)
      const code = extractValueFromColon(url)
      if (code) {
        this.goTo(`${environment.base_path}${url.replace(`:${code}`, element[code])}`)
      } else {
        this.goTo(`${environment.base_path}${url}`, queryObject)
      }
    } else if (button?.navigationType === 'popup' && button?.routerName) {
      //TODO: call popup từ string
    } else if (button?.navigationType === 'emit') {
      this.onTableActionClick.emit({ ...button, row: element })
    } else if (button?.navigationType === 'delete') {
      const propertyId = extractValueFromColon(button.routerName)
      if (propertyId) {
        this.onDelete(element[propertyId])
      } else {
        this.onDelete(element['id'])
      }
    } else {
      this.toastr.showToastr('', 'Chưa cài đặt để xử lý sự kiện', MessageSeverity.warning)
    }
  }

  protected readonly getElementByPath = getElementByPath
  protected readonly getTooltipByPath = getTooltipByPath
  protected readonly _ = _

  filterLeafNode(node: any, column: any): boolean {
    console.log(node[column.path])
    if (!node[column.path]) return false
    if (!this.searchString) {
      return false
    }
    return node[column.path].toLowerCase().indexOf(this.searchString?.toLowerCase()) === -1
  }

  filterParentNode(node: any, column: any): boolean {
    if (!node[column.path]) return false
    if (!node.expandable) return false

    if (!this.searchString || node[column.path].toLowerCase().indexOf(this.searchString?.toLowerCase()) !== -1) {
      return false
    }
    const descendants = this.treeControl.getDescendants(node)

    if (descendants.some((descendantNode) => descendantNode[column.path].toLowerCase().indexOf(this.searchString?.toLowerCase()) !== -1)) {
      return false
    }

    return true
  }

  isShowButton(inputScope: string[] | undefined) {
    if (this.checkScope && !_.isEmpty(inputScope)) {
      return checkSomeScopes(this.objFunction.allowScopes, inputScope)
    }
    return true
  }
  pathProperty(element) {
    return element.expandable ? 'path' : 'pathExpand'
  }
  getToolTipStatus(parentEle, cell) {
    return parentEle?.offsetWidth < cell?.eleRef?.nativeElement?.previousElementSibling?.offsetWidth
  }
}
