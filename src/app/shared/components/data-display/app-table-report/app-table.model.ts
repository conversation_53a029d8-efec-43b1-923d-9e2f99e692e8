// To parse this data:
//
//   import { Convert, TableModel } from "./file";
//
//   const tableModel = Convert.toTableModel(json);

import { ComponentConfig } from '@tnx/ngx-ui-builder'

export interface TableComponentConfig extends ComponentConfig {
  type: string | '_container'
  data: TableConfigModel
}

export interface TableConfigModel {
  isStaticTable?: boolean
  isTreeData?: boolean
  noPaging?: boolean
  disableAutoCallOnChange?: boolean // false to disable
  hideSearchAdvanced?: boolean // hide button seach advanced
  childrenAttr?: string
  pageSize?: string
  tableTitle?: string
  apiDetail?: string
  apiList?: string
  apiListMethod?: string // GET, POST
  apiCreate?: string
  apiUpdate?: string
  apiDelete?: string
  buttonLists?: any[]
  displayedColumns?: DisplayedColumn[]
  quickSearchFields?: QuickSearchField[]
  columnActionLists?: ColumnAction[]
  subValue?: string
}

export interface ColumnAction {
  title?: string
  type?: string
  class?: string
  icon?: string
  navigationType?: string
  routerName?: string
  scope?: string
  row?: any // data click
  condition?: any
}

export interface DisplayedColumn {
  name?: string
  field?: string
  path?: string
  show?: boolean
  type?: string
  enumText?: OptionEnumText[]
  suffixIconClass?: OptionEnumText[]
  prefixIconClass?: OptionEnumText[]
  subString?: string
  condition?: string
}

export interface OptionEnumText {
  key?: string
  text?: string
  class?: string
}

export interface QuickSearchField {
  key?: string
  text?: string
}

// Converts JSON strings to/from your types
export class Convert {
  public static toTableModel(json: string): TableConfigModel {
    return JSON.parse(json)
  }

  public static tableModelToJson(value: TableConfigModel): string {
    return JSON.stringify(value)
  }
}
