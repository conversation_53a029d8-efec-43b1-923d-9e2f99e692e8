<mat-tree #tree [dataSource]="dataSource" [treeControl]="treeControl">
  <mat-tree-node *matTreeNodeDef="let node" matTreeNodeToggle matTreeNodePadding>
    <button mat-icon-button disabled></button>
    <mat-checkbox
      *ngIf="isMultiCheckbox()"
      color="primary"
      class="checklist-leaf-node"
      [disabled]="readOnly"
      [checked]="isSelected(node)"
      #child
      (change)="leafItemSelectionToggle(node)"
      [matTooltip]="generateValue(node.data, this.itemOptions.paramData.value)"
      [matTooltipDisabled]="disableToolTip(child)">
      {{ generateValue(node.data, this.itemOptions.paramData.value) }}
    </mat-checkbox>
    <button
      #child
      class="text-left text-ellipsis"
      [disabled]="readOnly"
      *ngIf="itemOptions?.type === 'single'"
      (click)="leafItemSelectionToggle(node)"
      [matTooltip]="generateValue(node.data, this.itemOptions.paramData.value)"
      [matTooltipDisabled]="disableToolTip(child)">
      {{ generateValue(node.data, this.itemOptions.paramData.value) }}
    </button>
  </mat-tree-node>

  <mat-tree-node *matTreeNodeDef="let node; when: hasChild" matTreeNodePadding>
    <button mat-icon-button matTreeNodeToggle [attr.aria-label]="'toggle ' + node.filename">
      <mat-icon class="mat-icon-rtl-mirror" color="primary">
        {{ treeControl.isExpanded(node) ? 'remove' : 'add' }}
      </mat-icon>
    </button>
    <mat-checkbox
      *ngIf="treeControl.isExpanded(node) && isMultiCheckbox()"
      color="primary"
      (click)="$event.stopPropagation()"
      [disabled]="readOnly"
      [checked]="descendantsAllSelected(node)"
      [indeterminate]="descendantsPartiallySelected(node)"
      class="text-ellipsis"
      #child
      (change)="itemSelectionToggle(node)"
      [matTooltip]="generateValue(node.data, this.itemOptions.paramData.value)"
      [matTooltipDisabled]="disableToolTip(child)">
      {{ generateValue(node.data, this.itemOptions.paramData.value) }}
    </mat-checkbox>
    <button
      #child
      class="text-left text-ellipsis"
      [disabled]="readOnly"
      *ngIf="itemOptions?.type === 'single'"
      (click)="itemSelectionToggle(node)"
      [matTooltip]="generateValue(node.data, this.itemOptions.paramData.value)"
      [matTooltipDisabled]="disableToolTip(child)">
      {{ generateValue(node.data, this.itemOptions.paramData.value) }}
    </button>
  </mat-tree-node>
</mat-tree>
