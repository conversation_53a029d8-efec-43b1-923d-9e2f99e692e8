<div class="dynamic-select-column-container">
  <div [formGroup]="form">
    <button
      mat-raised-button
      color="button"
      [matMenuTriggerFor]="menu"
      type="button"
      #menuTrigger="matMenuTrigger"
      (menuOpened)="menuOpened()"
      (menuClosed)="menuClosed()"
      class="icon-toggle-header">
      <!--        <mat-icon class="ico-sort-header" svgIcon='sortHeaderTable'></mat-icon>-->

      <!--      <mat-icon class="mr-0 ic-task_list"></mat-icon>-->
      <div class="d-flex align-items-center">
        <i-tabler class="icon-20 m-r-4" name="table-options"></i-tabler>
      </div>
    </button>
    <div class="form-input-row">
      <mat-menu #menu="matMenu" class="dropdown-tree mat-elevation-z0 custom-menu" (closed)="menuClosed()">
        <div [style.min-width.px]="menuWidth">
          <div class="title-select-container">
            <div class="title-select"><PERSON><PERSON> chọn cột hiển thị</div>
            <div *ngIf="!dynamicSelect.enableConfirmBtn" class="sub-title-select">Vui lòng chọn ít nhất 2 cột để hiển thị</div>
          </div>
          <div #scrollContainer class="scroll-menu">
            <ng-container>
              <app-dynamic-select-checkbox #dynamicSelect [dataSelect]="tableColumn"></app-dynamic-select-checkbox>
            </ng-container>
          </div>
          <div class="d-flex flex-row justify-content-center align-items-center footer-column gap-8">
            <button mat-flat-button color="basic" type="button" (click)="resetForm($event)">
              <div class="d-flex align-items-center">
                <i-tabler class="icon-20 m-r-4" name="table-minus"></i-tabler>
                <span>Toàn bộ</span>
              </div>
            </button>
            <button [disabled]="!dynamicSelect.enableConfirmBtn" mat-flat-button color="basic" type="button" (click)="confirm()">
              <div class="d-flex align-items-center">
                <i-tabler class="icon-20 m-r-4" name="device-ipad-horizontal-check"></i-tabler>
                <span>Xác nhận</span>
              </div>
            </button>
          </div>
        </div>
      </mat-menu>
    </div>
  </div>
</div>
<!-- table-options -->
