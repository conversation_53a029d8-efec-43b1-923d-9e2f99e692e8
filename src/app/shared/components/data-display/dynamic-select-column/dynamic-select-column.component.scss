.id-error {
  color: dodgerblue;
  cursor: pointer;
}

::ng-deep .cdk-overlay-container .dropdown-tree {
  max-width: none !important;
  min-width: 300px;
  width: 100%;
  overflow: auto;
  box-shadow: none !important;
  border: 1px solid #d8d8d8;
}
.no-data {
  font-size: 14px;
  color: #ccc;
  padding: 8px;
}

.scroll-menu {
  max-width: none !important;
  min-width: 100%;
  max-height: 320px;
  width: 100%;
  overflow: auto;
  margin-top: -20px;
}

::ng-deep .mat-menu-panel {
  width: 100% !important;
  padding: 0 10px !important;
}
.tree-node {
  border-bottom: 1px solid var(--color-gray-200);
  padding: 5px 0px;
  width: 100%;
}

.ng-dropdown-panel {
  position: absolute !important;
  top: 100% !important;
  left: 0 !important;
  width: 100%;
  z-index: 1000;
}

.title-select {
  font-weight: 600;
  font-size: 16px;
  color: #041557;
  padding-inline: 12px;
}
.sub-title-select {
  font-size: 12px;
  color: #041557;
  padding-inline: 12px;
}
.footer-column {
  padding-bottom: 12px;
  padding-top: 12px;
  background-color: white;
}

app-dynamic-select-column {
  .ng-select.ng-select-multiple .ng-select-container {
    display: none;
  }
}
.custom-menu .mat-mdc-menu-content {
  background-color: #fff;
  padding: 8px;
}
.icon-toggle-header {
  //border-radius: 8px;
  //border: 1px solid #B2B8CC;
  //width: 40px;
  //height: 40px;
  //margin-left: 8px;
  //align-items: center;
  //justify-content: center;
  &:hover {
    //background-color: #DADDFA;
  }
  .ico-sort-header {
    //color: #4F5B89;
  }
}

.title-select-container {
  padding-bottom: 24px;
}
