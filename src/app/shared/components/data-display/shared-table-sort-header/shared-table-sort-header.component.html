<ng-container *ngIf="!column.sort">
  {{ column.name }}
</ng-container>

<ng-container *ngIf="column.sort">
  <div class="col-header d-flex align-items-center cursor-pointer" (click)="onSort()">
    {{ column.name }}
    <i-tabler *ngIf="sortColumn !== column.field || isDefault" name="arrows-sort" class="icon-18"></i-tabler>
    <i-tabler *ngIf="sortDirection === 'desc' && sortColumn === column.field" name="arrow-down" class="icon-18"></i-tabler>
    <i-tabler *ngIf="sortDirection === 'asc' && sortColumn === column.field" name="arrow-up" class="icon-18"></i-tabler>
  </div>
</ng-container>
