<ng-container *ngFor="let button of columnActionLists.slice(0, hasMoreActions() ? number : columnActionLists.length); let i = index">
  <ng-container *ngIf="configComponent.data.isTreeData; else elseAction">
    <button
      *ngIf="isShowButton(button?.scopes) && button.navigationType === 'delete' && element?.isLeaf"
      matTooltip="{{ button?.title }}"
      matTooltipPosition="above"
      (click)="onActionColumnClick(button, element)"
      mat-icon-button
      class="d-flex align-items-center justify-content-center">
      <i-tabler name="{{ button.icon }}" class="icon-18 d-flex align-items-center"></i-tabler>
    </button>
    <button
      *ngIf="isShowButton(button?.scopes) && button.navigationType !== 'delete'"
      matTooltip="{{ button?.title }}"
      matTooltipPosition="above"
      (click)="onActionColumnClick(button, element)"
      mat-icon-button
      class="d-flex align-items-center justify-content-center">
      <i-tabler name="{{ button.icon }}" class="icon-18 d-flex align-items-center"></i-tabler>
    </button>
  </ng-container>
  <ng-template #elseAction>
    <button
      matTooltip="{{ button?.title }}"
      matTooltipPosition="above"
      (click)="onActionColumnClick(button, element)"
      (contextmenu)="showContextMenu($event)"
      mat-icon-button
      class="d-flex align-items-center justify-content-center">
      <i-tabler name="{{ button.icon }}" class="icon-18 d-flex align-items-center"></i-tabler>
    </button>
    <div *ngIf="isShowButton(button?.scopes) && button?.isLink">
      <app-context-menu-custom #contextMenuCustom (actionClick)="contextMenuAction($event, button)"></app-context-menu-custom>
    </div>
  </ng-template>
</ng-container>

<ng-container *ngIf="hasMoreActions()">
  <button [matMenuTriggerFor]="moreMenu" matTooltipPosition="above" matTooltip="Xem thêm">
    <mat-icon>more_horiz</mat-icon>
  </button>
  <mat-menu #moreMenu="matMenu" class="mat-elevation-z1">
    <ng-container *ngFor="let button of columnActionLists.slice(number)">
      <ng-container *ngIf="configComponent.data.isTreeData; else elseMoreAction">
        <button
          *ngIf="isShowButton(button?.scopes) && button.navigationType === 'delete' && element?.isLeaf"
          mat-menu-item
          matTooltipPosition="above"
          matTooltip="{{ button?.title }}"
          (click)="onActionColumnClick(button, element)"
          class="d-flex align-items-center justify-content-center">
          <i-tabler name="{{ button.icon }}" class="icon-18 d-flex align-items-center"></i-tabler>
          {{ button?.title }}
        </button>
        <button
          *ngIf="isShowButton(button?.scopes) && button.navigationType !== 'delete'"
          mat-menu-item
          matTooltipPosition="above"
          matTooltip="{{ button?.title }}"
          (click)="onActionColumnClick(button, element)"
          class="d-flex align-items-center justify-content-center">
          <i-tabler name="{{ button.icon }}" class="icon-18 d-flex align-items-center"></i-tabler>
          {{ button?.title }}
        </button>
      </ng-container>
      <ng-template #elseMoreAction>
        <button
          *ngIf="isShowButton(button?.scopes)"
          mat-menu-item
          matTooltipPosition="above"
          matTooltip="{{ button?.title }}"
          (click)="onActionColumnClick(button, element)"
          class="d-flex align-items-center justify-content-center">
          <i-tabler name="{{ button.icon }}" class="icon-18 d-flex align-items-center"></i-tabler>
          {{ button?.title }}
        </button>
      </ng-template>
    </ng-container>
  </mat-menu>
</ng-container>
