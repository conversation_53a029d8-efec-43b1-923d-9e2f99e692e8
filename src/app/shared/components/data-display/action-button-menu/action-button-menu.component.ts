import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges, ViewChild } from '@angular/core'
import { MatTooltip } from '@angular/material/tooltip'
import { MatIcon } from '@angular/material/icon'
import { <PERSON><PERSON>orO<PERSON>, NgIf } from '@angular/common'
import { MatMenu, MatMenuItem, MatMenuTrigger } from '@angular/material/menu'
import { MatIconButton } from '@angular/material/button'
import { extractValueFromColon, Scopes } from '@shared'
import { checkSomeScopes, evaluateCondition, getPathFromUrl } from '../../../utils'
import _ from 'lodash'
import { environment } from '@env/environment'
import { AppContextMenuCustomComponent } from '@shared/components/element/app-context-menu/app-context-menu.component'
import { TablerIconComponent } from 'angular-tabler-icons'

@Component({
  selector: 'app-action-button-menu',
  standalone: true,
  imports: [
    <PERSON>Tool<PERSON>,
    <PERSON>I<PERSON>,
    NgI<PERSON>,
    <PERSON><PERSON>or<PERSON><PERSON>,
    MatMenuTrigger,
    MatMenu,
    MatMenuItem,
    MatIconButton,
    AppContextMenuCustomComponent,
    TablerIconComponent
  ],
  templateUrl: './action-button-menu.component.html',
  styleUrl: './action-button-menu.component.scss'
})
export class ActionButtonMenuComponent implements OnChanges {
  @Input() number = 2
  @Input() columnActionLists: any[] = []
  @Input() element: any
  @Input() configComponent: any
  @Input() checkScope = false //TODO: true nếu check rule theo scope
  @Input() allowScopes: Scopes[]
  @Output() actionClick = new EventEmitter<{ button: any; element: any }>()
  @ViewChild('contextMenuCustom') contextMenuCustom: AppContextMenuCustomComponent

  hasMoreActions(): boolean {
    return this.columnActionLists.length > 3
  }

  onActionColumnClick(button: any, element: any) {
    this.actionClick.emit({ button, element })
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['columnActionLists']) {
      this.filterActionLists()
    }
  }

  filterActionLists() {
    this.columnActionLists = this.columnActionLists.filter((action) => {
      if (action.condition) {
        return evaluateCondition(action.condition, this.element)
      }
      return true
    })
  }

  isShowButton(inputScope: Scopes[]) {
    if (this.checkScope && !_.isEmpty(inputScope)) {
      const condition = checkSomeScopes(this.allowScopes, inputScope)
      console.log('condition display button inputScope  | allowScopes | condition', inputScope, this.allowScopes, condition)
      return condition
    }
    return true
  }

  hyperLink(button) {
    const url = getPathFromUrl(button?.routerName || '')
    const code = extractValueFromColon(url)
    if (code) {
      return `${environment.base_path}${url.replace(`:${code}`, this.element[code])}` // Thiết lập href mới
    } else {
      return ''
    }
  }

  showContextMenu($event: MouseEvent) {
    this.contextMenuCustom.showContextMenu($event)
  }
  contextMenuAction($event, button) {
    if ($event === 0) {
      window.open(this.hyperLink(button), '_blank')
    }
  }
}
