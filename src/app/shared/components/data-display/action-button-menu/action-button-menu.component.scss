button {
  font-size: 14px;
}
/* styles.css */
.custom-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  position: relative;
  overflow: hidden;
  border-radius: 50%;
  background-color: transparent;
  transition: background-color 0.3s;
}

.custom-button mat-icon {
  font-size: 21px;
  color: black;
}

.custom-button:hover {
  background-color: rgba(0, 0, 0, 0.1); /* <PERSON><PERSON>u nền tối lại khi hover */
}
