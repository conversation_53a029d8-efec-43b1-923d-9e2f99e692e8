import { Component, ElementRef, Injector, Input, ViewChild } from '@angular/core'
import { finalize, takeUntil } from 'rxjs'
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog'
import { MatIconModule } from '@angular/material/icon'
import { MatButtonModule } from '@angular/material/button'
import { HttpOptions, Verbs } from '@shared'
import { ComponentDialogAbstract } from '../../../abstract/component-dialog.abstract'
import { environment } from '@env/environment'
import { TranslateModule } from '@ngx-translate/core'
import { PdfViewerModule } from 'ng2-pdf-viewer'
import { NgIf } from '@angular/common'

@Component({
  selector: 'app-image-url-viewer',
  templateUrl: './image-url-viewer.component.html',
  styleUrls: ['./image-url-viewer.component.scss'],
  standalone: true,
  imports: [MatButtonModule, MatIconModule, MatDialogModule, TranslateModule, PdfViewerModule, NgIf]
})
export class ImageUrlViewerComponent extends ComponentDialogAbstract {
  @Input() src: string | null = null // Nhận URL từ bên ngoài
  @Input() classes = ''

  constructor(protected override injector: Injector) {
    super(injector)
  }

  override initData(): void {
    if (this.src) {
      this.loadImage(this.src)
    }
  }

  private loadImage(url: string) {
    if (this.isValidHttpUrl(url)) {
      // Nếu URL hợp lệ, gán trực tiếp
      this.src = url
    } else {
      // Nếu là đường dẫn API, tải xuống
      this.downloadImage(url)
    }
  }

  private isValidHttpUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url)
      return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:'
    } catch (_) {
      return false
    }
  }

  downloadImage(url: string, params = {}) {
    const options: HttpOptions = {
      url: `${environment.hostApi}`,
      path: url,
      params: params
    }
    this.httpClient
      .download(Verbs.GET, options)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => {})
      )
      .subscribe((res: any) => {
        if (res) {
          const blob = new Blob([res.body], { type: res.body.type })
          this.src = URL.createObjectURL(blob)
        }
      })
  }
}
