import { Component, Inject } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog'
import { TranslateModule } from '@ngx-translate/core'
import { NgIf } from '@angular/common'
import { ImageComponent } from '../../section/image/image.component'
import { MatIconModule } from '@angular/material/icon'
import { MatButtonModule } from '@angular/material/button'
import { ButtonOutlinePrimaryComponent } from '@shared/components/element/button-outline-primary/button-outline-primary.component'

@Component({
  selector: 'mbb-dialog-success',
  templateUrl: './dialog-success.component.html',
  styleUrls: ['./dialog-success.component.scss'],
  standalone: true,
  imports: [MatButtonModule, MatIconModule, MatDialogModule, ImageComponent, NgIf, TranslateModule, ButtonOutlinePrimaryComponent]
})
export class DialogSuccessComponent {
  constructor(
    public dialogRef: MatDialogRef<DialogSuccessComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.dialogRef.disableClose = true
  }

  onCloseConfirm() {
    this.dialogRef.close(true)
  }

  onCloseCancel() {
    this.dialogRef.close(true)
  }
}
