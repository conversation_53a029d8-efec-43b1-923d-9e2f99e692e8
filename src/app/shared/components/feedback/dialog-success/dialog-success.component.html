<div class="d-flex justify-content-end">
  <button [tabIndex]="'-1'" mat-icon-button class="cursor-pointer" (click)="onCloseCancel()">
    <mat-icon class="">close</mat-icon>
  </button>
</div>
<div mat-dialog-content>
  <div class="d-flex justify-content-center">
    <app-image src="assets/images/images-status/success.png" />
  </div>
  <div class="dialog-title">
    <div [innerHTML]="data?.title"></div>
  </div>
  <div class="text-center content" *ngIf="!data?.innerHTML">
    <div [innerHTML]="data?.message"></div>
  </div>
  <p class="text-center" *ngIf="data?.innerHTML" [innerHTML]="data?.innerHTML"></p>
  <p class="text-center text-error" *ngIf="data?.note">{{ data?.note }}</p>
</div>
<div class="line"></div>
<div mat-dialog-actions class="d-flex justify-content-center">
  <app-button-o-primary (click)="onCloseConfirm()">
    {{ 'btn.close' | translate }}
  </app-button-o-primary>
</div>
