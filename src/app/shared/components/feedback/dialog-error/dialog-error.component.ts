import { Component, Inject } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog'
import { TranslateModule } from '@ngx-translate/core'
import { NgIf } from '@angular/common'
import { MatIconModule } from '@angular/material/icon'
import { MatButtonModule } from '@angular/material/button'
import { ButtonOutlinePrimaryComponent } from '@shared/components/element/button-outline-primary/button-outline-primary.component'

@Component({
  selector: 'app-dialog-error',
  templateUrl: './dialog-error.component.html',
  styleUrls: ['./dialog-error.component.scss'],
  standalone: true,
  imports: [
    MatButtonModule, 
    MatIconModule, 
    MatDialogModule, 
    NgIf, 
    TranslateModule, 
    ButtonOutlinePrimaryComponent
  ]
})
export class DialogErrorComponent {
  constructor(
    public dialogRef: MatDialogRef<DialogErrorComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.dialogRef.disableClose = true
  }

  onCloseConfirm() {
    this.dialogRef.close(true)
  }

  onCloseCancel() {
    this.dialogRef.close(true)
  }
}
