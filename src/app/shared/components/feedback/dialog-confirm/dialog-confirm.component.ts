import { Component, Inject } from '@angular/core'
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog'
import { TranslateModule } from '@ngx-translate/core'
import { NgIf } from '@angular/common'
import { MatIconModule } from '@angular/material/icon'
import { MatButtonModule } from '@angular/material/button'

@Component({
  selector: 'mbb-dialog-confirm',
  templateUrl: './dialog-confirm.component.html',
  styleUrls: ['./dialog-confirm.component.scss'],
  standalone: true,
  imports: [MatButtonModule, MatIconModule, MatDialogModule, NgIf, TranslateModule]
})
export class DialogConfirmComponent {
  textButtonLeft = 'btn.cancel'
  textButtonRight = 'btn.accept'
  imageName = 'confirm.png'
  constructor(
    public dialogRef: MatDialogRef<DialogConfirmComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.dialogRef.disableClose = true
    if (data?.textButtonLeft) {
      this.textButtonLeft = data?.textButtonLeft
    }
    if (data?.textButtonRight) {
      this.textButtonRight = data?.textButtonRight
    }
    if (data?.imageName) {
      this.imageName = data?.imageName
    }
  }

  onAcceptConfirm() {
    this.dialogRef.close(true)
  }

  onCloseCancel() {
    this.dialogRef.close(false)
  }
}
