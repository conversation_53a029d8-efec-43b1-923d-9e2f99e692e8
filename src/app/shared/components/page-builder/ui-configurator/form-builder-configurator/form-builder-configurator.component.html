<mat-expansion-panel class="mb-2 mat-elevation-z1" id="form-builder-api-config">
  <mat-expansion-panel-header>
    <mat-panel-title>
      <strong>Cấu hình <PERSON></strong>
    </mat-panel-title>
  </mat-expansion-panel-header>
  <div class="card p-2">
    <div class="form-group">
      <label class="col-4 form-label" for="CREATE"><span class="badge bg-success">API CREATE</span></label>
      <div class="col-8">
        <input class="form-control" id="CREATE" type="text" [(ngModel)]="context.config.data.apiCreate" />
      </div>
    </div>
  </div>
</mat-expansion-panel>
<mat-expansion-panel class="mb-2 mat-elevation-z1" id="form-builder-fields">
  <mat-expansion-panel-header>
    <mat-panel-title>
      <strong>Cấu thông tin form</strong>
    </mat-panel-title>
  </mat-expansion-panel-header>
  <div class="row">
    <div class="col-12">
      <div class="form-floating">
        <input
          class="form-control form-control-sm"
          type="text"
          value="{{ context.config.data.title }}"
          [(ngModel)]="context.config.data.title"
          [ngModelOptions]="{ standalone: true }" />
        <label class="form-check-label">Tên bảng</label>
      </div>
    </div>
  </div>
  <div cdkDropList (cdkDropListDropped)="sortArray($event, 'displayedColumns')">
    <div class="card p-2 mt-2" *ngFor="let c of context.config.data.displayedColumns; let i = index">
      <div class="d-flex gap-1">
        <div class="col-6">
          <div class="form-floating">
            <input
              class="form-control form-control-sm"
              type="text"
              id="columnName-{{ i }}"
              value="{{ c.name }}"
              [(ngModel)]="context.config.data.displayedColumns[i].name"
              [ngModelOptions]="{ standalone: true }" />
            <label class="form-check-label" for="columnName-{{ i }}">Hiển thị</label>
          </div>
        </div>
        <div class="col-6">
          <div class="form-floating">
            <input
              class="form-control form-control-sm"
              type="text"
              id="columnField-{{ i }}"
              value="{{ c.field }}"
              [(ngModel)]="context.config.data.displayedColumns[i].field"
              [ngModelOptions]="{ standalone: true }" />
            <label class="form-check-label" for="columnField-{{ i }}">Thuộc tính</label>
          </div>
        </div>
      </div>
      <div class="d-flex gap-1 mt-2">
        <div class="col-6">
          <div class="form-floating">
            <input
              class="form-control form-control-sm"
              type="text"
              id="columnPath-{{ i }}"
              value="{{ c.path }}"
              [(ngModel)]="context.config.data.displayedColumns[i].path"
              [ngModelOptions]="{ standalone: true }" />
            <label class="form-check-label" for="columnPath-{{ i }}" uib-tooltip="Object property trong mảng json trả về">Json Path</label>
          </div>
        </div>
        <div class="col-6 form-control-sm">
          <div class="mrl-2 form-check">
            <input
              class="form-check-input"
              type="checkbox"
              id="columnShow-{{ i }}"
              [(ngModel)]="context.config.data.displayedColumns[i].show"
              [ngModelOptions]="{ standalone: true }" />
            <label class="form-check-label" for="columnShow-{{ i }}">Hiển thị</label>
          </div>
        </div>
      </div>
      <div class="d-flex gap-1 mt-2">
        <div class="col-6">
          <div class="form-floating">
            <select [(ngModel)]="context.config.data.displayedColumns[i].type" id="type-{{ i }}" class="form-select form-control-sm">
              <option value="textbox">Text</option>
              <option value="checkbox">CheckBox</option>
              <option value="radio">Radio</option>
              <option value="date">Date</option>
              <option value="datetime">DateTime</option>
              <option value="textarea">TextArea</option>
              <option value="ngselect">SelectBox</option>
              <option value="hidden">Hidden</option>
              <option value="map">Map array (Cần mapField)</option>
            </select>
            <label class="form-check-label" for="type-{{ i }}" uib-tooltip="Kiểu dữ liệu hiển thị">Kiểu hiển thị</label>
          </div>
        </div>
        <div class="col-6 form-control-sm"></div>
      </div>
      <div class="d-flex justify-content-end mt-1">
        <div class="d-flex flex-row align-items-center mr-2">
          <button
            cdkDrag
            *ngIf="i > 0"
            class="btn btn-sm btn-outline-secondary border-0 mr-2"
            (click)="sortArray({ previousIndex: i, currentIndex: i - 1 }, 'displayedColumns')"
            uib-tooltip="Di chuyển lên">
            <svg class="icon text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 8">
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 7 7.674 1.3a.91.91 0 0 0-1.348 0L1 7" />
            </svg>
          </button>
          <button
            cdkDrag
            class="btn btn-sm btn-outline-secondary border-0"
            (click)="sortArray({ previousIndex: i, currentIndex: i + 1 }, 'displayedColumns')"
            uib-tooltip="Di chuyển xuống">
            <svg class="icon text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 8">
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="m1 1 5.326 5.7a.909.909 0 0 0 1.348 0L13 1" />
            </svg>
          </button>
        </div>
        <div class="d-flex flex-row align-items-center">
          <button class="btn btn-sm btn-outline-danger border-0" (click)="removeElementArray(i, 'displayedColumns')" uib-tooltip="Xóa Cột">
            <svg class="icon text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 20">
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M1 5h16M7 8v8m4-8v8M7 1h4a1 1 0 0 1 1 1v3H6V2a1 1 0 0 1 1-1ZM3 5h12v13a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V5Z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="d-flex justify-content-end mt-2 mb-2">
    <button class="btn btn-sm btn-outline-primary" (click)="addColumn()" uib-tooltip="Thêm mới">Thêm</button>
  </div>
</mat-expansion-panel>
<app-table-action-column-configurator [context]="context"></app-table-action-column-configurator>
