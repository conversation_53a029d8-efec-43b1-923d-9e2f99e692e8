import { CdkDrag, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop'
import { Component, Input, OnChanges, SimpleChanges } from '@angular/core'
import { ComponentConfig, ConfiguratorContext, TooltipDirective } from '@tnx/ngx-ui-builder'
import { TableActionColumnConfiguratorComponent } from '../table-configurator/table-action-column-configurator/table-action-column-configurator.component'
import { NgFor, NgIf } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { MatExpansionModule } from '@angular/material/expansion'

export interface columnTable {
  name?: string
  field?: string
  path?: string
  type?: string
  show?: boolean
}

@Component({
  selector: 'app-form-builder-configurator',
  templateUrl: './form-builder-configurator.component.html',
  styleUrls: ['./form-builder-configurator.component.scss'],
  standalone: true,
  imports: [FormsModule, CdkDropList, NgFor, TooltipDirective, NgIf, CdkDrag, TableActionColumnConfiguratorComponent, MatExpansionModule]
})
export class FormBuilderConfiguratorComponent implements OnChanges {
  @Input() context: ConfiguratorContext

  constructor() {}
  get config(): ComponentConfig {
    return this.context.config
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!this.config?.data) {
      this.config['data'] = {}
    }
    // khởi tạo cấu hình mặc định
    if (!this.config?.data?.displayedColumns) {
      this.addColumn()
    }
    if (!this.config?.data?.quickSearchFields) {
      this.addFieldQuickSearch()
    }
  }

  configChanged() {
    this.context.configChanged()
  }

  onChangeType($event: Event) {}

  addColumn() {
    const column: columnTable = {
      name: 'Tên cột ' + new Date().getTime(),
      field: 'field_' + new Date().getTime(),
      path: 'object.property',
      show: true
    }
    this.config.data['displayedColumns'] = [...(this.config?.data?.displayedColumns || []), column]
  }

  removeElementArray(i, key) {
    this.config?.data[key].splice(i, 1)
  }

  /**
   * search nhanh
   * @param event
   */
  sortArray(event: any, key: string) {
    if (this.config?.data[key]) {
      moveItemInArray(this.config?.data[key], event.previousIndex, event.currentIndex)
    }
  }

  removeQuickSearchFields(i) {
    this.config?.data?.quickSearchFields.splice(i, 1)
  }

  addFieldQuickSearch() {
    const field = {
      key: 'field_' + new Date().getTime(),
      text: 'Hiển thị'
    }
    this.config.data['quickSearchFields'] = [...(this.config?.data?.quickSearchFields || []), field]
  }

  /**
   * search nâng cao
   * @param event
   */

  removeAdvancedSearchFields(i) {
    this.config?.data?.advancedSearchFields.splice(i, 1)
  }

  addFieldAdvancedSearch() {
    const field = {
      name: 'Tên cột ' + new Date().getTime(),
      field: 'field_' + new Date().getTime(),
      show: true
    }
    this.config.data['advancedSearchFields'] = [...(this.config?.data?.advancedSearchFields || []), field]
  }

  /**
   * Button
   */

  addButton(key) {
    const button = {
      title: '',
      type: '',
      class: ''
    }
    this.config.data[key] = [...(this.config?.data?.buttonLists || []), button]
  }
}
