<mat-expansion-panel class="mb-2 mat-elevation-z1" id="spacing-options" [expanded]="true">
  <mat-expansion-panel-header>
    <mat-panel-title>
      <strong>Thông tin trường</strong>
    </mat-panel-title>
  </mat-expansion-panel-header>
  <div class="card p-2">
    <div class="form-group">
      <label class="col-4 form-label" for="controlType">Loại</label>
      <div class="col-8">
        <select [(ngModel)]="context.config.data.controlType" id="controlType" class="form-select" (ngModelChange)="configChanged()">
          <option value="textbox">Text</option>
          <option value="textboxlink">TextLink</option>
          <option value="number">Number</option>
          <option value="checkbox">CheckBox</option>
          <option value="radio">Radio</option>
          <option value="slide">SlideItem</option>
          <option value="date">Date</option>
          <option value="monthyear">MonthYear</option>
          <option value="datetime">DateTime</option>
          <option value="daterange">DateRange</option>
          <option value="time">Time</option>
          <option value="textarea">TextArea</option>
          <option value="ngselect">SelectBox</option>
          <option value="treeselect">TreeSelect</option>
          <option value="hidden">Hidden</option>
          <option value="rating">Rating</option>
        </select>
      </div>
    </div>
    <div class="form-group">
      <label class="col-4 form-label" for="key">Tên</label>
      <div class="col-8">
        <input class="form-control" id="key" type="text" [(ngModel)]="context.config.data.key" (ngModelChangeDebounced)="configChanged()" />
      </div>
    </div>
    <div class="form-group">
      <label class="col-4 form-label" for="label">Hiển thị</label>
      <div class="col-8">
        <input class="form-control" id="label" type="text" [(ngModel)]="context.config.data.label" (ngModelChangeDebounced)="configChanged()" />
      </div>
    </div>
    <div class="form-group">
      <label class="col-4 form-label" for="requiredMessage">requiredMessage</label>
      <div class="col-8">
        <input
          class="form-control"
          id="requiredMessage"
          type="text"
          [(ngModel)]="context.config.data.requiredMessage"
          (ngModelChangeDebounced)="configChanged()" />
      </div>
    </div>
    <div class="form-group">
      <label class="col-4 form-label" for="read-only">Chỉ xem</label>
      <div class="col-8">
        <input id="read-only" type="checkbox" [(ngModel)]="context.config.data.readOnly" (change)="configChanged()" />
      </div>
    </div>
    <div class="form-group">
      <label class="col-4 form-label" for="edit-mode">Chế độ sửa</label>
      <div class="col-8">
        <input id="edit-mode" type="checkbox" [(ngModel)]="context.config.data.editMode" (change)="configChanged()" />
      </div>
    </div>
    <div class="form-group">
      <label class="col-4 form-label" for="value">Giá trị</label>
      <div class="col-8">
        <input class="form-control" id="value" type="text" [(ngModel)]="context.config.data.value" (ngModelChangeDebounced)="configChanged()" />
      </div>
    </div>
    <app-date-range-config *ngIf="context.config.data.controlType === FORM_CONTROL_TYPE.DATERANGE" [context]="context"></app-date-range-config>
    <div class="form-group" *ngIf="context.config.data.controlType === FORM_CONTROL_TYPE.CHECKBOX">
      <label class="col-4 form-label" for="hideValueCheckBox">hideValueCheckBox</label>
      <div class="col-8">
        <input id="hideValueCheckBox" type="checkbox" [(ngModel)]="context.config.data.hideValueCheckBox" (change)="configChanged()" />
      </div>
    </div>
    <div class="form-group">
      <label class="col-4 form-label" for="defaultValue">Giá trị mặc định</label>
      <div class="col-8">
        <input
          class="form-control"
          id="defaultValue"
          type="text"
          [(ngModel)]="context.config.data.defaultValue"
          (ngModelChangeDebounced)="configChanged()" />
      </div>
    </div>
    <div class="form-group">
      <label class="col-4 form-label" for="tooltipMessage">Tooltip Message</label>
      <div class="col-8">
        <input
          class="form-control"
          id="tooltipMessage"
          type="text"
          [(ngModel)]="context.config.data.tooltipMessage"
          (ngModelChangeDebounced)="configChanged()" />
      </div>
    </div>
    <div class="form-group">
      <label class="col-4 form-label" for="placeHolder">Placeholder</label>
      <div class="col-8">
        <input
          class="form-control"
          id="placeHolder"
          type="text"
          [(ngModel)]="context.config.data.placeholder"
          (ngModelChangeDebounced)="configChanged()" />
      </div>
    </div>
    <div class="form-group">
      <label class="col-4 form-label" for="placeHolderSearch">Placeholder search</label>
      <div class="col-8">
        <input
          class="form-control"
          id="placeHolderSearch"
          type="text"
          [(ngModel)]="context.config.data.placeHolderSearch"
          (ngModelChangeDebounced)="configChanged()" />
      </div>
    </div>
    <div class="form-group">
      <label class="col-4 form-label">Bắt buộc</label>
      <div class="col-8">
        <input id="required" type="checkbox" [(ngModel)]="context.config.data.required" (change)="configChanged()" />
      </div>
    </div>
    <ng-container
      *ngIf="context.config.data.controlType === FORM_CONTROL_TYPE.TEXT_BOX || context.config.data.controlType === FORM_CONTROL_TYPE.TEXTAREA">
      <div class="form-group">
        <label class="col-4 form-label" for="maxLength">Giới hạn ký tự</label>
        <div class="col-8">
          <input
            class="form-control"
            id="maxLength"
            type="number"
            [(ngModel)]="context.config.data.maxLength"
            (ngModelChangeDebounced)="configChanged()" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label" for="minRow">minRow</label>
        <div class="col-8">
          <input class="form-control" id="minRow" type="number" [(ngModel)]="context.config.data.minRow" (ngModelChangeDebounced)="configChanged()" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label" for="pattern">Pattern</label>
        <div class="col-8">
          <input
            class="form-control"
            id="pattern"
            type="string"
            [(ngModel)]="context.config.data.pattern"
            (ngModelChangeDebounced)="configChanged()" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label" for="directives">Directives (Chặn kí tự theo format)</label>
        <div class="col-8">
          <input
            class="form-control"
            id="directives"
            type="string"
            [(ngModel)]="context.config.data.directives"
            (ngModelChangeDebounced)="configChanged()" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label" for="inputType">Input Type</label>
        <div class="col-8">
          <select [(ngModel)]="context.config.data.type" id="inputType" class="form-select" (ngModelChange)="configChanged()">
            <option value="text">Text</option>
            <option value="password">Password</option>
            <option value="number">Number</option>
            <option value="email">Email</option>
          </select>
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label" for="countMaxLength" uib-tooltip="Người dùng được phép nhập quá nhưng báo lỗi">Count MaxLength</label>
        <div class="col-8">
          <input id="countMaxLength" type="checkbox" [(ngModel)]="context.config.data.countMaxLength" (change)="configChanged()" />
        </div>
      </div>
    </ng-container>
    <div class="form-group">
      <label class="col-4 form-label" for="clearable" uib-tooltip="Hiển thị button xóa">Clearable</label>
      <div class="col-8">
        <input id="clearable" type="checkbox" [(ngModel)]="context.config.data.clearable" (change)="configChanged()" />
      </div>
    </div>
    <div class="form-group">
      <label class="col-4 form-label">Customer Error Key</label>
      <div class="col-8">
        <ng-select
          [items]="['customMessageError']"
          [multiple]="true"
          [addTag]="true"
          [(ngModel)]="context.config.data.customValidate"
          (change)="configChanged()"
          placeholder="Nhập tên key"></ng-select>
      </div>
    </div>
    <ng-container
      *ngIf="
        context.config.data.controlType === FORM_CONTROL_TYPE.DROPDOWN ||
        context.config.data.controlType === FORM_CONTROL_TYPE.CHECKBOX ||
        context.config.data.controlType === FORM_CONTROL_TYPE.TREE_DROPDOWN
      ">
      <div class="form-group">
        <label class="col-4 form-label" for="type">Tùy chọn</label>
        <div class="col-8">
          <select [(ngModel)]="context.config.data.type" id="type" class="form-select" (ngModelChange)="configChanged()">
            <option selected value="single">single</option>
            <option value="multiple">multiple</option>
            <option value="multiple_has_child">multiple has child</option>
          </select>
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label">Option</label>
        <div class="col-8">
          <app-form-control-option [items]="context.config.data.options" (changed)="onOptionChanged($event)"></app-form-control-option>
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label" for="select-api">API lấy dữ liệu</label>
        <div class="col-8">
          <input
            class="form-control"
            id="select-api"
            placeholder="ex: api/v1/users"
            type="text"
            [(ngModel)]="context.config.data.paramData.url"
            (change)="configChanged()" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label" for="select-key">Option Key</label>
        <div class="col-8">
          <input
            class="form-control"
            id="select-key"
            placeholder="ex: name"
            type="text"
            [(ngModel)]="context.config.data.paramData.key"
            (change)="configChanged()" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label" for="select-value">Option Value</label>
        <div class="col-8">
          <input
            class="form-control"
            id="select-value"
            placeholder='["code", "name"]'
            type="text"
            [(ngModel)]="context.config.data.paramData.value"
            (change)="configChanged()" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label" for="select-subvalue">Option SubValue</label>
        <div class="col-8">
          <input
            class="form-control"
            id="select-subvalue"
            placeholder='["code", "name"]'
            type="text"
            [(ngModel)]="context.config.data.paramData.subValue"
            (change)="configChanged()" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label" for="typeheadKey" uib-tooltip="Key tìm kiếm typehead">Typehead key</label>
        <div class="col-8">
          <input
            class="form-control"
            id="typeheadKey"
            type="text"
            [(ngModel)]="context.config.data.paramData.typeheadKey"
            (change)="configChanged()" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label" for="levelKey" uib-tooltip="Level property">Level property</label>
        <div class="col-8">
          <input
            class="form-control"
            id="levelKey"
            type="text"
            [(ngModel)]="context.config.data.paramData.levelProperty"
            (change)="configChanged()" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label" for="select-term" uib-tooltip="Query String">Query String First Call</label>
        <div class="col-8">
          <input
            class="form-control"
            id="select-term"
            type="text"
            [(ngModel)]="context.config.data.paramData.queryStringOnInit"
            (change)="configChanged()" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label" for="auto-call-api">First load api</label>
        <div class="col-8">
          <input id="auto-call-api" type="checkbox" [(ngModel)]="context.config.data.paramData.preLoad" (change)="configChanged()" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label" for="client-filter-text">Client filter text</label>
        <div class="col-8">
          <input id="client-filter-text" type="checkbox" [(ngModel)]="context.config.data.paramData.clientFilter" (change)="configChanged()" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label" for="client-filter-text">Ẩn search trong tree select</label>
        <div class="col-8">
          <input id="disable-search-in-select" type="checkbox" [(ngModel)]="context.config.data.isHiddenSearch" (change)="configChanged()" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label" for="client-filter-text">Enable item condition</label>
        <div class="col-8">
          <app-query-builder class="flex-100" [(ngModel)]="context.config.data.paramData.itemConditionEnable"></app-query-builder>
        </div>
      </div>
      <div class="form-group">
        <label class="col-4 form-label">Phân quyền hiển thị (Keycloak)</label>
        <div class="col-8">
          <ng-select
            [multiple]="true"
            [(ngModel)]="context.config.data.requiredRoles"
            [items]="keycloakRoles"
            (change)="configChanged()"
            placeholder="Chọn roles được phép xem">
          </ng-select>
        </div>
      </div>
      <!--      <div class="form-group">-->
      <!--        <label class="col-4 form-label">Search Fields</label>-->
      <!--        <div class="col-8">-->
      <!--          <ng-select [multiple]="true"-->
      <!--                     [addTag]="true"-->
      <!--                     [(ngModel)]="context.config.data.searchFields"-->
      <!--                     (change)="configChanged()"-->
      <!--                     placeholder="Nhập tên key">-->
      <!--          </ng-select>-->
      <!--        </div>-->
      <!--      </div>-->
    </ng-container>
  </div>
</mat-expansion-panel>
