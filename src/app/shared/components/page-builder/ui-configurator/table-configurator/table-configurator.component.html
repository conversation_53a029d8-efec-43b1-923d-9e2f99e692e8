<mat-expansion-panel class="mb-2 mat-elevation-z1" id="spacing-options">
  <mat-expansion-panel-header>
    <mat-panel-title>
      <strong>Cấu hình API</strong>
    </mat-panel-title>
  </mat-expansion-panel-header>
  <div class="card p-2">
    <div class="form-group">
      <label class="col-4 form-label" for="LIST"><span class="badge bg-primary">API LIST</span></label>
      <div class="col-8">
        <input class="form-control" id="LIST" type="text" [(ngModel)]="context.config.data.apiList" (ngModelChangeDebounced)="configChanged()" />
      </div>
    </div>
    <!--    <div class="form-group">-->
    <!--      <label class="col-4 form-label" for="CREATE"><span class="badge bg-success">API CREATE</span></label>-->
    <!--      <div class="col-8">-->
    <!--        <input class="form-control" id="CREATE" type="text" [(ngModel)]="context.config.data.apiCreate" (ngModelChangeDebounced)="configChanged()"/>-->
    <!--      </div>-->
    <!--    </div>-->
    <!--    <div class="form-group">-->
    <!--      <label class="col-4 form-label" for="DETAIL"><span class="badge bg-secondary">API DETAIL</span></label>-->
    <!--      <div class="col-8">-->
    <!--        <input class="form-control" id="DETAIL" type="text" [(ngModel)]="context.config.data.apiDetail" (ngModelChangeDebounced)="configChanged()"/>-->
    <!--      </div>-->
    <!--    </div>-->
    <!--    <div class="form-group">-->
    <!--      <label class="col-4 form-label" for="UPDATE"><span class="badge bg-warning">API UPDATE</span></label>-->
    <!--      <div class="col-8">-->
    <!--        <input class="form-control" id="UPDATE" type="text" [(ngModel)]="context.config.data.apiUpdate" (ngModelChangeDebounced)="configChanged()"/>-->
    <!--      </div>-->
    <!--    </div>-->
    <div class="form-group">
      <label class="col-4 form-label" for="DELETE"><span class="badge bg-danger">API DELETE</span></label>
      <div class="col-8">
        <input class="form-control" id="DELETE" type="text" [(ngModel)]="context.config.data.apiDelete" (ngModelChangeDebounced)="configChanged()" />
      </div>
    </div>
    <div class="form-group">
      <label class="col-4 form-label" for="listMethod"><span>API LIST METHOD</span></label>
      <div class="col-8">
        <select [(ngModel)]="context.config.data.apiListMethod" id="listMethod" class="form-select form-control-sm">
          <option value="GET" selected>GET</option>
          <option value="POST">POST</option>
        </select>
      </div>
    </div>
    <div class="form-group">
      <label class="col-4 form-label" for="pageSize">Số bản ghi/Trang</label>
      <div class="col-8">
        <input
          class="form-control"
          id="pageSize"
          type="number"
          [(ngModel)]="context.config.data.pageSize"
          (ngModelChangeDebounced)="configChanged()" />
      </div>
    </div>
  </div>
</mat-expansion-panel>
<mat-expansion-panel class="mb-2 mat-elevation-z1">
  <mat-expansion-panel-header>
    <mat-panel-title>
      <strong>Cấu hình bảng</strong>
    </mat-panel-title>
  </mat-expansion-panel-header>
  <div class="row mb-2">
    <div class="col-12">
      <app-custom-input-setting
        label="Tên bảng"
        [(ngModel)]="context.config.data.tableTitle"
        [ngModelOptions]="{ standalone: true }"
        (ngModelChangeDebounced)="configChanged()"></app-custom-input-setting>
    </div>
    <div class="col-12 mt-1">
      <app-custom-input-setting
        label="Children json path expand table"
        [(ngModel)]="context.config.data.childrenAttr"
        [ngModelOptions]="{ standalone: true }"
        (ngModelChangeDebounced)="configChanged()"></app-custom-input-setting>
    </div>
    <div class="col-6 p-1">
      <div class="mrl-3 form-check">
        <input
          class="form-check-input"
          type="checkbox"
          id="isTreeData"
          [(ngModel)]="context.config.data.isTreeData"
          [ngModelOptions]="{ standalone: true }" />
        <label class="form-check-label" for="isTreeData">Tree table</label>
      </div>
      <div class="mrl-3 form-check">
        <input
          class="form-check-input"
          type="checkbox"
          id="isStaticTable"
          [(ngModel)]="context.config.data.isStaticTable"
          [ngModelOptions]="{ standalone: true }" />
        <label class="form-check-label" for="isStaticTable">Static table</label>
      </div>
      <div class="mrl-3 form-check">
        <input
          class="form-check-input"
          type="checkbox"
          id="hideSearchAdvanced"
          [(ngModel)]="context.config.data.hideSearchAdvanced"
          [ngModelOptions]="{ standalone: true }" />
        <label class="form-check-label" for="hideSearchAdvanced">Hide Search Advanced</label>
      </div>
      <div class="mrl-3 form-check">
        <input
          class="form-check-input"
          type="checkbox"
          id="noPaging"
          [(ngModel)]="context.config.data.noPaging"
          [ngModelOptions]="{ standalone: true }" />
        <label class="form-check-label" for="noPaging">No paging</label>
      </div>
      <div class="mrl-3 form-check">
        <input
          class="form-check-input"
          type="checkbox"
          id="disableAutoCallOnChange"
          [(ngModel)]="context.config.data.disableAutoCallOnChange"
          [ngModelOptions]="{ standalone: true }" />
        <label class="form-check-label" for="disableAutoCallOnChange">disableAutoCallOnChange</label>
      </div>
      <div class="mrl-3 form-check">
        <input
          class="form-check-input"
          type="checkbox"
          id="checkBoxOnePage"
          [(ngModel)]="context.config.data.checkBoxOnePage"
          [ngModelOptions]="{ standalone: true }" />
        <label uib-tooltip="Chỉ checkall trên page hiện tại" class="form-check-label" for="checkBoxOnePage">checkBoxOnePage</label>
      </div>
      <!--          <div class="mrl-2 form-check">-->
      <!--            <input class="form-check-input" type="checkbox" id="columnShowQuickSearch-{{i}}"-->
      <!--                   [(ngModel)]="context.config.data[displayColumnsProperty][i].isQuickSearch" [ngModelOptions]="{standalone: true}" (ngModelChangeDebounced)="configChanged()">-->
      <!--            <label class="form-check-label" for="columnShowQuickSearch-{{i}}">Tìm kiếm nhanh</label>-->
      <!--          </div>-->
      <!--          <div class="mrl-2 form-check">-->
      <!--            <input class="form-check-input" type="checkbox" id="columnShowAdvancedSearch-{{i}}"-->
      <!--                   [(ngModel)]="context.config.data[displayColumnsProperty][i].isAdvancedSearch" [ngModelOptions]="{standalone: true}" (ngModelChangeDebounced)="configChanged()">-->
      <!--            <label class="form-check-label" for="columnShowAdvancedSearch-{{i}}">Tìm kiếm nâng cao</label>-->
      <!--          </div>-->
    </div>
  </div>
</mat-expansion-panel>
<app-table-column-configurator [context]="context" name="Columns" displayColumnsProperty="displayedColumns"></app-table-column-configurator>
<!--<app-table-column-configurator [context]="context" name="Expand Columns" displayColumnsProperty="expandColumns"></app-table-column-configurator>-->
<app-table-action-column-configurator [context]="context"></app-table-action-column-configurator>
<mat-expansion-panel class="mb-2 mat-elevation-z1" id="quickSearch">
  <mat-expansion-panel-header>
    <mat-panel-title>
      <strong>Cấu hình tìm kiếm nhanh</strong>
    </mat-panel-title>
  </mat-expansion-panel-header>
  <div cdkDropList (cdkDropListDropped)="sortArray($event, 'quickSearchFields')">
    <div class="card p-2 mt-2" *ngFor="let c of context.config.data.quickSearchFields; let i = index">
      <div class="d-flex gap-1">
        <div class="col-4">
          <div class="form-floating">
            <input
              class="form-control form-control-sm"
              type="text"
              id="keywordSearch-{{ i }}"
              value="{{ c.key }}"
              [(ngModel)]="context.config.data.quickSearchFields[i].key"
              [ngModelOptions]="{ standalone: true }" />
            <label class="form-check-label" for="keywordSearch-{{ i }}">Mã</label>
          </div>
        </div>
        <div class="col-4">
          <div class="form-floating">
            <input
              class="form-control form-control-sm"
              type="text"
              id="text-{{ i }}"
              value="{{ c.text }}"
              [(ngModel)]="context.config.data.quickSearchFields[i].text"
              [ngModelOptions]="{ standalone: true }" />
            <label class="form-check-label" for="text-{{ i }}">Hiển thị</label>
          </div>
        </div>
        <div class="col-4">
          <div class="form-floating">
            <input
              class="form-control form-control-sm"
              type="text"
              id="text-{{ i }}"
              value="{{ c.text }}"
              [(ngModel)]="context.config.data.quickSearchFields[i].placeHolder"
              [ngModelOptions]="{ standalone: true }" />
            <label class="form-check-label" for="text-{{ i }}">PlaceHolder</label>
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-end mt-1">
        <div class="d-flex flex-row align-items-center mr-2">
          <button
            cdkDrag
            *ngIf="i > 0"
            class="btn btn-sm btn-outline-secondary border-0 mr-2"
            (click)="sortArray({ previousIndex: i, currentIndex: i - 1 }, 'quickSearchFields')"
            uib-tooltip="Di chuyển lên">
            <svg class="icon text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 8">
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 7 7.674 1.3a.91.91 0 0 0-1.348 0L1 7" />
            </svg>
          </button>
          <button
            cdkDrag
            class="btn btn-sm btn-outline-secondary border-0"
            (click)="sortArray({ previousIndex: i, currentIndex: i + 1 }, 'quickSearchFields')"
            uib-tooltip="Di chuyển xuống">
            <svg class="icon text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 8">
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="m1 1 5.326 5.7a.909.909 0 0 0 1.348 0L13 1" />
            </svg>
          </button>
        </div>
        <div class="d-flex flex-row align-items-center">
          <button class="btn btn-sm btn-outline-danger border-0" (click)="removeQuickSearchFields(i)" uib-tooltip="Xóa Cột">
            <svg class="icon text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 20">
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M1 5h16M7 8v8m4-8v8M7 1h4a1 1 0 0 1 1 1v3H6V2a1 1 0 0 1 1-1ZM3 5h12v13a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V5Z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="d-flex justify-content-end mt-2 mb-2">
    <button class="btn btn-sm btn-outline-primary" (click)="addFieldQuickSearch()" uib-tooltip="Thêm field mới">Thêm mới</button>
  </div>
</mat-expansion-panel>
<mat-expansion-panel class="mb-2 mat-elevation-z1" id="buttonLists">
  <mat-expansion-panel-header>
    <mat-panel-title>
      <strong>Cấu hình danh sách nút</strong>
    </mat-panel-title>
  </mat-expansion-panel-header>
  <div cdkDropList (cdkDropListDropped)="sortArray($event, 'buttonLists')">
    <div class="card p-2 mt-2" *ngFor="let c of context.config.data.buttonLists; let i = index">
      <div class="d-flex gap-1 mt-2">
        <div class="col-6">
          <div class="form-floating" uib-tooltip="Tên hiển thị">
            <input
              class="form-control form-control-sm"
              type="text"
              id="btnTitle-{{ i }}"
              value="{{ c.title }}"
              [(ngModel)]="context.config.data.buttonLists[i].title"
              [ngModelOptions]="{ standalone: true }"
              (ngModelChangeDebounced)="configChanged()" />
            <label class="form-check-label" for="btnTitle-{{ i }}">Tên</label>
          </div>
        </div>
        <div class="col-6">
          <div class="form-floating" uib-tooltip="Kiểu để bạn xử lý logic Ex: Create, Update, etc">
            <input
              class="form-control form-control-sm"
              type="text"
              id="btnType-{{ i }}"
              value="{{ c.type }}"
              [(ngModel)]="context.config.data.buttonLists[i].type"
              [ngModelOptions]="{ standalone: true }"
              (ngModelChangeDebounced)="configChanged()" />
            <label class="form-check-label" for="btnType-{{ i }}">Hành động</label>
          </div>
        </div>
      </div>
      <div class="d-flex gap-1 mt-2">
        <div class="col-6">
          <div class="form-floating" uib-tooltip="Kiểu màu sắc hiển thị">
            <select [(ngModel)]="context.config.data.buttonLists[i].class" id="btnStyle-{{ i }}" class="form-select form-control-sm">
              <option value="btn-primary">Primary</option>
              <option value="btn-light-blue">Light</option>
              <option value="btn-success">Success</option>
              <option value="btn-dark">Dark</option>
            </select>
            <label class="form-check-label" for="btnStyle-{{ i }}">Kiểu hiển thị</label>
          </div>
        </div>
        <div class="col-6">
          <div class="form-floating" uib-tooltip="Biểu tượng của nút: Ex add">
            <input
              class="form-control form-control-sm"
              type="text"
              id="btnIcon-{{ i }}"
              value="{{ c.icon }}"
              [(ngModel)]="context.config.data.buttonLists[i].icon"
              [ngModelOptions]="{ standalone: true }"
              (ngModelChangeDebounced)="configChanged()" />
            <label class="form-check-label" for="btnIcon-{{ i }}">Icon</label>
          </div>
        </div>
      </div>
      <div class="d-flex gap-1 mt-2">
        <div class="col-6">
          <div class="form-floating" uib-tooltip="Xử lý sự kiện">
            <select [(ngModel)]="context.config.data.buttonLists[i].navigationType" id="btnHandlerEvent-{{ i }}" class="form-select form-control-sm">
              <option value="nav">Điều hướng tới màn hình</option>
              <option value="popup">Mở popup</option>
              <option value="emit">Bắn sự kiện để tự xử lý</option>
              <option value="export-excel">Export Excel</option>
              <option value="import-excel">Import Excel</option>
            </select>
            <label class="form-check-label" for="btnHandlerEvent-{{ i }}">Loại sự kiện</label>
          </div>
        </div>
        <div class="col-6">
          <div class="form-floating" uib-tooltip="Router name">
            <input
              class="form-control form-control-sm"
              type="text"
              id="routerName-{{ i }}"
              value="{{ c.routerName }}"
              [(ngModel)]="context.config.data.buttonLists[i].routerName"
              [ngModelOptions]="{ standalone: true }"
              (ngModelChangeDebounced)="configChanged()" />
            <label class="form-check-label" for="routerName-{{ i }}">Tên router</label>
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-end mt-1">
        <div class="d-flex flex-row align-items-center mr-2">
          <button
            cdkDrag
            *ngIf="i > 0"
            class="btn btn-sm btn-outline-secondary border-0 mr-2"
            (click)="sortArray({ previousIndex: i, currentIndex: i - 1 }, 'buttonLists')"
            uib-tooltip="Di chuyển lên">
            <svg class="icon text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 8">
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 7 7.674 1.3a.91.91 0 0 0-1.348 0L1 7" />
            </svg>
          </button>
          <button
            cdkDrag
            class="btn btn-sm btn-outline-secondary border-0"
            (click)="sortArray({ previousIndex: i, currentIndex: i + 1 }, 'buttonLists')"
            uib-tooltip="Di chuyển xuống">
            <svg class="icon text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 8">
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="m1 1 5.326 5.7a.909.909 0 0 0 1.348 0L13 1" />
            </svg>
          </button>
        </div>
        <div class="d-flex flex-row align-items-center">
          <button class="btn btn-sm btn-outline-danger border-0" (click)="removeElementArray(i, 'buttonLists')" uib-tooltip="Xóa">
            <svg class="icon text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 20">
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M1 5h16M7 8v8m4-8v8M7 1h4a1 1 0 0 1 1 1v3H6V2a1 1 0 0 1 1-1ZM3 5h12v13a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V5Z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="d-flex justify-content-end mt-2 mb-2">
    <button class="btn btn-sm btn-outline-primary" (click)="addButton('buttonLists')" uib-tooltip="Thêm mới">Thêm</button>
  </div>
</mat-expansion-panel>