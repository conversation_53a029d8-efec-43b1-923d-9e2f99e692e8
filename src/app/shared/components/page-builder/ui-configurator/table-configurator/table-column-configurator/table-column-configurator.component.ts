import { CdkDrag, CdkDragHandle, CdkDragPlaceholder, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop'
import { Component, Input, OnChanges, SimpleChanges } from '@angular/core'
import { ComponentConfig, ConfiguratorContext, NgModelChangeDebouncedDirective, TooltipDirective } from '@tnx/ngx-ui-builder'
import { columnTable } from '../table-configurator.component'
import { MatExpansionModule } from '@angular/material/expansion'
import { NgFor, NgIf } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { CustomInputSettingComponent } from '@shared/components/page-builder/ui-configurator/custom-input-setting/custom-input-setting.component'
import { NgSelectModule } from '@ng-select/ng-select'
import { FORMAT_DATE_TIME, FORMAT_DATE_TIME_M } from '../../../../../utils'
import { TablerIconsModule } from 'angular-tabler-icons'

@Component({
  selector: 'app-table-column-configurator',
  templateUrl: './table-column-configurator.component.html',
  styleUrls: ['./table-column-configurator.component.scss'],
  standalone: true,
  imports: [
    TooltipDirective,
    FormsModule,
    CdkDropList,
    NgFor,
    MatExpansionModule,
    CdkDrag,
    CdkDragPlaceholder,
    CdkDragHandle,
    NgIf,
    NgModelChangeDebouncedDirective,
    CustomInputSettingComponent,
    NgSelectModule,
    TablerIconsModule
  ]
})
export class TableColumnConfiguratorComponent implements OnChanges {
  @Input() context: ConfiguratorContext

  @Input() name = 'Config'
  @Input() displayColumnsProperty = 'displayedColumns'

  // JSON input properties
  showJsonInput = false;
  jsonInputValue = '';
  jsonError = '';

  // Object to columns properties
  showObjectInput = false;
  objectInputValue = '';
  objectError = '';

  statusClasses = [
    'app-status-inprocess',
    'app-status-approved',
    'app-status-waiting',
    'app-status-warning',
    'app-status-reject',
    'app-status-cancel',
    'app-status-unlock',
    'bg-gray-50',
    'bg-gray-100',
    'bg-gray-200',
    'bg-gray-300',
    'bg-gray-400',
    'bg-gray-500',
    'bg-gray-600',
    'bg-gray-700',
    'bg-gray-900',
    'bg-error',
    'bg-success',
    'bg-light-success',
    'bg-success-100',
    'bg-warning',
    'bg-warning-100',
    'bg-disabled',
    'bg-primary',
    'bg-dark-blue',
    'bg-light-blue',
    'bg-light-error',
    'bg-white-error',
    'bg-neutral-black'
  ]

  constructor() {}
  get config(): ComponentConfig {
    return this.context.config
  }
  get configValue() {
    return this.context.config?.data[this.displayColumnsProperty]
  }

  ngOnChanges(changes: SimpleChanges): void {
    //
  }

  configChanged() {
    this.context.configChanged()
  }

  removeElementArray(i) {
    this.config?.data[this.displayColumnsProperty].splice(i, 1)
    this.configChanged()
  }

  /**
   * sort array
   * @param event
   */
  sortArray(event: any) {
    console.log(event)
    if (this.config?.data[this.displayColumnsProperty]) {
      moveItemInArray(this.config?.data[this.displayColumnsProperty], event.previousIndex, event.currentIndex)
      this.configChanged()
    }
  }

  addColumn() {
    const column: columnTable = {
      name: 'Tên cột ' + new Date().getTime(),
      field: 'field_' + new Date().getTime(),
      path: 'object.property',
      show: true
    }
    this.config.data[this.displayColumnsProperty] = [...(this.config?.data[this.displayColumnsProperty] || []), column]
  }
  trackByFn(index, item) {
    return index
  }

  dragColumns($event: any) {
    this.sortArray({ previousIndex: $event.previousIndex, currentIndex: $event.currentIndex })
  }

  removeEnumText(columnIndex: number, enumIndex: number) {
    this.config.data[this.displayColumnsProperty][columnIndex]['enumText'].splice(enumIndex, 1)
  }

  addFieldEnumTextColumn(idx: any) {
    const field = {
      key: 'field_' + new Date().getTime(),
      text: 'Hiển thị'
    }
    this.config.data[this.displayColumnsProperty][idx]['enumText'] = [
      ...(this.config.data[this.displayColumnsProperty][idx]['enumText'] || []),
      field
    ]
  }

  protected readonly FORMAT_DATE_TIME_M = FORMAT_DATE_TIME_M
  protected readonly FORMAT_DATE_TIME = FORMAT_DATE_TIME

  /**
   * Toggle JSON input visibility
   */
  toggleJsonInput() {
    this.showJsonInput = !this.showJsonInput;
    if (this.showJsonInput) {
      // When showing the JSON input, populate it with the current columns
      this.jsonInputValue = JSON.stringify(this.configValue || [], null, 2);
      this.jsonError = '';
    }
    // Close the other input if open
    if (this.showJsonInput && this.showObjectInput) {
      this.showObjectInput = false;
    }
  }

  /**
   * Toggle Object input visibility
   */
  toggleObjectInput() {
    this.showObjectInput = !this.showObjectInput;
    if (this.showObjectInput) {
      this.objectInputValue = '';
      this.objectError = '';
    }
    // Close the other input if open
    if (this.showObjectInput && this.showJsonInput) {
      this.showJsonInput = false;
    }
  }

  /**
   * Apply JSON input to columns
   */
  applyJsonInput() {
    try {
      const parsedJson = JSON.parse(this.jsonInputValue);

      if (!Array.isArray(parsedJson)) {
        this.jsonError = 'Input must be an array of column objects';
        return;
      }

      // Validate each column object has required properties
      const invalidColumns = parsedJson.filter(col => {
        return !col.name || !col.field;
      });

      if (invalidColumns.length > 0) {
        this.jsonError = 'All columns must have at least name and field properties';
        return;
      }

      // Apply the parsed JSON to the columns
      this.config.data[this.displayColumnsProperty] = parsedJson;
      this.configChanged();

      // Hide the JSON input after successful application
      this.showJsonInput = false;
      this.jsonError = '';
    } catch (error) {
      this.jsonError = `Invalid JSON: ${error.message}`;
    }
  }

  /**
   * Convert object to column list
   */
  convertObjectToColumns() {
    try {
      const parsedObject = JSON.parse(this.objectInputValue);

      if (typeof parsedObject !== 'object' || Array.isArray(parsedObject) || parsedObject === null) {
        this.objectError = 'Input must be a valid JSON object';
        return;
      }

      // Convert object properties to columns
      const columns = [];

      // Process each property in the object
      Object.keys(parsedObject).forEach(key => {
        const value = parsedObject[key];
        const valueType = typeof value;

        // Create a column based on the property
        const column: any = {
          name: this.formatPropertyName(key),
          path: key,
          field: key,
          show: true
        };

        // Set column type based on value type
        if (valueType === 'number') {
          if (String(value).includes('.')) {
            column.type = 'decimal';
          } else {
            column.type = 'textbox';
          }
        } else if (valueType === 'boolean') {
          column.type = 'checkbox';
        } else if (valueType === 'string') {
          // Check if it's a date string
          if (this.isDateString(value)) {
            column.type = 'date';
            column.datetimeFormat = 'DD/MM/YYYY';
          } else {
            column.type = 'textbox';
          }
        } else if (valueType === 'object' && value !== null) {
          // For nested objects, we'll use the parent property name
          if (Array.isArray(value)) {
            column.type = 'map';
          } else {
            // For nested objects, we might want to flatten them
            // But for simplicity, we'll just mark them as objects
            column.type = 'textbox';
          }
        }

        columns.push(column);
      });

      // Add status column with enum if status exists in the object
      if ('status' in parsedObject) {
        const statusColumn = columns.find(col => col.field === 'status');
        if (statusColumn) {
          statusColumn.type = 'enumText';
          statusColumn.enumText = [
            {
              key: 'ACTIVE',
              text: 'Hoạt động',
              class: 'app-status-inprocess'
            },
            {
              key: 'INACTIVE',
              text: 'Không hoạt động',
              class: 'app-status-reject'
            }
          ];
        }
      }

      // Apply the generated columns
      this.config.data[this.displayColumnsProperty] = columns;
      this.configChanged();

      // Hide the object input after successful application
      this.showObjectInput = false;
      this.objectError = '';
    } catch (error) {
      this.objectError = `Invalid JSON: ${error.message}`;
    }
  }

  /**
   * Format property name to be more readable
   * e.g., 'firstName' becomes 'First Name'
   */
  private formatPropertyName(propertyName: string): string {
    // Convert camelCase to Title Case with spaces
    const formatted = propertyName
      // Insert a space before all uppercase letters
      .replace(/([A-Z])/g, ' $1')
      // Replace underscores with spaces
      .replace(/_/g, ' ')
      // Capitalize the first letter
      .replace(/^./, str => str.toUpperCase());

    return formatted.trim();
  }

  /**
   * Check if a string is likely a date
   */
  private isDateString(value: string): boolean {
    // Check if it's a timestamp (all digits)
    if (/^\d+$/.test(value) && value.length >= 10) {
      return true;
    }

    // Try to parse as date
    const date = new Date(value);
    return !isNaN(date.getTime());
  }
}
