import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { UiBuilderConfiguratorComponent } from './ui-builder-configurator.component'
import { UIBuilderModule } from '@tnx/ngx-ui-builder'
import { UiToolbarComponent } from './ui-toolbar/ui-toolbar.component'
import { FormControlConfiguratorComponent } from './form-control.configurator/form-control.configurator.component'
import { SharedModule } from '@shared'
import { PageTitleConfiguratorComponent } from './page-title.configurator/page-title.configurator.component'
import { TableConfiguratorComponent } from './table-configurator/table-configurator.component'
import { DragDropModule } from '@angular/cdk/drag-drop'
import { TableActionColumnConfiguratorComponent } from './table-configurator/table-action-column-configurator/table-action-column-configurator.component'
import { FormBuilderConfiguratorComponent } from './form-builder-configurator/form-builder-configurator.component'
import { TableColumnConfiguratorComponent } from './table-configurator/table-column-configurator/table-column-configurator.component'
import { NgSelectModule } from '@ng-select/ng-select'
import { MatExpansionModule } from '@angular/material/expansion'
import { MatListModule } from '@angular/material/list'

@NgModule({
  exports: [UiBuilderConfiguratorComponent],
  imports: [
    CommonModule,
    SharedModule,
    UIBuilderModule,
    MatExpansionModule,
    MatListModule,
    NgSelectModule,
    DragDropModule,
    UiBuilderConfiguratorComponent,
    UiToolbarComponent,
    FormControlConfiguratorComponent,
    PageTitleConfiguratorComponent,
    TableConfiguratorComponent,
    TableActionColumnConfiguratorComponent,
    TableColumnConfiguratorComponent,
    FormBuilderConfiguratorComponent
  ]
})
export class UiConfiguratorModule {}
