@import 'variables';
.custom-dialog-container .mat-dialog-container {
  padding: 0 !important;
  min-width: 350px;
  max-height: 440px;
  background: #fff;
  border: 1px solid rgba(0, 0, 0, 0.3) !important;
  border-radius: 8px !important;
  box-shadow:
    0px 1px 2px 0px rgb(60 64 67 / 30%),
    0px 2px 6px 2px rgb(60 64 67 / 15%) !important;
  //height: 80%;
  overflow-x: hidden;
  padding-top: 0;
  position: relative;
}

.app-toolbar {
  box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.07);
  height: 60px !important;
  .div-search {
    border-radius: 1.5rem;
    background-color: $color-gray-100;
    padding: 0.4rem;
    margin-left: $sidenav-width;
  }
  .badge {
    position: absolute;
    top: 0;
    left: 50%;
    font-size: 12px;
    border-radius: 10px;
  }
  .bg-custom-info {
    width: 30px;
    height: 30px;
    padding: 9px 6px 6px 7px;
    border-radius: 20.5px;
    background-color: #eaf3ff !important;
    position: relative;
    span {
      width: 17px;
      height: 15px;
      font-size: 11px;
      font-weight: 600;
      font-stretch: normal;
      font-style: normal;
      line-height: normal;
      letter-spacing: normal;
      text-align: center;
      color: var(--color-blue-primary);
    }
    .badge-user {
      width: 16px;
      height: 16px;
      position: absolute;
      top: -5px;
      left: 60%;
      // margin: 0 8px 20px 18px;
    }
  }
}

// Customize toolbar button
.app-toolbar-button {
  margin-right: 23px !important;

  &.mat-button,
  &.mat-icon-button {
    margin: 0 4px;
  }
  .mat-icon {
    margin-right: 0px !important;
  }

  &.mat-button {
    min-width: unset;
    line-height: 40px;
    border-radius: 999px;
  }
}
.app-toolbar-translate {
  &.mat-flat-button {
    padding: 0px;
    padding-left: 9px;
    padding-bottom: 8px;
  }
}
.app-avatar-button {
  padding: 0 4px;
  font-size: 0;

  .app-avatar {
    height: 32px;
    border-radius: 999px;
  }

  .app-username {
    font-weight: 600;
    font-size: 14px;
  }
}

.mat-menu-panel .mat-menu-content {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.mode-dform {
  min-width: 331px !important;
  .mode-item {
    height: 82px !important;
    padding-top: 12px;
    padding-bottom: 12px;
    padding-left: 33px;
    display: flex;
    .app-mode-title {
      font-weight: 700;
      font-size: 16px;
    }
    .app-mode-content {
      font-weight: 400;
      margin-top: -30px;
      color: $color-gray-700;
    }
    .app-mode-icon {
      margin-right: 0px;
      margin-top: 15px;
    }
  }
}

.btn-opened-sidenav {
  position: absolute !important;
  padding: 0px 28px 0px 8px !important;
  top: 1rem;
}
.btn-bg-primary {
  color: $color-blue-primary;
  background-color: $color-light-blue;
}
.hasBackdrop {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: $color-gray-900;
  opacity: 0.5;
  z-index: 101;
  top: 0px;
}
.app-sidenav-header {
  position: fixed;
  height: 100%;
  overflow-x: hidden;
  z-index: 101;
}
.user-mode-item {
  height: 84px !important;
  line-height: 0 !important;
  padding: 12px 33px !important;
  .mode-text {
    width: 130px;
    height: 100%;
    overflow: hidden;
  }
  .app-mode-title {
    line-height: 3rem !important;
    font-weight: 700;
    font-size: 16px;
  }
  .app-mode-content {
    font-size: 12px;
    margin-top: -1px;
    font-weight: 400;
    color: $color-gray-600;
  }
  .app-mode-icon {
    margin-right: 0px !important;
    margin-top: 20px;
    padding-left: 53px;
    font-size: 18px;
  }
}
