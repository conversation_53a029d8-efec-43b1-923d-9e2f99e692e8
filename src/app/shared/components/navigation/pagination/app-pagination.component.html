<div
  *ngIf="numberOfPages > 0"
  class="paginator"
  [ngClass]="{
    'justify-content-between': showTextPage,
    'justify-content-end': !showTextPage
  }">
  <span *ngIf="showTextPage" class="mrt-3">
    Hi<PERSON><PERSON> thị {{ pageIndex * page.pageSize + 1 }} -
    {{ pageIndex * page.pageSize + (totalItem - pageIndex * page.pageSize < page.pageSize ? totalItem - pageIndex * page.pageSize : page.pageSize) }}
    / {{ totalItem }}
  </span>
  <div id="page-items" class="d-flex align-items-center" *ngIf="totalItem > pageSizeList[0] && showPage">
    <button mat-button [disabled]="page.pageIndex === 0" (click)="setPageIndex(page.pageIndex - 1, true)">
      <mat-icon>chevron_left</mat-icon>
    </button>
    <button mat-button *ngFor="let p of pages" (click)="setPageIndex(p.value - 1, true)" [class.active]="pageIndex === p.value - 1">
      {{ p.label }}
    </button>
    <button mat-button [disabled]="page.pageIndex === numberOfPages - 1" (click)="setPageIndex(page.pageIndex + 1, true)">
      <mat-icon>chevron_right</mat-icon>
    </button>
    <mat-select *ngIf="showPageSizeList" class="app-select-custom m-r-4" [(value)]="page.pageSize" (selectionChange)="goto($event.value)">
      <mat-option *ngFor="let p of pageSizeList" [value]="p">
        <span>{{ p === -1 ? 'All' : p }}</span>
      </mat-option>
    </mat-select>
    <label class="m-10">Chuyển đến</label>
    <!--    <md-input-container>-->
    <!--      -->
    <!--    </md-input-container>-->
    <input
      #input
      type="number"
      matInput
      class="input-page-number"
      min="1"
      max="{{ numberOfPages }}"
      placeholder="Nhập"
      [(ngModel)]="inputPage"
      keydownEvent
      (onKeyDownEnterPress)="onUpdatePage()" />
  </div>
</div>
<mat-paginator
  class="d-none"
  #page
  [pageIndex]="pageIndex || 0"
  [length]="length || 0"
  [pageSize]="pageSize || 0"
  (page)="onPageChange($event)"></mat-paginator>
