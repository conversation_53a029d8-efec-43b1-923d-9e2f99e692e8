import { Component, Input } from '@angular/core'
import { checkLoadMFE } from '@shared'
import { environment } from '@env/environment'

@Component({
  selector: 'app-image',
  templateUrl: './image.component.html',
  styleUrls: ['./image.component.scss'],
  standalone: true
})
export class ImageComponent {
  @Input() src: string
  @Input() width: string = 'auto'
  @Input() height: string = 'auto'
  @Input() class?: string = ''
  @Input() srcset?: string = ''
  @Input() alt?: string = ''

  constructor() {}
  ngOnInit(): void {}

  get url() {
    return checkLoadMFE() ? `${environment.base_path}${this.src}` : this.src
  }
}
