<div #footerItem [ngClass]="{ 'panel footer-item': isFixedBottom, 'mrt-10': !isFixedBottom }" *ngIf="isShow">
  <div class="panel-body">
    <div class="footer-content d-flex justify-content-center align-content-center align-item-center">
      <div *ngIf="isShowSelection" class="mrl-4 btn-md left-btn btn-white-error btn-border-light-error">
        <label class="select-title">Đã chọn</label>
        <label class="select-number fc-primary">
          {{ selectionNumber }}
        </label>
        <label *ngIf="limitSelected" class="select-number fc-primary">/{{ limitSelected }}</label>
      </div>
      <button
        mat-raised-button
        color="primary"
        *ngFor="let item of listButton | async"
        [disabled]="disabled || item.disabled"
        class="btn m-10 btn-md {{ item.classBtn }}"
        (click)="onClick(item.typeBtn)"
        #footerButton>
        <mat-icon class="{{ item.icon }}" *ngIf="item.icon"></mat-icon>
        <span>{{ item.title }}</span>
      </button>
    </div>
  </div>
</div>
