import { AfterViewChecked, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnInit, Output, Renderer2, ViewChild } from '@angular/core'
import { BehaviorSubject, Observable } from 'rxjs'
import { MatIconModule } from '@angular/material/icon'
import { FlexModule } from '@angular/flex-layout/flex'
import { Async<PERSON>ip<PERSON>, NgClass, NgFor, NgIf } from '@angular/common'
import { LayoutService } from '@shared/services/layout.service'
import { MatButton } from '@angular/material/button'

@Component({
  selector: 'app-form-footer',
  templateUrl: './form-footer.component.html',
  styleUrls: ['./form-footer.component.scss'],
  standalone: true,
  imports: [NgIf, FlexModule, NgFor, MatIconModule, AsyncPipe, NgClass, MatButton]
})
export class FormFooterComponent implements OnInit, AfterViewChecked {
  @ViewChild('footerButton') footerButton!: ElementRef
  @ViewChild('footerItem', { static: false }) footerItem!: ElementRef
  @Input() limitSelected: any
  @Input() isShowSelection = false
  @Input() selection = new BehaviorSubject<any>('')
  @Input() listButton = new Observable<any[]>()
  @Input() disabled = false
  @Input() isFixedBottom = true
  @Output() eventClick = new EventEmitter()
  isShow!: boolean
  selectionNumber = 0
  constructor(
    private cdRef: ChangeDetectorRef,
    private layoutService: LayoutService,
    private renderer: Renderer2
  ) {}

  // eslint-disable-next-line
  ngOnInit() {
    this.layoutService.pageWidth$.subscribe((width) => {
      if (this.footerItem && width > 0) {
        this.renderer.setStyle(this.footerItem.nativeElement, 'width', `${width}px`)
      }
    })

    if (this.selection) {
      this.selection.subscribe((res) => {
        if (res) this.selectionNumber = res
        else this.selectionNumber = 0
      })
    }
  }

  ngAfterViewChecked(): void {
    this.listButton?.subscribe((res) => {
      if (res && res.length > 0) this.isShow = true
      this.cdRef.detectChanges()
    })
  }

  onClick(type: any): void {
    this.eventClick.emit(type)
  }
}
