import { Pipe, PipeTransform } from '@angular/core'
import moment from 'moment'

@Pipe({
  name: 'formatDate',
  standalone: true
})
export class formatDate implements PipeTransform {
  transform(date?: any) {
    if (!date) {
      return date
    }

    // Kiểm tra nếu date là số (timestamp)
    if (!isNaN(date)) {
      return moment.unix(Number(date)).format('DD/MM/YYYY')
    }

    // Kiểm tra nếu date là chuỗi ISO
    if (moment(date, moment.ISO_8601, true).isValid()) {
      return moment(date).format('DD/MM/YYYY')
    }

    // Kiểm tra nếu date là một đối tượng ngày hợp lệ
    if (moment(date).isValid()) {
      return moment(date).format('DD/MM/YYYY')
    }

    // Nếu không phải bất kỳ định dạng nào ở trên, trả về giá trị ban đầu
    return date
  }
}
