import { Pipe, PipeTransform } from '@angular/core'

/*
  This pipe truncates a string.
  Use it like so {{ String expression | truncate:10 }}
  This truncates the string to 10 letters and adds '...' to end.
*/

@Pipe({ standalone: true, name: 'truncate' })
export class TruncatePipe implements PipeTransform {
  /*
  The transform method is essential to a pipe. The PipeTransform interface defines that method and guides both tooling and the compiler. Technically, it's optional; <PERSON><PERSON> looks for and executes the transform method regardless.
  */

  transform(value: string | number, limit: number): string {
    if (typeof value === 'string') {
      return value.length < limit ? value : value.slice(0, limit) + '...'
    } else if (typeof value === 'number') {
      const valueToString = value.toString()
      return valueToString.length < limit ? valueToString : valueToString.slice(0, limit) + '...'
    } else {
      return ''
    }
  }
}
