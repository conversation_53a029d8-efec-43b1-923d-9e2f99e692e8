import { AbstractControl, AsyncValidatorFn, ValidationErrors } from '@angular/forms'
import { Observable, of } from 'rxjs'
import { map } from 'rxjs/operators'
import { Status } from '@shared'

export interface params {
  userAssigned: string
  systemCode: string
}

export function validatorUserExistInSystemDirective(validationService, params): AsyncValidatorFn {
  return (control: AbstractControl): Promise<ValidationErrors | null> | Observable<ValidationErrors | null> => {
    const value = control.value
    if (value && params.length) {
      let query = params.map((x) => {
        return new URLSearchParams({ ...x }).toString()
      })
      query.push(new URLSearchParams({ systemCode: value }))

      return validationService.checkExistUsersInSystem(query.join('&')).pipe(
        map(
          (res: any) => {
            if (
              res?.httpStatusCode === Status.SUCCESS &&
              res.data &&
              Array.isArray(res.data) &&
              res.data?.length &&
              res.data.filter((x) => x.existed)?.length
            ) {
              let errors = res.data.filter((x) => x.existed)
              return {
                customMessageError: true,
                errorValue: JSON.stringify({
                  message: 'Hệ thống đã tồn tại với người dùng',
                  data: errors.map((x) => {
                    return {
                      text: x.data.usernameAssigned,
                      id: x.data.fileId,
                      code: x.data.userRequestAccessCode
                    }
                  })
                })
              }
            } else {
              return null
            }
          },
          () => {
            return of(null)
          }
        )
      )
    } else {
      return of(null)
    }
  }
}
