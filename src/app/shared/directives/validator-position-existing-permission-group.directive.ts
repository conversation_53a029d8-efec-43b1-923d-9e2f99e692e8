import { AbstractControl, AsyncValidatorFn, ValidationErrors } from '@angular/forms'
import { Observable, of } from 'rxjs'
import { catchError, map } from 'rxjs/operators'
import { Status } from '@shared'

export interface params {
  systemCode: string
}

export function validatorPositionExistingPermissionGroupDirective(validationService, params): AsyncValidatorFn {
  return (control: AbstractControl): Promise<ValidationErrors | null> | Observable<ValidationErrors | null> => {
    const value = control.value
    if (value && params.systemCode) {
      return validationService.checkExistAssign({ ...params, positionTitleCode: value }).pipe(
        map((res: any) => {
          if (res?.httpStatusCode === Status.SUCCESS && res?.content) {
            return {
              customMessageError: true,
              errorValue: JSON.stringify({
                message: 'Đã tồn tại với nhóm quyền',
                code: res?.content?.groupPermissionCode || '',
                id: res?.content?.id || ''
              })
            }
          } else {
            return null
          }
        }),
        catchError((error) => {
          if (error && error.status === 404) {
            return of(null)
          } else {
            return of(null)
          }
        })
      )
    } else {
      return of(null)
    }
  }
}
