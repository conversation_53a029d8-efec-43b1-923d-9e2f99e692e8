import moment from 'moment'
import Utils from './utils'
import { environment } from '@env/environment'
import _ from 'lodash'

/**
 * L<PERSON>y dữ liệu từ local Store
 * @param key
 * @returns
 */
export function getDataLocalStorageByKey(key: string): any {
  const item = localStorage.getItem(key)
  if (item && item !== 'null') {
    return JSON.parse(item)
  } else {
    return []
  }
}

export function getDataSessionStorageByKey(key: string): any {
  const item = sessionStorage.getItem(key)
  if (item && item !== 'null') {
    return JSON.parse(item)
  } else {
    return []
  }
}

/**
 * chuyển kiểu
 * @param sText
 * @param pattern
 * @returns
 */
export function turnAlphanumberic(sText: string, pattern?: RegExp): string {
  let sNewText = sText
  sNewText = Utils.removeVNAccent(sNewText)
  if (pattern) {
    sNewText = Utils.removeSpecial<PERSON>har(sNewText, pattern)
  } else {
    sNewText = Utils.removeSpecial<PERSON>har(sNewText)
  }
  return sNewText
}

/**
 * chuyển đổi <PERSON> có dậu => CHU_CO_DAU
 * @param sText
 */
export function textToCode(sText: string): string {
  let sNewText = sText
  sNewText = Utils.removeVNAccent(sNewText)
  sNewText = sNewText.replace(/\s/g, '_')
  return sNewText.toUpperCase()
}

/**
 * hàm convertdate chuyển đổi từ dạng ngày có định dạng là dateFrom(VD: YYYY-MM-DD) thành dạng dateTo(VD: DD/MM/YYYY)
 * @param value
 * @param dateFrom
 * @param dateTo
 * @returns
 */
export function converDateToDate(value: any, dateFrom: string, dateTo: string): any {
  if (value) {
    return moment(value, dateFrom).format(dateTo)
  }
  return null
}

/**
 * Hàm convert date lấy về từ server
 * Dạng date lấy về là dạng yyymmdd(dạng ngày không có phân cách)
 * @param date
 * @returns
 */
export function convertDateFromServer(date: string): any {
  if (date) {
    return date.slice(0, 4) + '/' + date.slice(4, 6) + '/' + date.slice(6)
  }
  return null
}

/**
 * Lấy thông tin người dùng đăng nhập
 * @returns
 */
export function getUserInfo() {
  return getDataLocalStorageByKey('UI')
}

/**
 * checkToken
 * @param flagToken
 * @returns
 */
export function tokenValid(): boolean {
  const STORAGE: string[] = ['JWT', 'RJWT', 'UI', 'TE']
  // Kiểm tra các biến môi trường token, refreshtoken, user_info
  for (const x of STORAGE) {
    if (!(localStorage.getItem(x) != null)) {
      return false
    }
  }
  return true
}

export function checkLoadMFE() {
  return location.href.includes(environment.base_url) ? false : true
}

/**
 * Generate a value string from an item and an array of value fields.
 * @param item - The item from which to extract values.
 * @param valueFields - An array of strings representing the fields to extract values from.
 * Support string, array ["field","field2"] and array string json "["field","field2"]"
 * @returns A string containing the concatenated values of the specified fields.
 */
export function generateValue(item: any, valueFields: any): string {
  let valueFieldsArray: any[] = []
  try {
    valueFieldsArray = JSON.parse(valueFields)
  } catch (e) {
    if (!Array.isArray(valueFields)) {
      // case string "field"
      valueFieldsArray = [valueFields]
    } else {
      // case array ["field","field2"]
      valueFieldsArray = valueFields
    }
  }

  return valueFieldsArray
    .map((field: any, idx: number) => {
      const fieldValue = _.get(item, field)
      return fieldValue ? `${fieldValue}${idx === valueFieldsArray.length - 1 ? '' : ' - '}` : ''
    })
    .join('')
}

/**
 * thanhnx
 * convert array data sang selectbox
 * @param array
 * @param key
 * @param values array or string key string|string[]|"["field","field2"]"
 * @param subValues array or string key string|string[]|"["field","field2"]"
 * @param firstSelected auto checked first option => for case edit or any..
 */
export function getSelectOptions(data: any[], keyField: string, valueFields: any, isFirstSelected = false): any[] {
  if (!Array.isArray(data)) {
    return data
  }
  const array = data.map((item, index) => {
    const actualItem = item.data ? item.data : item // Nếu có thuộc tính .data, sử dụng nó, ngược lại sử dụng item gốc
    const value = generateValue(actualItem, valueFields)
    const subValue = generateValue(actualItem, valueFields)

    return {
      ...actualItem, // Sử dụng actualItem thay vì item
      key: String(_.get(actualItem, keyField)),
      value: value,
      subValue: subValue,
      checked: isFirstSelected && index === 0
    }
  })
  return _.uniqBy(array, keyField)
}

export function getSelectOptionsTree(data: any[], keyField: string, valueFields: any, subValueFields: any, isFirstSelected = false): any[] {
  if (!Array.isArray(data)) {
    return data
  }
  const array = data.map((item, index) => {
    const actualItem = item.data ? item.data : item // Nếu có thuộc tính .data, sử dụng nó, ngược lại sử dụng item gốc
    const value = generateValue(actualItem, valueFields)
    const subValue = generateValue(actualItem, subValueFields)
    return {
      ...actualItem, // Sử dụng actualItem thay vì item
      key: String(_.get(actualItem, keyField)),
      value: value,
      subValue: subValue,
      checked: isFirstSelected && index === 0,
      extra: actualItem
    }
  })
  return _.uniqBy(array, keyField)
}

export function getSelectOptionsMulti({
  data,
  keyField,
  valueFields,
  isFirstSelected = false
}: {
  data: any[]
  keyField: string
  valueFields: any
  isFirstSelected: boolean
}) {
  if (!Array.isArray(data)) {
    return data
  }
  const array = data.map((item, index) => {
    const actualItem = item.data ? item.data : item // Nếu có thuộc tính .data, sử dụng nó, ngược lại sử dụng item gốc
    const value = generateValue(actualItem, valueFields)
    const subValue = generateValue(actualItem, valueFields)

    return {
      ...actualItem, // Sử dụng actualItem thay vì item
      key: String(_.get(actualItem, keyField)),
      value: value,
      subValue: subValue,
      checked: isFirstSelected && index === 0
    }
  })
  return _.uniqBy(array, keyField)
}
export function isJsonString(str: string) {
  try {
    JSON.parse(str)
  } catch (e) {
    return false
  }
  return true
}

/**
 * Sử dụng hàm với mảng và tên tham số mong muốn
 const numbers = [1, 2, 3, 4, 5];
 const parameterName = 'numbers';
 const queryString = arrayToQueryString(parameterName, numbers);
 console.log(queryString);
 * @param parameterName
 * @param numbersArray
 */
export function arrayToQueryString(parameterName: any, numbersArray: any[]) {
  // Kiểm tra xem mảng có phần tử hay không
  if (numbersArray.length === 0) {
    return ''
  }

  // Sử dụng map để chuyển đổi mỗi số thành một cặp key=value
  const queryParams = numbersArray.map((number: any) => `${parameterName}=${number}`)

  // Sử dụng join để nối các cặp key=value bằng dấu &
  const queryString = queryParams.join('&')
  return `?${queryString}`
}

/**
 * Hàm flattenNestedData dùng để làm phẳng một mảng dữ liệu lồng nhau.
 *
 * @param {any[]} nestedData - Mảng dữ liệu lồng nhau cần làm phẳng.
 * @param {string} levelField - Tên của trường cấp độ trong dữ liệu lồng nhau (mặc định là 'level').
 * @returns {any[]} Mảng dữ liệu đã được làm phẳng.
 *
 * @example
 * const nestedData = [
 *   { level: 1, data: { name: 'A', children: [{ level: 2, data: { name: 'B' } }] } }
 * ];
 * const flatData = flattenNestedData(nestedData);
 * console.log(flatData); // Output: [{ name: 'A' }, { name: 'B' }]
 */
export function flattenNestedTreeData(nestedData: any[], levelField: string = 'level', keyField: string, valueFields: any): any[] {
  const flatArray: any[] = []
  const addedKeys: Set<string> = new Set()

  function flatten(data: any[]) {
    for (const item of data) {
      const { children, ...rest } = item.data
      const key = String(_.get(item.data, keyField))
      const value = generateValue(item.data, valueFields)

      // Kiểm tra xem khóa đã được thêm vào mảng chưa
      if (!addedKeys.has(key)) {
        flatArray.push({
          ...rest,
          key: key,
          value: value
        })
        addedKeys.add(key)
      }
      if (item.data.children && item.data.children.length > 0) {
        flatten(
          item.data.children.map((child: { [x: string]: any; children: string | any[] }) => ({
            level: child[levelField],
            expandable: child.children && child.children.length > 0,
            data: child
          }))
        )
      }
    }
  }

  flatten(nestedData)
  return flatArray
}

export function flattenNestedData(nestedData: any[], levelField: string = 'level', keyField: string, valueFields: any): any[] {
  const flatArray: any[] = []
  const addedKeys: Set<string> = new Set()

  function flatten(data: any[]) {
    for (const item of data) {
      const { children, ...rest } = item
      const key = String(_.get(item, keyField))
      const value = generateValue(item, valueFields)

      // Kiểm tra xem khóa đã được thêm vào mảng chưa
      if (!addedKeys.has(key)) {
        flatArray.push({
          ...rest,
          key: key,
          value: value
        })
        addedKeys.add(key)
      }
      if (item.children && item.children.length > 0) {
        flatten(
          item.children.map((child: { [x: string]: any; children: string | any[] }) => ({
            level: child[levelField],
            expandable: child.children && child.children.length > 0,
            ...child
          }))
        )
      }
    }
  }

  flatten(nestedData)
  return flatArray
}

/**
 * const path = "/category/product/edit/:productCode";
 * const productCode = extractValueFromColon(path);
 * console.log(productCode); // In ra "productCode"
 * str
 */
export function extractValueFromColon(str: string) {
  const regex = /:(\w+)/ // Biểu thức chính quy để tìm kiếm các chuỗi nằm sau dấu ":"
  const match = regex.exec(str) // Sử dụng exec để tìm kiếm và trả về một mảng nếu tìm thấy

  if (match && match.length > 1) {
    return match[1] // Trả về giá trị nằm sau dấu ":"
  } else {
    return null // Trả về null nếu không tìm thấy
  }
}

/**
 * Chuyển chuỗi số có dấu phẩy thành số (long)
 * @param value string | number
 * @returns number
 */
export function parseNumberFromString(value: string | number): number {
  if (typeof value === 'number') {
    return value
  }
  if (typeof value === 'string') {
    const num = Number(value.replace(/,/g, ''))
    return isNaN(num) ? 0 : num
  }
  return 0
}

/**
 * Định dạng số thành chuỗi có dấu phẩy ngăn cách hàng nghìn
 * @param value string | number
 * @returns string
 */
export function formatCurrency(value: string | number): string {
  if (value === null || value === undefined || value === '') return ''
  let num = typeof value === 'string' ? Number(value.replace(/,/g, '')) : value
  if (isNaN(num)) return ''
  return num.toLocaleString('en-US')
}
