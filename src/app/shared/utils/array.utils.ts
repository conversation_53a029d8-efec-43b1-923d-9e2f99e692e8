type UpdateObject<T> = Partial<T> & { [key: string]: any }

export function updateArrayObjectByIndex<T>(arr: T[], index: number, newData: UpdateObject<T>): T[] {
  // Check if index is within bounds
  if (index < 0 || index >= arr.length) {
    throw new Error('Index out of bounds')
  }

  // Create a copy of the array to avoid mutating the original array
  const updatedArray = [...arr]

  // Create a copy of the object to be updated and merge the new data
  const updatedObject = { ...updatedArray[index], ...newData }

  // Replace the object in the array with the updated object
  updatedArray[index] = updatedObject

  return updatedArray
}

/**
 * // Example usage:
 * const values = ['0'];
 *
 * const items: Item[] = [
 *   {
 *     key: '0',
 *     text: 'Tương tác'
 *   },
 *   {
 *     key: '1',
 *     text: 'Ticket'
 *   },
 *   {
 *     key: '2',
 *     text: 'Khác'
 *   }
 * ];``
 *
 * const result = mapValuesToText(values, items);
 *
 * console.log(result); // "Tương tác,Ticket,Khác"
 * ``
 * @param values
 * @param items
 */

export function mapValuesToText(values: string[], items: any[]): string {
  const texts = values.flatMap((value) => {
    return items.filter((item) => item.key === value).map((item) => item.text)
  })
  return texts.join(',')
}

/**
 * interface Option {
 *   key: string;
 *   value: string;
 * }
 * // Usage example:
 * const fieldTypeOptions = [
 *   { key: 'INPUT', value: 'Nhập' },
 *   { key: 'VIEW_360', value: '360 View' },
 *   { key: 'SOURCE', value: 'Nguồn' }
 * ];
 *
 * const index = getEnumIndexInOptions(fieldTypeOptions, 'INPUT');
 * console.log(index); // Output: 0 (index of 'INPUT' in the array)
 * @param options
 * @param enumValue
 */
// Define a utility function to get the index of an enum value in an array of objects
export const getEnumIndexInOptions = (options: any[], enumValue: string): number => {
  return options.findIndex((option) => option.key === enumValue)
}

/**
 * // Example usage:
 * const stringArray: string[] = ["1", "2", "3", "4", "5"];
 * const numberArray: number[] = convertStringArrayToNumberArray(stringArray);
 * console.log(numberArray); // Output: [1, 2, 3, 4, 5]
 * @param stringArray
 */

export const convertStringArrayToNumberArray = (stringArray: string[]): number[] => {
  return stringArray.map((str) => Number(str))
}

export const convertNumberArrayToStringArray = (numberArray: string[]): string[] => {
  return numberArray.map((str) => String(str))
}

/**
 * check tồn tại 1 trong các quyền
 * @param allowScopes
 * @param inputScope
 */
export const checkSomeScopes = (allowScopes: string[], inputScope: string[]): boolean => {
  return (inputScope || []).some((s) => (allowScopes || []).includes(s))
}

/**
 * check tồn tại tất cả các quyền
 * @param allowScopes
 * @param inputScope
 */
export const checkEveryScopes = (allowScopes: string[], inputScope: string[]): boolean => {
  return (inputScope || []).every((s) => allowScopes.includes(s))
}

export const updatedArrayConfig = (arr1: any[], arr2: any[]) => {
  if (arr1) {
    if (arr2 && Array.isArray(arr2)) {
      const arrResult = arr1.map((item) => {
        const foundItem = arr2.find((el) => el.id === item.id)
        if (foundItem) {
          return { ...item, status: foundItem.status }
        }
        return item
      })
      return arrResult
    }
    return arr1
  }
  return []
}
export function sortByKey(defaultsArr: any[], array: any[], key: string, direction: string = ''): any[] {
  if (direction === '') {
    return [...defaultsArr] // Trả về mảng copy, không thay đổi thứ tự ban đầu
  }
  return array.sort((a, b) => {
    if (typeof a[key] === 'string' && typeof b[key] === 'string') {
      return direction === 'asc' ? a[key].localeCompare(b[key]) : b[key].localeCompare(a[key])
    }

    if (typeof a[key] === 'number' && typeof b[key] === 'number') {
      return direction === 'asc' ? a[key] - b[key] : b[key] - a[key]
    }

    return 0
  })
}
