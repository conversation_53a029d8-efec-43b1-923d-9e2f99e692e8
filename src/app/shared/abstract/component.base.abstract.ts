import { AfterViewInit, ChangeDetectorRef, Directive, Injector, isDevMode, OnDestroy, OnInit, Renderer2 } from '@angular/core'
import { AbstractControl, FormArray, FormControl, FormGroup, Validators } from '@angular/forms'
import { MatDialog } from '@angular/material/dialog'
import { ActivatedRoute, Router } from '@angular/router'
import { TranslateService } from '@ngx-translate/core'
import {
  ActivityIndicatorSingletonService,
  FormType,
  HttpClientService,
  IFormControls,
  ItemControlService,
  LocalStoreManagerService,
  MessageSeverity,
  ToastMbService
} from '@shared'
import { of, Subject } from 'rxjs'

import { ConfigService, ConfigurableService } from '@tnx/ngx-ui-builder'
import { AppFunction } from '../models'
import { environment } from '@env/environment'
import { AppState } from '@store/reducers/app.reducer'
import { Store } from '@ngrx/store'
import { AppService } from '@services/app.service'
import { customValidatorCondition } from '../directives'
import { HttpClient } from '@angular/common/http'
import { Location } from '@angular/common'
import Utils from '../utils/utils'
import { DialogService } from '../components/feedback'
import { switchMap, tap } from 'rxjs/operators'
import { defaultConfig } from '@core/config/config-builder'
import { SessionService, UiElementHelperService } from '../services'
import { LayoutService } from '../services/layout.service'

@Directive()
export abstract class ComponentBaseAbstract implements OnInit, AfterViewInit, OnDestroy {
  get f(): IFormControls {
    return this.form.controls
  }
  public form: FormGroup = new FormGroup({})
  public formDirty = false
  public ngUnsubscribe = new Subject()
  public baseData
  public objFunction: AppFunction

  // Khai báo injector các user
  protected translateService: TranslateService
  protected dialogService: DialogService
  protected toastr: ToastMbService
  protected dialog: MatDialog
  protected httpClient: HttpClientService
  protected itemControl: ItemControlService
  protected indicator: ActivityIndicatorSingletonService
  protected localStore: LocalStoreManagerService
  protected router: Router
  protected route: ActivatedRoute
  protected configService: ConfigService // builder
  protected configurableService: ConfigurableService // builder
  protected sessionService: SessionService
  protected appService: AppService
  protected store: Store<AppState>
  protected cdRef: ChangeDetectorRef
  protected http: HttpClient
  protected location: Location
  protected renderer: Renderer2
  protected elementService: UiElementHelperService
  protected layoutService: LayoutService

  protected constructor(protected injector: Injector) {
    this.translateService = injector.get(TranslateService)
    this.dialogService = injector.get(DialogService)
    this.dialog = injector.get(MatDialog)
    this.toastr = injector.get(ToastMbService)
    this.httpClient = injector.get(HttpClientService)
    this.itemControl = injector.get(ItemControlService)
    this.indicator = injector.get(ActivityIndicatorSingletonService)
    this.localStore = injector.get(LocalStoreManagerService)
    this.router = injector.get(Router)
    this.route = injector.get(ActivatedRoute)
    this.configService = injector.get(ConfigService)
    this.configurableService = injector.get(ConfigurableService)
    this.sessionService = injector.get(SessionService)
    this.appService = injector.get(AppService)
    this.store = injector.get(Store)
    this.cdRef = injector.get(ChangeDetectorRef)
    this.http = injector.get(HttpClient)
    this.location = injector.get(Location)
    this.renderer = injector.get(Renderer2)
    this.elementService = injector.get(UiElementHelperService)
    this.layoutService = injector.get(LayoutService)
  }

  ngOnInit() {
    this.initData()
  }

  ngAfterViewInit() {
    this.afterView()
  }
  protected initData(): void {}

  protected afterView(): void {}

  // Validate input required.
  public isInvalidInput(formGroup: FormGroup, controlName: string): boolean {
    if (formGroup == null) {
      return false
    }
    const control = formGroup.get(controlName)
    if (control == null) {
      return false
    }
    return (control.dirty || control.touched) && control.invalid
  }

  protected gotoAccessDenied() {
    this.router.navigate([environment.base_path + '/access-denied'])
  }

  /**
   * Kiểm tra control đã valid chưa
   * @param controlName;
   * @param error;
   */
  public isValidControl(controlName: string, error: string = null) {
    const control = this.f[controlName]
    if (!error) {
      return control?.touched && control?.invalid
    }
    return control?.touched && control.hasError(error)
  }

  /**
   * Xử lý set data vào form
   * @param formData
   */
  protected patchValue(formData) {
    this.form.patchValue(formData)
    this.baseData = this.form.getRawValue()
  }

  /**
   * cập nhật giá giá trị form builder
   * @param key
   * @param value
   * @param options
   * @param paramData
   * @param data
   */
  patchValueFormBuilder(key, value, options: any[] = undefined, paramData = {}, data: FormType<any> = undefined) {
    const allConfig = this.configService.getAllConfig()
    try {
      if (allConfig.filter((f) => f.type === 'formControl' && f.data?.key === key)?.length > 1) {
        isDevMode() &&
          console.error(
            `Chuẩn hóa dữ liệu config builder vì có nhiều element trùng key sẽ dẫn tới 1 vài lỗi khó xác định. Kiểm tra key ${key}`,
            allConfig.filter((f) => f.type === 'formControl' && f.data?.key === key)
          )
      }
      const idControl = allConfig.find((f) => f.type === 'formControl' && f.data?.key === key).id
      if (idControl) {
        const config = this.configService.getConfig(idControl)
        config.data.value = value
        if (options && Array.isArray(options)) {
          config.data.options = options
        }
        config.data.paramData = { ...config.data.paramData, ...paramData }
        config.data.syncValue = true
        if (data?.required !== undefined) {
          config.data.required = data?.required
        }
        if (data?.readOnly !== undefined) {
          config.data.readOnly = data?.readOnly
        }
        this.configService.updateConfig([config])
      }
    } catch (e) {
      isDevMode && console.error(`${e} Đảm bảo formControl có ID = ${key}, vui lòng kiểm tra lại cấu hình`)
    }
  }

  /**
   * ẩn component builder
   * @param id
   * @param hidden true or false
   */
  updateHiddenItemBuilder(key, hidden = false) {
    const allConfig = this.configService.getAllConfig()
    const id = allConfig.find((f) => f.data?.key === key)?.id
    if (id) {
      const config = this.configService.getConfig(id)
      config.hidden = hidden
      this.configService.updateConfig([config])
    }
  }

  /**
   * example: this.updateConfigFormControlBuilder('fieldName', { readOnly: false, required: true })
   * update object config formControl
   * @param key
   * @param data
   */
  updateConfigFormControlBuilder(key, data = {}) {
    const allConfig = this.configService.getAllConfig()
    const idForm = allConfig.find((f) => f.type === 'formControl' && f.data?.key === key).id
    const config = this.configService.getConfig(idForm)
    config.data = { ...config.data, ...data }
    this.configService.updateConfig([config])
  }

  /**
   * patch object
   * example
   *             const dataEdit = {
   *               systemCode: {
   *                 value: content?.systemCode ? String(content?.systemCode) : undefined,
   *                 options: getSelectOptions([content?.systemInfo], 'systemCode', ['systemCode', 'systemName'], true)
   *               },
   *               jobIdKSCH: {
   *                 value: content?.jobIdKSCH ? String(content?.jobIdKSCH) : undefined,
   *                 options: getSelectOptions([content?.titleKCSH], 'jobId', ['jobCode', 'jobName'], true),
   *                 paramData: { url: `portal-access-management/title/search?orgCode=${this.selectedOrgCode}` }
   *               },
   *               usernameKCSHs: {
   *                 value: content?.userKCSHs.map((x) => x.userName),
   *                 options: getSelectOptions(content?.userKCSHs, 'userName', ['userName', 'fullName'], true),
   *                 paramData: { url: `portal-access-management/mhr-user/search?orgCode=${this.selectedOrgCode}&orgGetType=true&orgGetDirection=true` }
   *               },
   *               jobIdKCNTT: {
   *                 value: content?.jobIdKCNTT ? String(content?.jobIdKCNTT) : undefined,
   *                 options: getSelectOptions([content?.titleKCNTT], 'jobId', ['jobCode', 'jobName'], true),
   *                 paramData: { url: `portal-access-management/title/search?orgCode=${ORG_CODE_CNTT}` }
   *               },
   *               usernameKCNTTs: {
   *                 value: content?.userKCNTTs.map((x) => x.userName),
   *                 options: getSelectOptions(content?.userKCNTTs, 'userName', ['userName', 'fullName'], true),
   *                 paramData: { url: `portal-access-management/mhr-user/search?orgCode=${ORG_CODE_CNTT}&orgGetType=true&orgGetDirection=true` }
   *               }
   *             }
   *             this.patchValuesFormBuilder(dataEdit)
   * @param key
   * @param value
   * @param options
   */
  patchValuesFormBuilder(objectPatch: any = {}) {
    for (let key of Object.keys(objectPatch)) {
      if (objectPatch[key]) {
        this.patchValueFormBuilder(
          key,
          objectPatch[key].value,
          objectPatch[key].options || undefined,
          objectPatch[key]?.paramData || {},
          objectPatch[key]
        )
      }
    }
  }

  transformObject(obj: any): any {
    const result: any = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (typeof obj[key] === 'object' && obj[key] !== null && 'value' in obj[key]) {
          // Giữ nguyên nếu thuộc tính đã có cấu trúc { value: ... }
          result[key] = obj[key]
        } else {
          // Chuyển đổi nếu thuộc tính chưa có cấu trúc { value: ... }
          result[key] = { value: obj[key] }
        }
      }
    }
    return result
  }

  updateConfigsFormBuilder(objectPatch: any = {}) {
    for (let key of Object.keys(objectPatch)) {
      if (objectPatch[key]) {
        this.updateConfigFormControlBuilder(key, objectPatch[key])
      }
    }
  }

  // Validate all fields in FormGroup
  protected validateAllFields(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach((field) => {
      const control = formGroup.get(field)
      if (control instanceof FormArray) {
        control.markAsTouched()
        Object.keys(control.controls).forEach((formArr: any) => {
          const formGroupArr = control.controls[formArr] as FormGroup
          Object.keys(formGroupArr.controls).forEach((fieldArr) => {
            const controlArr = formGroupArr.get(fieldArr)
            if (controlArr instanceof FormArray) {
              controlArr.markAsTouched()
              if (controlArr.controls && controlArr.controls.length > 0) {
                controlArr.controls.forEach((element) => {
                  if (element instanceof FormGroup) {
                    this.validateAllFields(element)
                  }
                })
              }
            }
            if (controlArr instanceof FormControl) {
              if (controlArr.value && typeof controlArr.value === 'string' && controlArr.value?.trim() != controlArr.value) {
                controlArr.setValue(controlArr.value.trim())
              }
              controlArr.markAsTouched({
                onlySelf: true
              })
            } else if (controlArr instanceof FormGroup) {
              this.validateAllFields(controlArr)
            }
          })
        })
      }
      if (control instanceof FormControl) {
        if (control.value && typeof control.value === 'string' && control.value?.trim() != control.value) {
          control.setValue(control.value.trim())
        }
        control.markAsTouched({
          onlySelf: true
        })
      } else if (control instanceof FormGroup) {
        this.validateAllFields(control)
      }
    })
  }

  // Validate all fields in FormGroup
  protected clearAllFields(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach((field) => {
      const control = formGroup.get(field)
      this.removeRequiredToControl(control)
    })
  }

  protected addRequired(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach((field) => {
      const control = formGroup.get(field)
      this.addRequiredToControl(control)
    })
  }

  addRequiredToControl(control: AbstractControl<any, any>) {
    control.setValidators([Validators.required])
    control.updateValueAndValidity()
  }

  removeRequiredToControl(control: AbstractControl<any, any>) {
    control.clearValidators()
    control.updateValueAndValidity()
  }

  protected clearAllFieldsArrayGroup(formArray: FormArray) {
    formArray.controls.forEach((formGroup: any) => {
      this.clearAllFields(formGroup)
    })
  }

  protected updateRequiredArrayFields(formArray: FormArray) {
    formArray.controls.forEach((formGroup: FormGroup) => {
      this.addRequired(formGroup)
    })
  }

  // Add required all fields in FormGroup
  protected updateRequiredAllFields(formGroup: FormGroup, configForm: any[]) {
    Object.keys(formGroup.controls).forEach((field) => {
      const control = formGroup.get(field)
      const formItem = configForm.find((x) => x.key == field)
      if (formItem && formItem.required) {
        control.setValidators([Validators.required])
        control.updateValueAndValidity()
      }
    })
  }

  /**
   * add customer validate with message
   * @param formControl
   * @param msg
   * @protected
   */
  protected addCustomErrorToControl(formControl: AbstractControl, msg) {
    if (formControl) {
      const validator = customValidatorCondition(false, msg)
      const existingValidators = formControl.validator ? [formControl.validator] : []
      const combinedValidators = existingValidators.concat(formControl.asyncValidator ? [formControl.asyncValidator] : [])

      const isDuplicate = combinedValidators.some((v) => v === validator)

      if (!isDuplicate) {
        formControl.setValidators([...existingValidators, validator])
        formControl.updateValueAndValidity()
        formControl.markAsTouched()
      }
    }
  }

  /**
   * remove all validators of form control
   * @param key
   * @protected
   */
  protected clearValidatorControl(key: string) {
    this.form.get(key).clearValidators()
    this.form.get(key).updateValueAndValidity()
  }

  /**
   * Xử lý map giá trị list checkbox
   * @param lstCheckbox;
   * @param lstValue;
   */
  protected pathValueCheckBox(lstCheckbox: any[], lstValue: any[]) {
    lstCheckbox.map((x) => {
      x.checked = lstValue.indexOf(x.key) >= 0 ? true : false
    })
  }

  // Validate input required.
  public isValidInput(formGroup: FormGroup, controlName: string): boolean {
    if (formGroup == null) {
      return false
    }
    const control = formGroup.get(controlName)
    if (control == null) {
      return false
    }
    return (control.dirty || control.touched) && !control.invalid
  }

  /**
   * tự khởi tạo config theo object default nếu chưa tồn tại trong database
   * @param componentName
   * khi set true, đảm bảo truyen vao component tuong ung
   * @param [usePrefix=false]
   */
  async getUIConfig(componentName: string, usePrefix = false): Promise<any> {
    this.appService.getConfigBuilder(componentName)
    localStorage.setItem('currentComponentBuilder', componentName)
    const configBuilder$ = this.appService.getConfigBuilder(componentName).pipe(
      switchMap((res: any) => {
        let data = Utils.JSonTryParse(res?.data?.config?.config)
        data = this.configService.removeAllPrefixesFromIdsAndTypes(data)
        isDevMode() && console.log('componentName', data)
        this.appService.getConfigBuilder({ ...res?.data, id: componentName })
        if (!data && isDevMode()) {
          this.saveBuilderUIConfig(componentName, defaultConfig)
          return of(defaultConfig)
        } else {
          return of(data)
        }
      }),
      tap((data: any) => {
        if (usePrefix) {
          const config = this.configService.addPrefixToIds([...data], componentName)
          this.configService.init(config)
        } else {
          this.configService.init(data)
        }
      }),
      switchMap(() => this.configService.watchAllConfig()),
      tap((response) => {})
    )

    return new Promise<any>((resolve, reject) => {
      const subscription = configBuilder$.subscribe({
        next: (result) => {
          // Đảm bảo bạn chỉ trả về kết quả khi bạn cần nó
          resolve(result) // Trả về kết quả từ observable khi nó hoàn thành
        },
        complete: () => {
          if (environment.uat) {
            this.configurableService.editorEnabled$.next(localStorage.getItem('editorEnable') === 'true')
          }
          subscription.unsubscribe() // Hủy đăng ký observable sau khi hoàn thành
        },
        error: (err) => {
          reject(err) // Trả về promise với lỗi nếu observable gặp lỗi
        }
      })
    })
  }

  async initBuilderUIConfigFromConfig(componentName: string, data: any, usePrefix = false): Promise<any> {
    // this.appService.addOrUpdateRoutesConfig(location.pathname, componentName)

    localStorage.setItem('currentComponentBuilder', componentName)

    data = this.configService.removeAllPrefixesFromIdsAndTypes(data)

    isDevMode() && console.log('componentName', data)

    // this.appService.addOrUpdateComponentConfig({ data, id: componentName })

    if (!data && isDevMode()) {
      this.saveBuilderUIConfig(componentName, defaultConfig)
      data = defaultConfig
    }

    if (usePrefix) {
      const prefixedConfig = this.configService.addPrefixToIds([...data], componentName)
      this.configService.init(prefixedConfig)
    } else {
      this.configService.init(data)
    }

    // Ensure data is an observable
    const dataObservable = of(data)

    return new Promise<any>((resolve, reject) => {
      let subscription // Khai báo biến subscription trước

      subscription = dataObservable.subscribe({
        next: (result) => {
          resolve(result)
        },
        complete: () => {
          if (environment.uat) {
            this.configurableService.editorEnabled$.next(localStorage.getItem('editorEnable') === 'true')
          }
          subscription?.unsubscribe() // Hủy đăng ký để dọn dẹp tài nguyên
        },
        error: (err) => {
          reject(err)
        }
      })
    })
  }

  /**
   * author thanhnx.os
   */
  saveBuilderUIConfig(componentName: string, body: any) {
    if (!componentName || !body || !body?.length) return
    const data = {
      component: componentName,
      data: { config: body }
    }
    this.appService.createConfigBuilder(data).subscribe((response) => {
      console.info('SAVE CONFIG COMPONENT ', response)
    })
  }

  /**
   * Xử lý destroy data trên component
   */
  ngOnDestroy() {
    this.ngUnsubscribe.next(true)
    this.ngUnsubscribe.complete()
    this.destroyData()
  }

  protected destroyData(): void {}

  goTo(state: string, params?: any, replaceUrl?: boolean, relativeTo?: ActivatedRoute) {
    replaceUrl = replaceUrl == null ? false : replaceUrl
    this.router.navigate([state], {
      queryParams: params,
      relativeTo,
      replaceUrl
    })
  }

  navTo(state: string, params?: any, replaceUrl?: boolean, relativeTo?: ActivatedRoute) {
    replaceUrl = replaceUrl == null ? false : replaceUrl
    this.router.navigate([environment.base_path + '/' + state], {
      queryParams: params,
      relativeTo,
      replaceUrl
    })
  }

  openRouterNewTab(command: string) {
    const url = this.router.serializeUrl(this.router.createUrlTree([command]))
    window.open(url, '_blank')
  }

  /**
   * thanhnx color logger
   * @param data
   * @param text
   */
  logger(data: any, text: string = 'data') {
    Utils.logger(data, text)
  }

  info(text: string = 'data', data: any = {}) {
    Utils.logger(data, text)
  }

  error(text: string = 'data', data: any = {}) {
    Utils.error(data, text)
  }

  showErrorGetData() {
    this.toastr.showToastr(this.translateService.instant('dialog.message-error'), '', MessageSeverity.error)
  }
  showDialogSuccessI18n(message: string | string[], title: string | string[]) {
    this.dialogService.success({
      title: title ? this.translateService.instant(title) : '',
      message: message ? this.translateService.instant(message) : ''
    })
  }

  showDialogErrorI18n(message: string | string[], title: string | string[]) {
    this.dialogService.error({
      title: title ? this.translateService.instant(title) : '',
      message: message ? this.translateService.instant(message) : ''
    })
  }
}
