import _ from 'lodash'
import { FORM_CONTROL_TYPE } from '../constants'

export type paramData = {
  isTag?: boolean
  url?: string
  key?: string
  value?: string[]
  preLoad?: boolean
  maxSelectedItems?: string
  isOpen?: boolean
  bindValue?: string
  typeheadKey?: string
  levelProperty?: string // level key if is tree checkbox
  /**
   * chỉ gọi 1 lan duy nhat
   */
  queryStringOnInit?: string
  clientFilter?: boolean
  subValue?: string[]
  typeheadKeySub?: string
  pageSize?: number
  itemConditionEnable?: any
}

export interface ItemOptions {
  key: string
  value: string
  checked?: boolean
  extra?: any
  prefixIcon?: string
}

export interface FormOptions<T> {
  value: T | T[]
  key?: string
  label?: string
  required?: boolean
  max?: string
  min?: string
  maxLength?: string | number | null
  minLength?: string | number | null
  maxDate?: string
  minDate?: string
  order?: number
  controlType?: string
  focus?: boolean
  type?: string
  placeholder?: string
  placeHolderSearch?: string
  options?: ItemOptions[]
  layout?: string
  directives?: string
  microDirectives?: () => void
  customValidate?: string | string[]
  updateOn?: string
  template?: string
  reset?: boolean
  paramData?: paramData
  title?: boolean
  pattern?: string
  checked?: boolean
  readOnly?: boolean
  minRow?: string
  hidden?: boolean
  hideValueCheckBox?: boolean
  clearable?: boolean
  addTag?: boolean
  regex?: RegExp
  checkBoxKey?: string
  syncValue?: boolean
  countMaxLength?: boolean
  upperCase?: boolean
  requiredMessage?: string
  tooltipMessage?: string
  selectAll?: boolean
  removeVNAccent?: boolean
}

export class FormType<T> {
  value: T | T[]
  defaultValue?: T | T[]
  key: string
  label: string
  required?: boolean
  max?: string
  min?: string
  maxLength: string | number | null
  minLength: string | number | null
  maxDate?: string
  minDate?: string
  order: number
  controlType: string
  focus: boolean
  type: string
  placeholder?: string
  placeHolderSearch?: string
  options?: ItemOptions[]
  layout?: string
  directives?: string // sử dụng khi muốn cấm không cho nhập
  microDirectives?: () => void
  customValidate: string | string[]
  updateOn?: string
  template?: string
  reset?: boolean
  paramData?: paramData
  title: boolean
  pattern?: string
  checked?: boolean // Sử dụng cho checkboxItem
  readOnly?: boolean
  minRow?: string
  hideValueCheckBox?: boolean
  hidden?: boolean // input hidden
  clearable?: boolean // clean ng select
  addTag?: boolean // add tag ng select
  regex?: RegExp // sử dụng khi cần báo lỗi sai định dạng
  checkBoxKey?: string // 'key' | 'value',
  syncValue?: boolean = true // nếu bật true mà value của item thay đổi sẽ update cho formControl của form đã tạo
  countMaxLength: boolean = false // count down characters
  upperCase?: boolean
  requiredMessage?: string
  tooltipMessage?: string
  selectAll?: boolean
  removeVNAccent?: boolean

  constructor(options: FormOptions<T>) {
    this.value = options.controlType === FORM_CONTROL_TYPE.DROPDOWN && options.value == '' ? undefined : options.value
    this.key = options.key || ''
    this.label = options.label || ''
    this.required = !!options.required
    this.max = options.max
    this.min = options.min
    this.maxLength = options.maxLength ? options.maxLength : null
    this.minLength = options.minLength ? options.minLength : null
    this.maxDate = options.maxDate
    this.minDate = options.minDate
    this.order = options.order === undefined ? 1 : options.order
    this.controlType = options.controlType || ''
    this.type = options.type || ''
    this.placeholder = options.placeholder || ''
    this.placeHolderSearch = options.placeHolderSearch || ''
    this.focus = !!options.focus
    this.options = options.options || []
    this.layout = options.layout || '100'
    this.directives = options.directives || ''
    this.microDirectives = options.microDirectives
    this.customValidate = options.customValidate || ''
    this.updateOn = options.updateOn || ''
    this.template = options.template || ''
    this.reset = !!options.reset
    this.paramData = options.paramData
    this.title = !!options.title
    this.pattern = options.pattern || ''
    this.checked = !!options.checked
    this.readOnly = typeof options.readOnly === 'boolean' ? options.readOnly : false
    this.minRow = options.minRow || '2'
    this.hidden = options.hidden
    this.hideValueCheckBox = options.hideValueCheckBox
    this.clearable = _.isBoolean(options.clearable) ? options.clearable : false
    this.addTag = options.addTag
    this.regex = options.regex ? options.regex : undefined
    this.checkBoxKey = options.checkBoxKey ? options.checkBoxKey : 'value'
    this.syncValue = options.syncValue
    this.countMaxLength = options?.countMaxLength || false
    this.upperCase = options?.upperCase || false
    this.requiredMessage = options?.requiredMessage || ''
    this.tooltipMessage = options?.tooltipMessage || ''
    this.selectAll = options?.selectAll
    this.removeVNAccent = options?.removeVNAccent
  }
}
