import { FormOptions, FormType } from './form-type.model'
import { FORM_CONTROL_TYPE } from '../constants'

export class TreeviewItem extends FormType<string> {
  override controlType = FORM_CONTROL_TYPE.TREEVIEW
}

export class RadioItem extends FormType<string> {
  override controlType = FORM_CONTROL_TYPE.RADIO
}

export class TextAreaItem extends FormType<string> {
  override controlType = FORM_CONTROL_TYPE.TEXTAREA
}

export class DateTimeItem extends FormType<string> {
  override controlType = FORM_CONTROL_TYPE.DATETIME
}

export class DateRangeItem extends FormType<any> {
  override controlType = FORM_CONTROL_TYPE.DATERANGE
  timePicker?: boolean
  displayFormat?: string
  dateLimit?: number
  singleDatePicker?: boolean
  // Constructor ghi đè
  constructor(
    options: FormOptions<any> & {
      timePicker?: boolean
      displayFormat?: string
      dateLimit?: number
      singleDatePicker?: boolean
    }
  ) {
    super(options) // Gọi constructor của lớp cha
    this.timePicker = options.timePicker // Gán giá trị cho thuộc tính mới
    this.displayFormat = options?.displayFormat || 'DD/MM/YYYY'
    this.dateLimit = options?.dateLimit
    this.singleDatePicker = options?.singleDatePicker
  }
}

export class TextboxItem extends FormType<string> {
  override controlType = FORM_CONTROL_TYPE.TEXT_BOX
}

export class TextboxLinkItem extends FormType<string> {
  override controlType = FORM_CONTROL_TYPE.TEXT_BOX_LINK
}

export class CheckboxItem extends FormType<string> {
  override controlType = FORM_CONTROL_TYPE.CHECKBOX
}

export class NgSelectItem extends FormType<string> {
  override controlType = FORM_CONTROL_TYPE.DROPDOWN
}

export class TreeDropdownItem extends FormType<any> {
  override controlType = FORM_CONTROL_TYPE.TREE_DROPDOWN
  isHiddenSearch?: boolean = false
}
export class HiddenItem extends FormType<string> {
  override controlType = 'hidden'
}

export class SlideItem extends FormType<boolean> {
  override controlType = FORM_CONTROL_TYPE.SLIDE_TOGGLE
}

export class Template extends FormType<string> {
  override controlType = 'template'
}

export class StarItem extends FormType<string> {
  override controlType = FORM_CONTROL_TYPE.RATING
}

export class AddressItem extends FormType<string> {
  override controlType = FORM_CONTROL_TYPE.ADDRESS
}

export class FormControlModel extends FormType<string> {
  toJSON() {
    var jsonedObject = {}
    for (var x in this) {
      if (x === 'toJSON' || x === 'constructor') {
        continue
      }
      // @ts-ignore
      jsonedObject[x] = this[x]
    }
    return jsonedObject
  }
}
