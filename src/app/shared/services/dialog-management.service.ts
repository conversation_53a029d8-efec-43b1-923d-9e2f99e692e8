import { Injectable } from '@angular/core'
import { MatDialogRef } from '@angular/material/dialog'
import * as _ from 'lodash'

@Injectable({
  providedIn: 'root'
})
export class DialogManagementService {
  dialogRefs: MatDialogRef<any, any>[] = new Array()

  constructor() {}

  addDialogRef(dialogRef: MatDialogRef<any, any>) {
    dialogRef.afterClosed().subscribe(() => {
      this.dialogRefs = this.dialogRefs.filter((ref: any) => {
        return ref?.id !== dialogRef.id
      })
    })
    this.dialogRefs.push(dialogRef)
  }

  closeAll() {
    this.dialogRefs.forEach((dialogRef) => {
      dialogRef.close()
    })
  }
}
