// layout.user.ts
import { Injectable } from '@angular/core'
import { BehaviorSubject } from 'rxjs'

@Injectable({
  providedIn: 'root'
})
export class LayoutService {
  private classSource = new BehaviorSubject<string>('')
  private fullScreen = new BehaviorSubject<boolean>(false)
  currentClass = this.classSource.asObservable()
  fullScreen$ = this.fullScreen.asObservable()

  private pageWidthSubject = new BehaviorSubject<number>(0)
  pageWidth$ = this.pageWidthSubject.asObservable()

  updateWidth(width: number) {
    this.pageWidthSubject.next(width)
  }

  changeClass(newClass: string) {
    this.classSource.next(newClass)
  }

  toggleFullScreen() {
    this.fullScreen.next(!this.fullScreen.getValue())
  }
}
