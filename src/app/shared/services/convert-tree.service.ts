import { Injectable } from '@angular/core'
import { BehaviorSubject } from 'rxjs'
import { Menus } from '../models/tree-menu'
import { TodoItemNode } from '../models/tree-model'
import _ from 'lodash'

@Injectable({
  providedIn: 'root'
})
export class ConvertTreeService {
  public filterText: BehaviorSubject<any> = new BehaviorSubject<any>(false)
  public menu$: BehaviorSubject<any> = new BehaviorSubject<any>(false)

  constructor() {}

  setFilterText(item) {
    // Notify the change.
    this.filterText.next(item)
  }

  setDataMenu(item) {
    // Notify the change.
    this.menu$.next(item)
  }

  mapListPermission(listResourceBase, listPermissionBase, permissionsIdList) {
    const listPermission = []
    ;(listPermissionBase || []).forEach((element) => {
      if (permissionsIdList.includes(element?.id)) {
        listPermission.push(element.parentId)
        const resourceItem = (listResourceBase || []).find((res) => res.id == element.parentId)
        if (resourceItem) {
          listPermission.push(resourceItem.parentId)
        }
      }
    })
    return _.uniqBy(listPermission, (e) => {
      return e
    })
  }

  convertTreeData(data, parentId): Array<TodoItemNode> {
    return data
      .filter((val: Menus) => {
        return val.parentId == parentId
      })
      .map((val: Menus) => {
        const menu = this.findChildren(val, data)
        return menu
      })
  }

  findChildren = (menu: Menus, listMenu: Array<Menus>): TodoItemNode => {
    const $children: Array<TodoItemNode> = listMenu.reduce((preVal: Array<TodoItemNode>, currentVal: Menus) => {
      if (currentVal.parentId === menu.id) {
        const $menu: TodoItemNode = this.findChildren(currentVal, listMenu)
        preVal.push($menu)
      }
      return preVal
    }, [])
    return {
      children: $children,
      item: menu.name,
      menus: menu
    }
  }
}
