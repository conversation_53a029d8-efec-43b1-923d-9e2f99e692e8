import { HttpClient, HttpParameterCodec, HttpResponse } from '@angular/common/http'
import { Injectable, isDevMode } from '@angular/core'
import { Router } from '@angular/router'
import { Observable } from 'rxjs'
import { HttpOptions } from '../models'
import { LocalStoreManagerService } from './local-store-manager.service'
import { v4 as uuidv4 } from 'uuid'
import { cleanObject, convertTimeFieldsToTimestamp, splitAndAssignFields } from '../utils'
import { Status } from '../constants'
import { environment } from '@env/environment'

export interface ApiResponse<T> {
  statusCode: number | null
  httpStatusCode: number
  description: string | null
  clientMessageId: string
  timestamp: number
  data: T
}

export enum Verbs {
  GET = 'GET',
  PUT = 'PUT',
  POST = 'POST',
  DELETE = 'DELETE',
  PATCH = 'PATCH'
}

// Helper method to check API success
export function isApiSuccess(res: { httpStatusCode?: number }): boolean {
  return res?.httpStatusCode === Status.SUCCESS
}

export class HttpUrlEncodingCodec implements HttpParameterCodec {
  encodeKey(k: string): string {
    return standardEncoding(k)
  }
  encodeValue(v: string): string {
    return standardEncoding(v)
  }
  decodeKey(k: string): string {
    return decodeURIComponent(k)
  }
  decodeValue(v: string) {
    return decodeURIComponent(v)
  }
}
function standardEncoding(v: string): string {
  return encodeURIComponent(v)
}
@Injectable({ providedIn: 'root' })
export class HttpClientService {
  constructor(
    private http: HttpClient,
    private localStore: LocalStoreManagerService,
    private router: Router
  ) {}

  get<T>(options: HttpOptions): Observable<T> {
    return this.httpCall(Verbs.GET, options)
  }

  delete<T>(options: HttpOptions): Observable<T> {
    return this.httpCall(Verbs.DELETE, options)
  }

  post<T>(options: HttpOptions): Observable<T> {
    return this.httpCall(Verbs.POST, options)
  }

  put<T>(options: HttpOptions): Observable<T> {
    return this.httpCall(Verbs.PUT, options)
  }

  patch<T>(options: HttpOptions): Observable<T> {
    return this.httpCall(Verbs.PATCH, options)
  }

  httpCall<T>(verb: Verbs, options: HttpOptions): Observable<T> {
    // case table list table for dev
    if (isDevMode() && !options.url) {
      options.url = 'https://api.pronexus.vn'
    }

    const clientMessageId = uuidv4()

    // Setup default values
    options.body = cleanObject(convertTimeFieldsToTimestamp(options.body)) ?? null
    options.headers = options.headers ?? {}
    options.headers = { ...options.headers, clientMessageId: clientMessageId, traceId: clientMessageId }
    //for unit test
    if (localStorage.getItem('TOKEN_TEST')) {
      options.headers = {
        ...options.headers,
        clientMessageId: clientMessageId,
        traceId: clientMessageId,
        Authorization: `Bearer ${localStorage.getItem('TOKEN_TEST')}`
      }
    }
    // // update value property undefined => blank
    const params = options?.params ? cleanObject(convertTimeFieldsToTimestamp(options?.params)) : {}
    // console.log('params: ', params)
    let url
    if (options.path.includes('http')) {
      url = options.path
    } else {
      url = `${options.url}/${options.path}`
    }
    if (isDevMode()) {
      url = url.replace('https://api.pronexus.vn/portal/api', `${environment.services.portal}`)
    }
    return this.http.request<T>(verb, url, {
      body: options.body,
      headers: options.headers,
      params: params ?? null
    })
  }

  /**
   *
   * @param verb
   * @param options
   */
  download(verb: Verbs, options: HttpOptions) {
    let body = splitAndAssignFields(options.body)
    body = convertTimeFieldsToTimestamp(body)
    body = cleanObject(body)

    options.body = body ?? null
    options.headers = options.headers ?? {}
    options.headers = { ...options.headers, clientMessageId: uuidv4() }
    const params = options?.params ? cleanObject(convertTimeFieldsToTimestamp(splitAndAssignFields(options?.params))) : {}
    const url = options.url ? `${options.url}/${options.path}` : options.path
    return this.http.request(verb, url, {
      headers: options.headers,
      observe: 'response',
      responseType: 'blob',
      params: params ?? null,
      body: options.body
    })
  }

  /**
   *
   * @param options
   * @param formData
   */
  uploadFormData(options: HttpOptions, formData: FormData) {
    options.headers = options.headers ?? {}
    options.headers = {
      ...options.headers,
      clientMessageId: uuidv4()
    }
    const url = options.url ? `${options.url}/${options.path}` : options.path
    return this.http.post<any>(url, formData, {
      headers: options.headers,
      observe: 'response',
      responseType: 'blob' as 'json',
      params: options.params ?? null
    })
  }
}
