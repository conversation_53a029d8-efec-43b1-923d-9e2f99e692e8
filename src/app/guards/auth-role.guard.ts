import { AuthGuardData, createAuthGuard } from 'keycloak-angular'
import { ActivatedRouteSnapshot, CanActivateFn, RouterStateSnapshot, UrlTree } from '@angular/router'
import { inject } from '@angular/core'
import Keycloak from 'keycloak-js'
import { SessionService } from '@shared/services/session.service'
import { Router } from '@angular/router'

/**
 * The logic below is a simple example, please make it more robust when implementing in your application.
 *
 * Reason: isAccessGranted is not validating the resource, since it is merging all roles. Two resources might
 * have the same role name and it makes sense to validate it more granular.
 */
const isAccessAllowed = async (route: ActivatedRouteSnapshot, state: RouterStateSnapshot, authData: AuthGuardData): Promise<boolean | UrlTree> => {
  const { authenticated } = authData
  const sessionService = inject(SessionService)
  if (authenticated) {
    const resources: string[] = route.data['resources'] || [];
    const scopes: string[] = route.data['scopes'] || [];
    // Nếu không truyền resource hoặc scope thì cho qua
    if ((!resources || resources.length === 0) && (!scopes || scopes.length === 0)) {
      return true;
    }
    // Nếu chỉ có resource, check chỉ cần tồn tại trong session
    if (resources.length > 0 && scopes.length === 0) {
      const hasResource = resources.some(resource => {
        const allowed = sessionService.getSessionData(`RESOURCE_${resource}`)
        return Array.isArray(allowed) && allowed.length > 0
      })
      if (!hasResource) {
        return accessDeniedUrl()
      }
      return true
    }
    // Nếu chỉ có scopes, check bất kỳ resource nào có đủ scopes
    if (scopes.length > 0 && resources.length === 0) {
      const hasScope = sessionHasScopesForAnyResource(scopes, sessionService)
      if (!hasScope) {
        return accessDeniedUrl()
      }
      return true
    }
    // Nếu có cả resource và scopes, check từng resource phải có đủ scopes
    if (resources.length > 0 && scopes.length > 0) {
      const hasPermission = resources.some(resource => {
        const allowed = sessionService.getSessionData(`RESOURCE_${resource}`)
        return Array.isArray(allowed) && scopes.every(scope => allowed.includes(scope))
      })
      if (!hasPermission) {
        return accessDeniedUrl()
      }
      return true
    }
    // Fallback to deny
    return accessDeniedUrl()
  } else {
    const keycloakService = inject(Keycloak)
    if (!keycloakService) {
      console.error('KeycloakService is not available')
      return false
    }
    const redirectUri = window.location.origin + state.url
    await keycloakService.login({
      redirectUri: redirectUri
    })
    return false
  }
}

function sessionHasScopesForAnyResource(scopes: string[], sessionService: SessionService): boolean {
  // Duyệt tất cả keys trong session, key dạng RESOURCE_<name>
  const allKeys = Array.from((sessionService as any).sessionVariables.keys()) as string[];
  const resourceKeys = allKeys.filter((k: string) => typeof k === 'string' && k.startsWith('RESOURCE_'));
  return resourceKeys.some((key: string) => {
    const allowed = sessionService.getSessionData(key);
    return Array.isArray(allowed) && scopes.every(scope => allowed.includes(scope));
  });
}

function accessDeniedUrl(): UrlTree {
  const router = inject(Router)
  return router.parseUrl('/access-denied')
}

export const canActivateAuthRole = createAuthGuard<CanActivateFn>(isAccessAllowed)
