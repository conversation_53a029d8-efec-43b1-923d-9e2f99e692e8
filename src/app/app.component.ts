import { Component } from '@angular/core'
import { RouterOutlet } from '@angular/router'
import { TranslateService } from '@ngx-translate/core'

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet],
  templateUrl: './app.component.html'
})
export class AppComponent {
  title = 'Modernize Angular Admin Tempplate'
  constructor(private translate: TranslateService) {
    // this.translate.setDefaultLang('en');
    // this.translate.use('en');
    //
    // this.translate.get('home').subscribe((res: string) => {
    //   console.log('HELLO:', res); // Nếu res là "HELLO" thay vì nội dung từ JSON, có vấn đề với loader.
    // });
  }
}
