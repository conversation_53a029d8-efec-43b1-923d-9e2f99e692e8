import { Routes } from '@angular/router'

export const routes: Routes = [
  {
    path: '',
    title: '<PERSON>h mục sản phẩm',
    loadComponent: async () => (await import('./pages/list/product-list.component')).ProductListComponent,
    data: {
      breadcrumb: '<PERSON>h mục sản phẩm',
      showBreadcrumb: false
    }
    // canActivate: [RoleGuard],
  },
  {
    path: 'create',
    title: 'Thêm mới sản phẩm',
    loadComponent: async () => (await import('./pages/create/product-create.component')).ProductCreateComponent,
    data: { breadcrumb: 'Thêm mới sản phẩm' }
  },
  {
    path: 'edit/:id',
    title: 'Chỉnh sửa sản phẩm',
    loadComponent: async () => (await import('./pages/edit/product-edit.component')).ProductEditComponent,
    // canActivate: [RoleGuard],
    data: { breadcrumb: 'Chỉnh sửa sản phẩm' }
  },
  {
    path: 'import',
    title: 'Import sản phẩm',
    loadComponent: async () => (await import('./pages/import/product-import.component')).ProductImportComponent,
    // canActivate: [RoleGuard],
    data: { breadcrumb: 'Import sản phẩm' }
  }
]
