import { Component, Inject, Injector } from '@angular/core'
import { <PERSON><PERSON><PERSON><PERSON>, FormGroup, ReactiveFormsModule } from '@angular/forms'
import { ComponentAbstract, Status } from '@shared'
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog'
import { takeUntil, finalize } from 'rxjs'
import { MaterialModule } from 'src/app/material.module'
import { CommonModule } from '@angular/common'
import { TablerIconComponent } from 'angular-tabler-icons'
import { SalaryAdvanceCategoryService } from '../../services/salary-advance-category.service'
import { SalaryAdvanceCategoryUpdateModel } from '../../models/salary-advance-category.model'
import { SALARY_ADVANCE_CATEGORY_STATUS } from '../../constants'
import { MatCheckboxModule } from '@angular/material/checkbox'
import { FlexModule } from '@angular/flex-layout'
import { formDescription, formIsActive, form<PERSON><PERSON>, formValue } from '../../form/edit.form'
import { TextControlComponent, CheckboxControlComponent } from '@shared/components/data-input'
import { TextareaControlComponent } from '@shared/components/data-input/textarea-control/textarea-control.component'

@Component({
  selector: 'app-salary-advance-category-edit',
  templateUrl: './salary-advance-category-edit.component.html',
  styleUrls: ['./salary-advance-category-edit.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MaterialModule,
    ReactiveFormsModule,
    TablerIconComponent,
    MatDialogModule,
    MatCheckboxModule,
    FlexModule,
    TextControlComponent,
    CheckboxControlComponent,
    TextareaControlComponent
  ]
})
export class SalaryAdvanceCategoryEditComponent extends ComponentAbstract {
  $formKey = formKey()
  $formValue = formValue()
  $formDescription = formDescription()
  $formIsActive = formIsActive()

  form: FormGroup
  key: string

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    protected override injector: Injector,
    private formBuilder: FormBuilder,
    private salaryAdvanceCategoryService: SalaryAdvanceCategoryService,
    public dialogRef: MatDialogRef<SalaryAdvanceCategoryEditComponent>
  ) {
    super(injector)
    this.key = data.key
    this.form = this.itemControl.toFormGroup([this.$formKey, this.$formValue, this.$formDescription, this.$formIsActive])
    this.getSalaryAdvanceCategoryDetail()
  }

  componentInit() {}

  getSalaryAdvanceCategoryDetail() {
    this.indicator.showActivityIndicator()
    this.salaryAdvanceCategoryService
      .getSalaryAdvanceCategoryDetail(this.key)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator())
      )
      .subscribe({
        next: (res: any) => {
          if (res?.httpStatusCode === Status.SUCCESS) {
            this.form.patchValue({
              transName: res.data.transName,
              icon: res.data.icon,
              description: res.data.description,
              status: res.data.status === 'ACTIVE'
            })
          }
        },
        error: (err) => {
          this.showDialogErrorI18n(err?.error?.data?.message, 'Lấy thông tin danh mục ứng lương <br/>thất bại')
          this.dialogRef.close()
        }
      })
  }

  onUpdate() {
    this.validateAllFields(this.form)
    if (this.form.valid) {
      this.indicator.showActivityIndicator()
      const model: SalaryAdvanceCategoryUpdateModel = {
        transName: this.form.get('transName')?.value,
        icon: this.form.get('icon')?.value,
        description: this.form.get('description')?.value,
        status: this.form.get('status')?.value ? 'ACTIVE' : 'INACTIVE'
      }

      this.salaryAdvanceCategoryService
        .updateSalaryAdvanceCategory(this.key, model)
        .pipe(
          takeUntil(this.ngUnsubscribe),
          finalize(() => this.indicator.hideActivityIndicator())
        )
        .subscribe({
          next: (res: any) => {
            if (res?.httpStatusCode === Status.SUCCESS) {
              this.showDialogSuccessI18n('', 'Cập nhật danh mục ứng lương <br/>thành công')
              this.dialogRef.close(true)
            }
          },
          error: (err) => {
            this.showDialogErrorI18n(err?.error?.data?.message, 'Cập nhật danh mục ứng lương <br/>thất bại')
          }
        })
    } else {
      this.showDialogErrorI18n('', 'Vui lòng kiểm tra lại thông tin nhập vào')
    }
  }
}
