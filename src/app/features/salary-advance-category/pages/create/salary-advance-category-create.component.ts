import { Component, Inject, Injector } from '@angular/core'
import { Form<PERSON>uilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms'
import { ComponentAbstract, Status } from '@shared'
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog'
import { takeUntil, finalize } from 'rxjs'
import { MaterialModule } from 'src/app/material.module'
import { CommonModule } from '@angular/common'
import { TablerIconComponent } from 'angular-tabler-icons'
import { SalaryAdvanceCategoryService } from '../../services/salary-advance-category.service'
import { SalaryAdvanceCategoryCreateModel } from '../../models/salary-advance-category.model'
import { SALARY_ADVANCE_CATEGORY_STATUS } from '../../constants'
import { MatCheckboxModule } from '@angular/material/checkbox'
import { FlexModule } from '@angular/flex-layout'
import { formDescription, formStatus, formTransName, formIcon } from '../../form/create.form'
import { TextControlComponent, CheckboxControlComponent } from '@shared/components/data-input'
import { TextareaControlComponent } from '@shared/components/data-input/textarea-control/textarea-control.component'

@Component({
  selector: 'app-salary-advance-category-create',
  templateUrl: './salary-advance-category-create.component.html',
  styleUrls: ['./salary-advance-category-create.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MaterialModule,
    ReactiveFormsModule,
    TablerIconComponent,
    MatDialogModule,
    MatCheckboxModule,
    FlexModule,
    TextControlComponent,
    CheckboxControlComponent,
    TextareaControlComponent
  ]
})
export class SalaryAdvanceCategoryCreateComponent extends ComponentAbstract {
  $formTransName = formTransName()
  $formIcon = formIcon()
  $formDescription = formDescription()
  $formStatus = formStatus()

  form: FormGroup

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    protected override injector: Injector,
    private formBuilder: FormBuilder,
    private salaryAdvanceCategoryService: SalaryAdvanceCategoryService,
    public dialogRef: MatDialogRef<SalaryAdvanceCategoryCreateComponent>
  ) {
    super(injector)
    this.form = this.itemControl.toFormGroup([this.$formTransName, this.$formIcon, this.$formDescription, this.$formStatus])
  }

  componentInit() {}

  onCreate() {
    this.validateAllFields(this.form)
    if (this.form.valid) {
      this.indicator.showActivityIndicator()
      const isActive = this.form.get('status')?.value === 'true'
      const model: SalaryAdvanceCategoryCreateModel = {
        transName: this.form.get('transName')?.value,
        icon: this.form.get('icon')?.value,
        description: this.form.get('description')?.value,
        status: isActive ? 'ACTIVE' : 'INACTIVE'
      }

      this.salaryAdvanceCategoryService
        .createSalaryAdvanceCategory(model)
        .pipe(
          takeUntil(this.ngUnsubscribe),
          finalize(() => this.indicator.hideActivityIndicator())
        )
        .subscribe({
          next: (res: any) => {
            if (res?.httpStatusCode === Status.SUCCESS) {
              this.showDialogSuccessI18n('', 'Thêm mới danh mục ứng lương <br/>thành công')
              this.dialogRef.close(true)
            }
          },
          error: (err) => {
            this.showDialogErrorI18n(err?.error?.data?.message, 'Thêm mới danh mục ứng lương <br/>thất bại')
          }
        })
    } else {
      this.showDialogErrorI18n('', 'Vui lòng kiểm tra lại thông tin nhập vào')
    }
  }
}
