import { CheckboxItem, TextAreaItem, TextboxItem } from '@shared'

export const formKey = () =>
  new TextboxItem({
    key: 'transName',
    label: 'Tên danh mục',
    placeholder: 'Nhập tên danh mục',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 100,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập tên danh mục'
  })

export const formValue = () =>
  new TextboxItem({
    key: 'icon',
    label: 'Icon',
    placeholder: 'Nhập icon',
    value: '',
    required: false,
    countMaxLength: true,
    maxLength: 500,
    customValidate: 'customMessageError'
  })

export const formDescription = () =>
  new TextAreaItem({
    key: 'description',
    label: 'Mô tả',
    placeholder: 'Nhập mô tả danh mục',
    value: '',
    required: false,
    countMaxLength: true,
    maxLength: 500,
    customValidate: 'customMessageError'
  })

export const formIsActive = () =>
  new CheckboxItem({
    key: 'status',
    label: '<PERSON><PERSON>ch hoạt',
    value: 'true',
    required: false
  })
