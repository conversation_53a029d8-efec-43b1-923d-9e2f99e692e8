import { CheckboxItem, TextAreaItem, TextboxItem } from '@shared'

export const formTransName = () =>
  new TextboxItem({
    key: 'transName',
    label: 'Tên giao dịch',
    placeholder: 'Nhập tên giao dịch',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 100,
    upperCase: true,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập tên giao dịch'
  })

export const formIcon = () =>
  new TextboxItem({
    key: 'icon',
    label: 'Icon',
    placeholder: 'Nhập tên icon',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 50,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập tên icon'
  })

export const formDescription = () =>
  new TextAreaItem({
    key: 'description',
    label: 'Mô tả',
    placeholder: 'Nhập mô tả',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 500,
    customValidate: 'customMessageError',
    requiredMessage: '<PERSON>ui lòng nhập mô tả'
  })

export const formStatus = () =>
  new CheckboxItem({
    key: 'status',
    label: 'Kích hoạt',
    value: 'true',
    required: false
  })
