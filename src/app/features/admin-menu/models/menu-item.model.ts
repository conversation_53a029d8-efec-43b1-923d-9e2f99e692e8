/**
 * Node for menu item
 */
export class MenuItemNode {
  id: string;
  children: MenuItemNode[];
  item: string;
  url: string | null = null;
  icon: string | null = null;
  sort: number = 0; // Add sort property with default value
  originalData?: any; // Store the original API response data
}

/**
 * Flat node with expandable and level information
 */
export class MenuItemFlatNode {
  id: string;
  item: string;
  url: string | null = null;
  icon: string | null = null;
  sort: number = 0; // Add sort property for flat node
  level: number;
  expandable: boolean;
  isEditing: boolean = false;
  isNew: boolean = false;
  originalData?: any; // Store the original API response data
  _pendingSort?: number;
}
