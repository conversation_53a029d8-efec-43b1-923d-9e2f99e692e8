import { SelectionModel } from '@angular/cdk/collections'
import { FlatTreeControl } from '@angular/cdk/tree'
import { Component, OnInit, Injectable, NO_ERRORS_SCHEMA } from '@angular/core'
import { MatButtonModule } from '@angular/material/button'
import { MatCardModule } from '@angular/material/card'
import { MatCheckboxModule } from '@angular/material/checkbox'
import { MatFormFieldModule } from '@angular/material/form-field'
import { MatIconModule } from '@angular/material/icon'
import { MatInputModule } from '@angular/material/input'
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner'
import { MatTreeFlatDataSource, MatTreeFlattener, MatTreeModule } from '@angular/material/tree'
import { RouterModule } from '@angular/router'
import { CommonModule } from '@angular/common'
import { MatDialog, MatDialogModule } from '@angular/material/dialog'
import { MatSnackBar } from '@angular/material/snack-bar'
import { MenuItemNode, MenuItemFlatNode } from '../../models/menu-item.model'
import { MenuDatabaseService } from '../../services/menu-database.service'
import { MenuApiService } from '../../services/menu-api.service'
import { MenuItemDialogComponent } from '../../components/menu-item-dialog/menu-item-dialog.component'
import { DialogService } from '@shared/components/feedback'
import { TablerIconsModule } from 'angular-tabler-icons'
import { MatTooltip } from '@angular/material/tooltip'
import { ResourcePermissionDialogComponent } from '../../components/resource-permission-dialog/resource-permission-dialog.component'
import { ScopePermissionDialogComponent } from '../../components/scope-permission-dialog/scope-permission-dialog.component'
import { FormsModule } from '@angular/forms'

@Component({
  selector: 'app-admin-menu',
  standalone: true,
  imports: [
    MatIconModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatTreeModule,
    MatCardModule,
    MatButtonModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    CommonModule,
    RouterModule,
    TablerIconsModule,
    MatTooltip,
    FormsModule
  ],
  templateUrl: './admin-menu.component.html',
  styleUrls: ['./admin-menu.component.scss'],
  providers: [MenuDatabaseService, DialogService]
})
export class AdminMenuComponent implements OnInit {
  /** Map from flat node to nested node. This helps us finding the nested node to be modified */
  flatNodeMap = new Map<MenuItemFlatNode, MenuItemNode>()

  /** Map from nested node to flattened node. This helps us to keep the same object for selection */
  nestedNodeMap = new Map<MenuItemNode, MenuItemFlatNode>()

  /** A selected parent node to be inserted */
  selectedParent: MenuItemFlatNode | null = null

  /** The new item's name */
  newItemName = ''

  treeControl: FlatTreeControl<MenuItemFlatNode>
  treeFlattener: MatTreeFlattener<MenuItemNode, MenuItemFlatNode>
  dataSource: MatTreeFlatDataSource<MenuItemNode, MenuItemFlatNode>

  /** The selection for checklist */
  checklistSelection = new SelectionModel<MenuItemFlatNode>(true /* multiple */)

  /** Loading state for loading menu data */
  loadingData = false

  /** Error message */
  errorMessage = ''

  selectedResource: any = null
  scopes: string[] = ['read', 'write', 'delete'] // Có thể cập nhật động theo resource
  selectedResources: any[] = []

  constructor(
    private _database: MenuDatabaseService,
    private menuApiService: MenuApiService,
    private dialog: MatDialog,
    private dialogService: DialogService,
    private snackBar: MatSnackBar // Thêm snackbar để thông báo
  ) {
    this.treeFlattener = new MatTreeFlattener(this.transformer, this.getLevel, this.isExpandable, this.getChildren)

    this.treeControl = new FlatTreeControl<MenuItemFlatNode>(this.getLevel, this.isExpandable)
    this.dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener)

    _database.dataChange.subscribe((data) => {
      this.dataSource.data = data

      // Expand the first level by default
      if (this.treeControl.dataNodes && this.treeControl.dataNodes.length > 0) {
        this.treeControl.dataNodes.filter((node) => node.level === 0).forEach((node) => this.treeControl.expand(node))
      }
    })
  }

  ngOnInit(): void {
    this.loadMenuData()
    // Sau khi load xong, đồng bộ pendingSort
    this._database.dataChange.subscribe(() => {
      this.syncPendingSortForAllNodes()
    })
  }

  /**
   * Load menu data from the API
   */
  loadMenuData(): void {
    this.loadingData = true
    this.errorMessage = null

    // Load menu data from the database service
    this._database.loadInitialData()

    // Subscribe to changes in the database
    this._database.dataChange.subscribe((data) => {
      this.dataSource.data = data
      this.loadingData = false
    })

    // Subscribe to loading state
    this._database.loading.subscribe((loading) => {
      this.loadingData = loading
    })

    // Subscribe to error state
    this._database.error.subscribe((error) => {
      this.errorMessage = error
    })
  }

  /**
   * Transformer to convert nested node to flat node. Record the nodes in maps for later use.
   */
  transformer = (node: MenuItemNode, level: number) => {
    const existingNode = this.nestedNodeMap.get(node)
    const flatNode = existingNode && existingNode.item === node.item ? existingNode : new MenuItemFlatNode()
    flatNode.id = node.id
    flatNode.item = node.item
    flatNode.level = level
    flatNode.expandable = !!node.children && node.children.length > 0
    flatNode.url = node.url
    flatNode.icon = node.icon
    flatNode.originalData = node.originalData
    // Lấy sort từ originalData.attributes.sort[0] nếu có
    if (
      node.originalData &&
      node.originalData.attributes &&
      Array.isArray(node.originalData.attributes.sort) &&
      node.originalData.attributes.sort.length
    ) {
      flatNode.sort = Number(node.originalData.attributes.sort[0])
    } else {
      flatNode.sort = 0
    }

    this.flatNodeMap.set(flatNode, node)
    this.nestedNodeMap.set(node, flatNode)

    return flatNode
  }

  getLevel = (node: MenuItemFlatNode) => node.level

  isExpandable = (node: MenuItemFlatNode) => node.expandable

  getChildren = (node: MenuItemNode): MenuItemNode[] => node.children || []

  hasChild = (_: number, _nodeData: MenuItemFlatNode) => _nodeData.expandable

  hasNoContent = (_: number, _nodeData: MenuItemFlatNode) => _nodeData.item === ''

  /**
   * Whether all the descendants of the node are selected.
   */
  descendantsAllSelected(node: MenuItemFlatNode): boolean {
    const descendants = this.treeControl.getDescendants(node)
    const descAllSelected =
      descendants.length > 0 &&
      descendants.every((child) => {
        return this.checklistSelection.isSelected(child)
      })
    return descAllSelected
  }

  /**
   * Whether part of the descendants are selected
   */
  descendantsPartiallySelected(node: MenuItemFlatNode): boolean {
    const descendants = this.treeControl.getDescendants(node)
    const result = descendants.some((child) => this.checklistSelection.isSelected(child))
    return result && !this.descendantsAllSelected(node)
  }

  /**
   * Toggle the item selection. Select/deselect all the descendants node
   */
  menuItemSelectionToggle(node: MenuItemFlatNode): void {
    this.checklistSelection.toggle(node)
    const descendants = this.treeControl.getDescendants(node)
    this.checklistSelection.isSelected(node) ? this.checklistSelection.select(...descendants) : this.checklistSelection.deselect(...descendants)

    // Force update for the parent
    descendants.forEach((child) => this.checklistSelection.isSelected(child))
    this.checkAllParentsSelection(node)
    this.selectedResources = this.checklistSelection.selected
  }

  /**
   * Toggle a leaf item selection. Check all the parents to see if they changed
   */
  menuLeafItemSelectionToggle(node: MenuItemFlatNode): void {
    this.checklistSelection.toggle(node)
    this.checkAllParentsSelection(node)

    // TODO: Add API call to update menu item status
    console.log('Toggled menu item:', node.item, 'Selected:', this.checklistSelection.isSelected(node))
    this.selectedResources = this.checklistSelection.selected
  }

  /* Checks all the parents when a leaf node is selected/unselected */
  checkAllParentsSelection(node: MenuItemFlatNode): void {
    let parent: MenuItemFlatNode | null = this.getParentNode(node)
    while (parent) {
      this.checkRootNodeSelection(parent)
      parent = this.getParentNode(parent)
    }
  }

  /** Check root node checked state and change it accordingly */
  checkRootNodeSelection(node: MenuItemFlatNode): void {
    const nodeSelected = this.checklistSelection.isSelected(node)
    const descendants = this.treeControl.getDescendants(node)
    const descAllSelected =
      descendants.length > 0 &&
      descendants.every((child) => {
        return this.checklistSelection.isSelected(child)
      })
    if (nodeSelected && !descAllSelected) {
      this.checklistSelection.deselect(node)
    } else if (!nodeSelected && descAllSelected) {
      this.checklistSelection.select(node)
    }
  }

  /* Get the parent node of a node */
  getParentNode(node: MenuItemFlatNode): MenuItemFlatNode | null {
    const currentLevel = this.getLevel(node)

    if (currentLevel < 1) {
      return null
    }

    const startIndex = this.treeControl.dataNodes.indexOf(node) - 1

    for (let i = startIndex; i >= 0; i--) {
      const currentNode = this.treeControl.dataNodes[i]

      if (this.getLevel(currentNode) < currentLevel) {
        return currentNode
      }
    }
    return null
  }

  /** Add a new item using dialog */
  addNewItem(node: MenuItemFlatNode) {
    const parentNode = this.flatNodeMap.get(node)
    if (parentNode) {
      // Get parent ID for the new menu item
      const parentId = parentNode.id || '0'

      // Open dialog for creating new menu item
      const dialogRef = this.dialog.open(MenuItemDialogComponent, {
        width: '50%',
        data: {
          parentId,
          isEdit: false,
          availableScopes: [] // You can load available scopes here if needed
        }
      })

      dialogRef.afterClosed().subscribe((result) => {
        if (result) {
          // Call API to create the menu item
          this.menuApiService.createMenuItem(result).subscribe(
            (response) => {
              console.log('Menu item created:', response)

              // Reload the entire menu data instead of just updating local data
              this.loadMenuData()
            },
            (error) => {
              console.error('Error creating menu item:', error)
              this.errorMessage = 'Failed to create menu item. Please try again.'
            }
          )
        }
      })
    }
  }

  /** Add a root level item using dialog */
  addRootItem(): void {
    // Open dialog for creating new root menu item
    const dialogRef = this.dialog.open(MenuItemDialogComponent, {
      width: '50%',
      data: {
        parentId: '0',
        isEdit: false,
        availableScopes: [] // You can load available scopes here if needed
      }
    })

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Call API to create the menu item
        this.menuApiService.createMenuItem(result).subscribe(
          (response) => {
            console.log('Root menu item created:', response)

            // Reload the entire menu data
            this.loadMenuData()
          },
          (error) => {
            console.error('Error creating root menu item:', error)
            this.errorMessage = 'Failed to create root menu item. Please try again.'
          }
        )
      }
    })
  }

  /** Start editing an item using dialog */
  startEditing(node: MenuItemFlatNode): void {
    const nestedNode = this.flatNodeMap.get(node)
    if (nestedNode && nestedNode.id) {
      // Fetch the full menu item data from API
      this.menuApiService.getMenuItemById(nestedNode.id).subscribe(
        (response) => {
          // Get the actual menu item from the response data
          const menuItemData = response.data

          // Open dialog for editing menu item with actual data from API
          const dialogRef = this.dialog.open(MenuItemDialogComponent, {
            width: '50%',
            data: {
              parentId: '0', // Not needed for edit
              menuItem: menuItemData,
              isEdit: true
            }
          })

          dialogRef.afterClosed().subscribe((result) => {
            if (result) {
              // Call API to update the menu item
              this.menuApiService.updateMenuItem(nestedNode.id, result).subscribe(
                (updateResponse) => {
                  console.log('Menu item updated:', updateResponse)

                  // Reload the entire menu data
                  this.loadMenuData()
                },
                (error) => {
                  console.error('Error updating menu item:', error)
                  this.errorMessage = 'Failed to update menu item. Please try again.'
                }
              )
            }
          })
        },
        (error) => {
          console.error('Error fetching menu item details:', error)
          this.errorMessage = 'Failed to fetch menu item details. Please try again.'
        }
      )
    }
  }

  /** Save the node to database */
  saveNode(node: MenuItemFlatNode, itemValue: string) {
    // This is now replaced by dialog-based editing
    node.isEditing = false
  }

  /** Cancel editing */
  cancelEditing(node: MenuItemFlatNode): void {
    // This is now handled by the dialog's cancel button
    node.isEditing = false
  }

  /** Delete a node */
  deleteNode(node: MenuItemFlatNode): void {
    if (!node || !node.id) {
      return
    }

    this.dialogService.confirm(
      {
        title: 'Delete Confirmation',
        message: `Are you sure you want to delete "${node.item}"?`,
        acceptBtn: 'Delete',
        closeBtn: 'Cancel'
      },
      (result: boolean) => {
        if (result) {
          this.menuApiService.deleteMenuItem(node.id).subscribe(
            () => {
              console.log('Menu item deleted successfully')

              // Reload the entire menu data instead of just removing locally
              this.loadMenuData()
            },
            (error) => {
              console.error('Error deleting menu item:', error)
              this.errorMessage = 'Failed to delete menu item. Please try again.'
            }
          )
        }
      }
    )
  }

  /** Copy a menu item */
  copyMenu(node: MenuItemFlatNode): void {
    if (!node || !node.id) {
      return
    }

    // Get the original node data
    const originNode = this.flatNodeMap.get(node)
    if (!originNode || !originNode.originalData) return

    // Fetch the full menu item data from API
    this.menuApiService.getMenuItemById(originNode.id).subscribe(
      (response) => {
        // Get the actual menu item from the response data
        const menuItemData = response.data

        // Open dialog for copying menu item with actual data from API
        const dialogRef = this.dialog.open(MenuItemDialogComponent, {
          width: '50%',
          data: {
            parentId: menuItemData.attributes?.parentId?.[0] || '0',
            menuItem: menuItemData,
            isEdit: false,
            isCopy: true
          }
        })

        dialogRef.afterClosed().subscribe((result) => {
          if (result) {
            // Call API to create the copied menu item
            this.menuApiService.createMenuItem(result).subscribe(
              (response) => {
                console.log('Menu item copied:', response)
                this.dialogService.success(
                  {
                    title: 'Thành công',
                    message: 'Sao chép menu thành công!'
                  },
                  () => {
                    this.loadMenuData()
                  }
                )
              },
              (error) => {
                console.error('Error copying menu item:', error)
                this.dialogService.error({
                  title: 'Lỗi',
                  message: 'Sao chép menu thất bại!'
                })
              }
            )
          }
        })
      },
      (error) => {
        console.error('Error fetching menu item for copy:', error)
        this.dialogService.error({
          title: 'Lỗi',
          message: 'Không thể tải dữ liệu menu để sao chép!'
        })
      }
    )
  }

  onMenuNodeClick(node: any) {
    this.selectedResource = node
    // Nếu muốn cập nhật scopes theo resource, xử lý tại đây
  }
  openScopePermissionDialog(node?: any): void {
    // Lấy danh sách menu (resource) từ dataSource
    const resourceList = this.dataSource.data
    // Giả lập danh sách scope mẫu, thực tế có thể lấy theo resource hoặc API riêng
    const scopes = ['read', 'write', 'delete']
    this.dialog.open(ScopePermissionDialogComponent, {
      width: '50%',
      disableClose: true,
      data: { resources: resourceList, selectedResource: node, scopes }
    })
  }

  // Cập nhật sort cho menu node, chỉ thay đổi sort giữ nguyên các trường khác
  onSortChange(node: MenuItemFlatNode, value: string) {
    const newSort = Number(value)
    if (isNaN(newSort) || newSort === node.sort) return
    // Lấy node gốc từ flatNodeMap
    const originNode = this.flatNodeMap.get(node)
    if (!originNode || !originNode.originalData) return
    // Clone lại object để giữ các trường khác
    const updateData = { ...originNode.originalData }
    // Đảm bảo attributes tồn tại và là object
    updateData.attributes = { ...(updateData.attributes || {}) }
    updateData.attributes.sort = [String(newSort)] // backend nhận mảng string
    // Gọi API update menu (giống logic trong menu-item-dialog.component)
    this.menuApiService.updateMenuItem(updateData._id, updateData).subscribe({
      next: (res) => {
        node.sort = newSort
        this.snackBar.open('Cập nhật thứ tự thành công!', 'Đóng', { duration: 1500 })
      },
      error: () => {
        this.snackBar.open('Cập nhật thứ tự thất bại!', 'Đóng', { duration: 2000 })
      }
    })
  }
  // Lưu sort khi bấm nút save
  saveSort(node: MenuItemFlatNode) {
    const newSort = Number(node._pendingSort)
    if (isNaN(newSort) || newSort === node.sort) return
    // Lấy node gốc từ flatNodeMap
    const originNode = this.flatNodeMap.get(node)
    if (!originNode || !originNode.originalData) return
    // Clone lại object để giữ các trường khác
    const updateData = { ...originNode.originalData }
    // Đảm bảo attributes tồn tại và là object
    updateData.attributes = { ...(updateData.attributes || {}) }
    updateData.attributes.sort = [String(newSort)] // backend nhận mảng string
    // Gọi API update menu
    this.menuApiService.updateMenuItem(updateData._id, updateData).subscribe({
      next: (res) => {
        node.sort = newSort
        this.snackBar.open('Cập nhật thứ tự thành công!', 'Đóng', { duration: 1500 })
        this.loadMenuData()
      },
      error: () => {
        this.snackBar.open('Cập nhật thứ tự thất bại!', 'Đóng', { duration: 2000 })
      }
    })
  }

  // Khi load dữ liệu, đồng bộ _pendingSort = sort
  private syncPendingSortForAllNodes() {
    if (this.treeControl && this.treeControl.dataNodes) {
      this.treeControl.dataNodes.forEach((node) => {
        node._pendingSort = node.sort
      })
    }
  }
}
