<div class="row">
  <div class="col-12">
    <mat-card class="cardWithShadow theme-card">
      <mat-card-header>
        <mat-card-title class="m-b-0">Quản lý menu</mat-card-title>
      </mat-card-header>
      <mat-card-content class="b-t-1 p-y-24">
        <!-- Loading spinner -->
        <div *ngIf="loadingData" class="d-flex justify-content-center m-y-24">
          <mat-spinner diameter="40"></mat-spinner>
        </div>

        <!-- Error message -->
        <div *ngIf="errorMessage" class="alert alert-danger">
          {{ errorMessage }}
          <button mat-button color="primary" (click)="loadMenuData()">Retry</button>
        </div>

        <!-- Add root level menu item button -->
        <div class="mb-3 d-flex justify-content-end" *ngIf="!loadingData && !errorMessage">
          <button mat-raised-button color="primary" (click)="addRootItem()" class="d-flex align-items-center justify-content-center">
            <div class="d-flex align-items-center">
              <i-tabler name="plus" class="icon-18 m-r-4"></i-tabler>
              <span>Thêm menu</span>
            </div>
          </button>
        </div>
        <mat-tree [dataSource]="dataSource" [treeControl]="treeControl" *ngIf="!loadingData && !errorMessage">
          <!-- Leaf Node -->
          <mat-tree-node *matTreeNodeDef="let node; let i = index" matTreeNodeToggle matTreeNodePadding>
            <button mat-icon-button disabled></button>

            <!-- Checkbox for leaf node -->
            <mat-checkbox
              class="checklist-leaf-node"
              [checked]="checklistSelection.isSelected(node)"
              (change)="menuLeafItemSelectionToggle(node)"></mat-checkbox>

            <!-- Node content - either in edit mode or display mode -->
            <div *ngIf="!node.isEditing" class="node-content" (click)="onMenuNodeClick(node)">
              <!--          <i-tabler name="grip-vertical" class="icon-18 m-r-4 cursor-move dnd-drag-handle"></i-tabler>-->
              <!--              <i-tabler *ngIf="node.icon" name="{{ node.icon }}" class="icon-18 m-r-4 node-icon"></i-tabler>-->
              <span class="iconify routeIcon m-r-4 node-icon" [attr.data-icon]="node.icon"></span>
              <span>{{ node.item }}</span>
              <span class="text-muted m-l-10 d-flex align-items-center">
                <input
                  type="number"
                  class="sort-input"
                  [(ngModel)]="node._pendingSort"
                  style="width: 55px; padding: 2px 6px; font-size: 13px; border-radius: 4px; border: 1px solid #ccc; text-align: right" />
                <button *ngIf="node._pendingSort != node.sort" mat-icon-button color="primary" class="sort-save-btn" (click)="saveSort(node)">
                  <i-tabler name="device-floppy" class="icon-16"></i-tabler>
                </button>
              </span>

              <!-- Action buttons -->
              <div class="node-actions">
                <div class="text-muted">{{ node.originalData?.attributes?.route?.[0] }}</div>
                <div class="text-muted m-l-10">{{ node.originalData?.name }}</div>
                <button mat-icon-button class="small-icon-btn" (click)="startEditing(node)" matTooltip="Edit menu item">
                  <i-tabler name="edit" class="icon-18"></i-tabler>
                </button>
                <button mat-icon-button class="small-icon-btn" (click)="deleteNode(node)" matTooltip="Delete menu item">
                  <i-tabler name="trash" class="icon-18"></i-tabler>
                </button>
                <button mat-icon-button class="small-icon-btn" (click)="copyMenu(node)" matTooltip="Sao chép Menu">
                  <i-tabler name="copy" class="icon-18"></i-tabler>
                </button>
                <button mat-icon-button class="small-icon-btn" (click)="openScopePermissionDialog(node)" matTooltip="Phân quyền scope">
                  <i-tabler name="lock-access" class="icon-18"></i-tabler>
                </button>
              </div>
            </div>

            <!-- Edit mode -->
            <div *ngIf="node.isEditing" class="edit-mode">
              <mat-form-field appearance="outline" class="edit-field">
                <input matInput #itemValue [value]="node.item" />
              </mat-form-field>
              <button mat-icon-button class="small-icon-btn" (click)="saveNode(node, itemValue.value)" matTooltip="Save">
                <i-tabler name="check" class="icon-18"></i-tabler>
              </button>
              <button mat-icon-button class="small-icon-btn" (click)="cancelEditing(node)" matTooltip="Cancel">
                <i-tabler name="x" class="icon-18"></i-tabler>
              </button>
            </div>
          </mat-tree-node>

          <!-- Expandable Node -->
          <mat-tree-node *matTreeNodeDef="let node; when: hasChild; let i = index" matTreeNodePadding>
            <!-- Expand/collapse button -->
            <button mat-icon-button matTreeNodeToggle [attr.aria-label]="'Toggle ' + node.item">
              <i-tabler name="{{ treeControl.isExpanded(node) ? 'chevron-down' : 'chevron-right' }}" class="icon-18 mat-icon-rtl-mirror"></i-tabler>
            </button>

            <!-- Checkbox for expandable node -->
            <mat-checkbox
              [checked]="checklistSelection.isSelected(node)"
              [indeterminate]="descendantsPartiallySelected(node)"
              (change)="menuItemSelectionToggle(node)"></mat-checkbox>

            <!-- Node content - either in edit mode or display mode -->
            <div *ngIf="!node.isEditing" class="node-content" (click)="onMenuNodeClick(node)">
              <!--          <i-tabler name="grip-vertical" class="icon-18 m-r-4 cursor-move dnd-drag-handle"></i-tabler>-->
              <!--              <i-tabler *ngIf="node.icon" name="{{ node.icon }}" class="icon-18 m-r-4 node-icon"></i-tabler>-->
              <span class="iconify routeIcon m-r-4 node-icon" [attr.data-icon]="node.icon"></span>
              <span>{{ node.item }}</span>
              <span class="text-muted ms-2" *ngIf="node.attributes && node.attributes.sort && node.attributes.sort.length">
                (sort: {{ node.attributes.sort[0] }})
              </span>

              <!-- Action buttons -->
              <div class="node-actions">
                <button mat-icon-button class="small-icon-btn" (click)="addNewItem(node)" matTooltip="Add child item">
                  <i-tabler name="plus" class="icon-18"></i-tabler>
                </button>
                <button mat-icon-button class="small-icon-btn" (click)="startEditing(node)" matTooltip="Edit menu item">
                  <i-tabler name="edit" class="icon-18"></i-tabler>
                </button>
                <button mat-icon-button class="small-icon-btn" (click)="deleteNode(node)" matTooltip="Delete menu item">
                  <i-tabler name="trash" class="icon-18"></i-tabler>
                </button>
                <button mat-icon-button class="small-icon-btn" (click)="copyMenu(node)" matTooltip="Sao chép Menu">
                  <i-tabler name="copy" class="icon-18"></i-tabler>
                </button>
                <button mat-icon-button class="small-icon-btn" (click)="openScopePermissionDialog(node)" matTooltip="Phân quyền scope">
                  <i-tabler name="lock-access" class="icon-18"></i-tabler>
                </button>
              </div>
            </div>

            <!-- Edit mode -->
            <div *ngIf="node.isEditing" class="edit-mode">
              <mat-form-field appearance="outline" class="edit-field">
                <input matInput #itemValue [value]="node.item" />
              </mat-form-field>
              <button mat-icon-button class="small-icon-btn" (click)="saveNode(node, itemValue.value)" matTooltip="Save">
                <i-tabler name="check" class="icon-18"></i-tabler>
              </button>
              <button mat-icon-button class="small-icon-btn" (click)="cancelEditing(node)" matTooltip="Cancel">
                <i-tabler name="x" class="icon-18"></i-tabler>
              </button>
            </div>
          </mat-tree-node>
        </mat-tree>
        <div class="mt-4">
          <p class="text-sm text-muted">
            Chọn các mục menu để bật/tắt hiển thị trong giao diện quản trị. Các ô checkbox đánh dấu những mục menu đang hoạt động và sẽ được hiển thị
            cho người dùng.
          </p>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
