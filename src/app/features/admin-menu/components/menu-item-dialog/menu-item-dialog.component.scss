.w-100 {
  width: 100%;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.text-hint {
  display: block;
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.6);
  margin-top: -0.5rem;
  margin-bottom: 1rem;
}

.uri-container {
  margin-bottom: 1rem;
}

.uri-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;

  .uri-field {
    flex: 1;
    margin-right: 0.5rem;
  }
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -0.75rem;
  margin-left: -0.75rem;
}

/* <PERSON><PERSON><PERSON> c<PERSON>t có padding đồng nhất */
.col,
.col-1, .col-2, .col-3, .col-4, .col-5, .col-6, 
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6,
.col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

.col-md-3 {
  flex: 0 0 25%;
  max-width: 25%;
}

.col-md-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

.col-md-9 {
  flex: 0 0 75%;
  max-width: 75%;
}

.col-md-12,
.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

@media (max-width: 768px) {
  .col-md-3,
  .col-md-6,
  .col-md-9,
  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}
