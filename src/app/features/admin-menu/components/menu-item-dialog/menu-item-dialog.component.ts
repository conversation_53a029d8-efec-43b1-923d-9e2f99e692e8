import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MenuApiService } from '../../services/menu-api.service';
import { catchError, of } from 'rxjs';

export interface MenuItemDialogData {
  parentId: string;
  menuItem?: any;
  isEdit: boolean;
  isCopy?: boolean;
  availableScopes?: any[];
}

@Component({
  selector: 'app-menu-item-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatIconModule,
    MatSelectModule,
    MatChipsModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './menu-item-dialog.component.html',
  styleUrls: ['./menu-item-dialog.component.scss']
})
export class MenuItemDialogComponent implements OnInit {
  menuItemForm: FormGroup;
  isEdit = false;
  isCopy = false;
  availableScopes: any[] = [];
  loading = false;
  parentMenus: any[] = [];

  constructor(
    private fb: FormBuilder,
    public dialogRef: MatDialogRef<MenuItemDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: MenuItemDialogData,
    private menuApiService: MenuApiService
  ) {
    this.isEdit = data.isEdit;
    this.isCopy = data.isCopy || false;
    this.menuItemForm = this.fb.group({
      name: ['', [Validators.required]],
      displayName: ['', [Validators.required]],
      parentId: [data.parentId || '0'],
      attributes: this.fb.group({
        isMenu: [true],
        sort: [0, [Validators.pattern(/^[0-9]+$/)]],
        route: ['']
      }),
      iconUri: [''], // Di chuyển iconUri ra ngoài attributes
      uris: this.fb.array([this.fb.control('')]),
      scopes: this.fb.array([])
    });
  }

  ngOnInit(): void {
    // Load all menu items for parent selection
    this.loadParentMenus();

    // Load available scopes
    this.loading = true;
    this.menuApiService.getAvailableScopes()
      .pipe(
        catchError(error => {
          console.error('Error loading scopes:', error);
          return of([]);
        })
      )
      .subscribe(scopes => {
        // Handle both array and ApiResponse types
        this.availableScopes = Array.isArray(scopes) ? scopes : scopes.data;
        this.loading = false;
      });

    if ((this.isEdit || this.isCopy) && this.data.menuItem) {
      const menuItem = this.data.menuItem;
      // Patch các trường đơn giản
      this.menuItemForm.patchValue({
        name: this.isCopy ? `${menuItem.name} (Copy)` : menuItem.name,
        displayName: this.isCopy ? `${menuItem.displayName} (Copy)` : menuItem.displayName,
        parentId: menuItem.attributes?.parentId?.[0] || '0',
        attributes: {
          isMenu: menuItem.attributes?.isMenu?.[0] === 'true',
          sort: +(menuItem.attributes?.sort?.[0] || 0),
          route: menuItem.attributes?.route?.[0] || ''
        },
        iconUri: menuItem.iconUri || menuItem.icon_uri || '',
      });

      // Patch uris
      this.urisArray.clear();
      if (Array.isArray(menuItem.uris) && menuItem.uris.length > 0) {
        menuItem.uris.forEach((uri: string) => this.urisArray.push(this.fb.control(uri)));
      } else {
        this.urisArray.push(this.fb.control(''));
      }

      // Patch scopes
      this.scopesArray.clear();
      if (Array.isArray(menuItem.scopes)) {
        menuItem.scopes.forEach((scope: any) => {
          this.scopesArray.push(this.fb.group({ id: scope.id }));
        });
      }
    }
  }

  get urisArray() {
    return this.menuItemForm.get('uris') as FormArray;
  }

  get scopesArray() {
    return this.menuItemForm.get('scopes') as FormArray;
  }

  get isCopyMode() {
    return this.isCopy;
  }

  addUri() {
    this.urisArray.push(this.fb.control(''));
  }

  removeUri(index: number) {
    if (this.urisArray.length > 1) {
      this.urisArray.removeAt(index);
    }
  }

  addScope(scopeId: string) {
    // Check if scope already exists
    const exists = this.scopesArray.value.some((scope: any) => scope.id === scopeId);
    if (!exists) {
      this.scopesArray.push(this.fb.group({ id: scopeId }));
    }
  }

  addAllScopes() {
    if (!this.availableScopes || this.availableScopes.length === 0) {
      return;
    }

    // Clear existing scopes
    while (this.scopesArray.length) {
      this.scopesArray.removeAt(0);
    }

    // Add all available scopes
    this.availableScopes.forEach(scope => {
      this.scopesArray.push(this.fb.group({ id: scope.id }));
    });
  }

  removeScope(index: number) {
    this.scopesArray.removeAt(index);
  }

  getScopeName(scopeId: string): string {
    if (!this.availableScopes) return scopeId;

    const scope = this.availableScopes.find(s => s.id === scopeId);
    return scope ? scope.name : scopeId;
  }

  /**
   * Load all menu items to populate parent menu dropdown
   */
  loadParentMenus(): void {
    this.menuApiService.getAllMenuItems().subscribe(
      response => {
        // Flatten the tree structure for dropdown selection
        this.parentMenus = this.flattenMenuTree(response.data);

        // If editing, remove current item from parent options to prevent circular reference
        if (this.isEdit && this.data.menuItem && this.data.menuItem.id) {
          this.parentMenus = this.parentMenus.filter(item => item.id !== this.data.menuItem.id);
        }
        // --- PATCH: Ensure parentId is set after parentMenus are loaded ---
        if (this.isEdit && this.data.menuItem) {
          // Lấy parentId từ attributes cho đúng
          const parentId = this.data.menuItem.attributes?.parentId?.[0] || '0';
          const parentIdExists = this.parentMenus.some(item => item.id === parentId);
          this.menuItemForm.get('parentId')?.setValue(parentIdExists ? parentId : '0');
        }
      },
      error => {
        console.error('Error loading parent menus:', error);
      }
    );
  }

  /**
   * Flatten hierarchical menu structure into a flat list
   */
  flattenMenuTree(items: any[], level = 0, prefix = ''): any[] {
    let result: any[] = [];

    items.forEach(item => {
      // Add this item with indentation to show hierarchy
      const displayName = prefix + item.displayName;
      result.push({
        id: item.id || item._id,
        displayName: displayName,
        level: level
      });

      // Process children recursively with increased indentation
      if (item.children && item.children.length > 0) {
        const childPrefix = prefix + '-- ';
        const childrenItems = this.flattenMenuTree(item.children, level + 1, childPrefix);
        result = [...result, ...childrenItems];
      }
    });

    return result;
  }

  onSubmit() {
    if (this.menuItemForm.valid) {
      const formValue = this.menuItemForm.value;

      // GỬI SCOPES LÀ MẢNG OBJECT {id: string}
      let scopesObj: { id: string }[] = [];
      if (Array.isArray(formValue.scopes)) {
        scopesObj = formValue.scopes.map((s: any) => {
          if (typeof s === 'string') return { id: s };
          if (s && typeof s === 'object' && s.id) return { id: s.id };
          return null;
        }).filter(Boolean);
      }

      // Ensure attributes object exists
      if (!formValue.attributes) {
        formValue.attributes = {};
      }

      // Convert sort to number if it exists
      if (formValue.attributes.sort !== undefined) {
        formValue.attributes.sort = Number(formValue.attributes.sort);
      } else {
        formValue.attributes.sort = 0;
      }

      // Filter out empty URIs
      formValue.uris = formValue.uris.filter((uri: string) => uri.trim() !== '');

      // Ensure at least one URI
      if (formValue.uris.length === 0) {
        formValue.uris.push('/');
      }

      const scopesName: string[] = scopesObj.map(obj => this.getScopeName(obj.id));
      // Construct result object
      const result = {
        ...formValue,
        parentId: formValue.parentId !== '0' ? formValue.parentId : null,
        scopes: scopesName,
        icon_uri: formValue.iconUri // Đảm bảo trả về icon_uri đúng chuẩn backend
      };
      // Xóa iconUri nếu không cần trả về 2 trường
      delete result.iconUri;

      // Return result to caller
      this.dialogRef.close(result);
    }
  }

  onCancel() {
    this.dialogRef.close();
  }
}
