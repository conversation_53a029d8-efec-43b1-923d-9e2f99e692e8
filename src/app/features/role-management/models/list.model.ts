// Model for listing role assignments

export interface ListRoleAssignment {
  user: {
    self: string | null
    id: string
    origin: string | null
    createdTimestamp: number
    username: string
    enabled: boolean
    totp: boolean
    emailVerified: boolean
    firstName: string
    lastName: string
    email: string
    federationLink: string | null
    serviceAccountClientId: string | null
    attributes: Record<string, string[]>
    credentials: unknown[]
    disableableCredentialTypes: string[]
    requiredActions: string[]
    federatedIdentities: unknown[]
    realmRoles: string[]
    clientRoles: Record<string, string[]>
    clientConsents: unknown[]
    notBefore: number
    applicationRoles: string[]
    socialLinks: unknown[]
    groups: unknown[]
    access: {
      manageGroupMembership: boolean
      view: boolean
      mapRoles: boolean
      impersonate: boolean
      manage: boolean
    }
    userProfileMetadata: unknown
  }
  roles: string[] | null
}
