{"statusCode": null, "httpStatusCode": 200, "description": null, "clientMessageId": "55f855f6-8d45-4b48-b199-50541209af38", "timestamp": *************, "data": {"content": [{"self": null, "id": "31b26b93-6c52-41d8-b47a-4910c4946a25", "origin": null, "createdTimestamp": *************, "username": "**********", "enabled": true, "totp": false, "emailVerified": true, "firstName": "**********", "lastName": "**********", "email": "<EMAIL>", "federationLink": null, "serviceAccountClientId": null, "attributes": null, "credentials": null, "disableableCredentialTypes": [], "requiredActions": [], "federatedIdentities": null, "realmRoles": null, "clientRoles": null, "clientConsents": null, "notBefore": 0, "applicationRoles": null, "socialLinks": null, "groups": null, "access": {"manageGroupMembership": true, "view": true, "mapRoles": true, "impersonate": true, "manage": true}, "userProfileMetadata": null}, {"self": null, "id": "1ea07d3f-a2d6-4dd5-8345-b14016fdcca2", "origin": null, "createdTimestamp": *************, "username": "**********", "enabled": true, "totp": false, "emailVerified": true, "firstName": "**********", "lastName": "**********", "email": "<EMAIL>", "federationLink": null, "serviceAccountClientId": null, "attributes": null, "credentials": null, "disableableCredentialTypes": [], "requiredActions": [], "federatedIdentities": null, "realmRoles": null, "clientRoles": null, "clientConsents": null, "notBefore": 0, "applicationRoles": null, "socialLinks": null, "groups": null, "access": {"manageGroupMembership": true, "view": true, "mapRoles": true, "impersonate": true, "manage": true}, "userProfileMetadata": null}, {"self": null, "id": "2fbc6a1e-7001-4a63-85b3-998204b7454c", "origin": null, "createdTimestamp": *************, "username": "**********", "enabled": true, "totp": false, "emailVerified": true, "firstName": "**********", "lastName": "**********", "email": "<EMAIL>", "federationLink": null, "serviceAccountClientId": null, "attributes": null, "credentials": null, "disableableCredentialTypes": [], "requiredActions": [], "federatedIdentities": null, "realmRoles": null, "clientRoles": null, "clientConsents": null, "notBefore": 0, "applicationRoles": null, "socialLinks": null, "groups": null, "access": {"manageGroupMembership": true, "view": true, "mapRoles": true, "impersonate": true, "manage": true}, "userProfileMetadata": null}, {"self": null, "id": "aed212db-f118-4634-92f6-7b4cc29d7601", "origin": null, "createdTimestamp": *************, "username": "**********", "enabled": true, "totp": false, "emailVerified": true, "firstName": "**********", "lastName": "**********", "email": "<EMAIL>", "federationLink": null, "serviceAccountClientId": null, "attributes": null, "credentials": null, "disableableCredentialTypes": [], "requiredActions": [], "federatedIdentities": null, "realmRoles": null, "clientRoles": null, "clientConsents": null, "notBefore": 0, "applicationRoles": null, "socialLinks": null, "groups": null, "access": {"manageGroupMembership": true, "view": true, "mapRoles": true, "impersonate": true, "manage": true}, "userProfileMetadata": null}, {"self": null, "id": "554be168-cdc4-434e-8a9f-7e79bc179bc8", "origin": null, "createdTimestamp": *************, "username": "**********", "enabled": true, "totp": false, "emailVerified": true, "firstName": "**********", "lastName": "**********", "email": "<EMAIL>", "federationLink": null, "serviceAccountClientId": null, "attributes": null, "credentials": null, "disableableCredentialTypes": [], "requiredActions": [], "federatedIdentities": null, "realmRoles": null, "clientRoles": null, "clientConsents": null, "notBefore": 0, "applicationRoles": null, "socialLinks": null, "groups": null, "access": {"manageGroupMembership": true, "view": true, "mapRoles": true, "impersonate": true, "manage": true}, "userProfileMetadata": null}, {"self": null, "id": "53b331e4-2491-4471-be81-1e4dabec5e66", "origin": null, "createdTimestamp": *************, "username": "**********", "enabled": true, "totp": false, "emailVerified": true, "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "federationLink": null, "serviceAccountClientId": null, "attributes": null, "credentials": null, "disableableCredentialTypes": [], "requiredActions": [], "federatedIdentities": null, "realmRoles": null, "clientRoles": null, "clientConsents": null, "notBefore": 0, "applicationRoles": null, "socialLinks": null, "groups": null, "access": {"manageGroupMembership": true, "view": true, "mapRoles": true, "impersonate": true, "manage": true}, "userProfileMetadata": null}, {"self": null, "id": "5f8e3252-6821-4eab-af27-74190bb035a7", "origin": null, "createdTimestamp": *************, "username": "**********", "enabled": true, "totp": false, "emailVerified": true, "firstName": "**********", "lastName": "**********", "email": "<EMAIL>", "federationLink": null, "serviceAccountClientId": null, "attributes": null, "credentials": null, "disableableCredentialTypes": [], "requiredActions": [], "federatedIdentities": null, "realmRoles": null, "clientRoles": null, "clientConsents": null, "notBefore": 0, "applicationRoles": null, "socialLinks": null, "groups": null, "access": {"manageGroupMembership": true, "view": true, "mapRoles": true, "impersonate": true, "manage": true}, "userProfileMetadata": null}, {"self": null, "id": "8110fa4d-81a1-4a23-aa4f-8ae500dffae3", "origin": null, "createdTimestamp": *************, "username": "**********", "enabled": true, "totp": false, "emailVerified": true, "firstName": "**********", "lastName": "**********", "email": "<EMAIL>", "federationLink": null, "serviceAccountClientId": null, "attributes": null, "credentials": null, "disableableCredentialTypes": [], "requiredActions": [], "federatedIdentities": null, "realmRoles": null, "clientRoles": null, "clientConsents": null, "notBefore": 0, "applicationRoles": null, "socialLinks": null, "groups": null, "access": {"manageGroupMembership": true, "view": true, "mapRoles": true, "impersonate": true, "manage": true}, "userProfileMetadata": null}, {"self": null, "id": "1658a190-9d8e-4f32-bd91-acae3223faff", "origin": null, "createdTimestamp": *************, "username": "**********", "enabled": true, "totp": false, "emailVerified": true, "firstName": "**********", "lastName": "**********", "email": "<EMAIL>", "federationLink": null, "serviceAccountClientId": null, "attributes": null, "credentials": null, "disableableCredentialTypes": [], "requiredActions": [], "federatedIdentities": null, "realmRoles": null, "clientRoles": null, "clientConsents": null, "notBefore": 0, "applicationRoles": null, "socialLinks": null, "groups": null, "access": {"manageGroupMembership": true, "view": true, "mapRoles": true, "impersonate": true, "manage": true}, "userProfileMetadata": null}, {"self": null, "id": "84b51675-3eff-4681-b1dc-71f60bca4564", "origin": null, "createdTimestamp": *************, "username": "**********", "enabled": true, "totp": false, "emailVerified": true, "firstName": "**********", "lastName": "**********", "email": "<EMAIL>", "federationLink": null, "serviceAccountClientId": null, "attributes": null, "credentials": null, "disableableCredentialTypes": [], "requiredActions": [], "federatedIdentities": null, "realmRoles": null, "clientRoles": null, "clientConsents": null, "notBefore": 0, "applicationRoles": null, "socialLinks": null, "groups": null, "access": {"manageGroupMembership": true, "view": true, "mapRoles": true, "impersonate": true, "manage": true}, "userProfileMetadata": null}], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": [], "offset": 0, "paged": true, "unpaged": false}, "totalPages": 4, "totalElements": 37, "last": false, "numberOfElements": 10, "first": true, "size": 10, "number": 0, "sort": [], "empty": false}}