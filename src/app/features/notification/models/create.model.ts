export interface NotificationCreateModel {
  event: string
  title: string
  body: string
  extra: Record<string, any>
  platform: string
  status: string
}
export const PLATFORM_OPTIONS = [
  { key: 'IOS', value: 'iOS' },
  { key: 'ANDROID', value: 'Android' },
  { key: 'ALL', value: 'Tất cả' }
]

export const STATUS_OPTIONS = [
  { key: 'ACTIVE', value: 'Hoạt động' },
  { key: 'INACTIVE', value: 'Không hoạt động' }
]

export const EVENT_OPTIONS = [
  { key: 'PROMOTION', value: '<PERSON><PERSON><PERSON>ến mãi' },
  { key: 'SYSTEM_UPDATE', value: 'Hệ thống' }
]
