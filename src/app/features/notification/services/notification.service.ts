import { Injectable } from '@angular/core'
import { environment } from '@env/environment'
import { HttpClientService, HttpOptions } from '@shared'
import { AppService } from '@services/app.service'
import { PATH_API } from '@features/notification/constants'
import { NotificationCreateModel } from '../models/create.model'
import { NotificationEditModel } from '../models/edit.model'

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  constructor(
    private httpClient: HttpClientService,
    private appService: AppService
  ) {}

  /**
   * Get notification list
   * @param params
   */
  getNotifications(params: any) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.LIST,
      params
    }
    return this.httpClient.get(options)
  }

  /**
   * Create notification
   * @param body
   */
  createNotification(body: NotificationCreateModel) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.CREATE,
      body
    }
    return this.httpClient.post(options)
  }

  /**
   * Get notification detail
   * @param id
   */
  getDetail(id: string) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.LIST}/${id}`
    }
    return this.httpClient.get(options)
  }

  /**
   * Update notification
   * @param id
   * @param body
   */
  updateNotification(id: string, body: NotificationEditModel) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.CREATE}/${id}`,
      body
    }
    return this.httpClient.put(options)
  }

  /**
   * Delete notification
   * @param id
   */
  deleteNotification(id: string) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.DELETE}/${id}`
    }
    return this.httpClient.delete(options)
  }
}
