import { Component, Injector, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core'
import { BUTTON_SAVE, BUTTON_TYPE_BACK, ComponentAbstract, Status } from '@shared'
import { TextControlComponent } from '@shared/components/data-input/text-control/text-control.component'
import { SelectControlComponent } from '@shared/components/data-input/select-control/select-control.component'
import { TextareaControlComponent } from '@shared/components/data-input/textarea-control/textarea-control.component'
import { FormFooterComponent } from '@shared/components/section/form-footer/form-footer.component'
import { debounceTime, finalize, Observable, takeUntil } from 'rxjs'
import { NotificationService } from '../../services/notification.service'
import { ROUTES_NAME_NOTIFICATION } from '../../constants'
import { EVENT_OPTIONS, PLATFORM_OPTIONS, STATUS_OPTIONS } from '../../models/create.model'
import { $formBody, $formEvent, $formExtra, $formPlatform, $formPushNotification, $formStatus, $formTitle, $formUsers } from '../../form/create.form'
import { CheckboxControlComponent } from '@shared/components/data-input'
import { EditorControlComponent } from '@shared/components/data-input/editor-control/editor-control.component'
import { ActivatedRoute } from '@angular/router'

@Component({
  selector: 'app-create-notification',
  standalone: true,
  imports: [TextControlComponent, SelectControlComponent, FormFooterComponent, EditorControlComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './create-notification.component.html',
  styleUrls: ['./create-notification.component.scss']
})
export class CreateNotificationComponent extends ComponentAbstract implements OnInit {
  // Form controls
  $formEvent = $formEvent()
  $formTitle = $formTitle()
  $formBody = $formBody()
  $formPlatform = $formPlatform()
  $formStatus = $formStatus()
  $formUsers = $formUsers()
  $formPushNotification = $formPushNotification()

  listButton: Observable<any[]>

  constructor(
    protected override injector: Injector,
    private service: NotificationService
  ) {
    super(injector)
    this.form.valueChanges.pipe(debounceTime(300)).subscribe((value) => {
      console.log(value)
    })
  }

  protected componentInit(): void {
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_BACK, BUTTON_SAVE)
    // Check if there's an ID in the route parameters
    const id = this.route.snapshot.paramMap.get('id')
    if (id) {
      this.loadNotification(id)
    }
  }

  /**
   * Load notification data from API
   * @param id The notification ID to load
   */
  loadNotification(id: string): void {
    this.indicator.showActivityIndicator(true)
    this.service
      .getDetail(id)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe({
        next: (res: any) => {
          if (res?.data) {
            const data = res.data
            this.form.patchValue({
              event: data.event,
              title: data.title,
              body: data.body,
              platform: data.platform,
              status: data.status,
              userIds: data.userIds || []
            })
          }
        },
        error: (err) => {
          this.showDialogErrorI18n(err?.data?.message, 'Lỗi khi tải thông báo')
        }
      })
  }

  onClickBtn($event: any) {
    switch ($event) {
      case BUTTON_SAVE.typeBtn:
        this.saveForm()
        break
      case BUTTON_TYPE_BACK.typeBtn:
        this.location.back()
        break
    }
  }

  async saveForm() {
    this.validateAllFields(this.form)
    const formData = this.form.getRawValue()

    if (this.form.valid) {
      this.createNotification({ ...formData, push: true })
    }
  }

  /**
   * Call API to create notification
   * @param data
   */
  createNotification(data: any) {
    this.indicator.showActivityIndicator(true)
    this.service
      .createNotification(data)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe({
        next: (res: any) => {
          if (res?.httpStatusCode === Status.SUCCESS) {
            this.form.reset()
            this.goTo(ROUTES_NAME_NOTIFICATION.LIST)
            this.showDialogSuccessI18n('', 'Tạo thông báo <br/>thành công')
          }
        },
        error: (err) => {
          this.showDialogErrorI18n(err?.error?.data?.message, 'Tạo thông báo <br/>thất bại')
        }
      })
  }
}
