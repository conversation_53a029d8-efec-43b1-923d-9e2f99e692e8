import { TableComponentConfig } from '@shared/components/data-display/app-table-tree/app-table.model'
import { D_CURRENCY, D_PERCENT, TextboxItem } from '@shared'

export const config: TableComponentConfig = {
  type: 'table',
  id: 'table-fee-service',
  data: {
    isStaticTable: true,
    disableAutoCallOnChange: true,
    quickSearchFields: [
      {
        key: 'customerCode',
        text: 'Mã KH'
      }
    ],
    apiList: '',
    apiListMethod: 'POST',
    tableTitle: '',
    apiCreate: '',
    displayedColumns: [
      {
        name: 'ID',
        field: 'id',
        path: 'id',
        show: true,
        type: 'textbox'
      },
      {
        name: 'Loại phí',
        field: 'feeName',
        path: 'feeName',
        show: true,
        type: 'textbox'
      },
      {
        name: 'Min',
        field: 'min',
        path: 'min',
        show: true,
        type: 'textbox'
      },
      {
        name: '<PERSON>',
        field: 'max',
        path: 'max',
        show: true,
        type: 'textbox'
      },
      {
        name: '<PERSON><PERSON> cố định',
        field: 'fee',
        path: 'fee',
        show: true,
        type: 'textbox'
      },
      {
        name: 'Phí %',
        field: 'feePercent',
        path: 'feePercent',
        show: true,
        type: 'textbox'
      },
      {
        name: 'Hành động',
        field: 'action',
        path: '',
        show: true
      }
    ],
    pageSize: '10',
    buttonLists: [],
    columnActionLists: [
      {
        icon: 'edit',
        type: 'edit',
        class: '',
        scope: '00_VIEW',
        title: 'Chỉnh sửa',
        routerName: '/fee-service/edit/:id',
        navigationType: 'nav'
      },
      {
        icon: 'trash',
        type: 'delete',
        class: '',
        scope: '03_DELETE',
        title: 'Xóa',
        routerName: ':id',
        navigationType: 'delete'
      }
    ],
    apiDelete: ''
  },
  classes: ''
}
export const formFeeName = () =>
  new TextboxItem({
    key: 'feeName',
    label: 'Tên phí dịch vụ',
    placeholder: 'Nhập tên phí dịch vụ',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
export const formValueFrom = () =>
  new TextboxItem({
    key: 'fromValue',
    label: 'Giá trị từ',
    placeholder: 'Nhập giá trị',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    directives: D_CURRENCY,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
export const formValueTo = () =>
  new TextboxItem({
    key: 'toValue',
    label: 'Giá trị đến',
    placeholder: 'Nhập giá trị',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    directives: D_CURRENCY,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
export const formPermanent = () =>
  new TextboxItem({
    key: 'feeRate',
    label: 'Phí cố định',
    placeholder: 'Nhập số phí cố định',
    value: '',
    required: false,
    countMaxLength: true,
    maxLength: 200,
    directives: D_CURRENCY,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
export const formPercent = () =>
  new TextboxItem({
    key: 'feePercentage',
    label: 'Phí %',
    placeholder: 'Nhập số % phí',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    directives: D_PERCENT,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
