<mat-card class="cardWithShadow">
  <mat-card-content>
    <div class="panel">
      <app-text-control [form]="form" [item]="$formFeeName" class="col-9"></app-text-control>
      <app-fee-range
        #feeRange
        *ngFor="let group of feeRangeList.controls; let i = index"
        [group]="group"
        [index]="i"
        [isLast]="i === feeRangeList.length - 1"
        [showDelete]="feeRangeList.length > 1"
        (addGroup)="addGroup()"
        (removeGroup)="removeGroup($event)"></app-fee-range>
      <app-form-footer [listButton]="listButton" [isFixedBottom]="true" (eventClick)="onClickBtn($event)"></app-form-footer>
    </div>
  </mat-card-content>
</mat-card>
