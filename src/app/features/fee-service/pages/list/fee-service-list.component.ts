import { Component, Injector, ViewChild } from '@angular/core'
import { ComponentAbstract, MessageSeverity } from '@shared'
import { FlexModule } from '@angular/flex-layout'
import { NgForOf } from '@angular/common'
import { FormControl, FormGroup, FormsModule, Validators } from '@angular/forms'
import { MatCard } from '@angular/material/card'
import {
  MatCell,
  MatCellDef,
  MatColumnDef,
  MatHeaderCell,
  MatHeaderCellDef,
  MatHeaderRow,
  MatHeaderRowDef,
  MatRow,
  MatRowDef,
  MatTable,
  MatTableDataSource
} from '@angular/material/table'
import { MatCheckbox } from '@angular/material/checkbox'
import { MatSort } from '@angular/material/sort'
import { MatPaginator } from '@angular/material/paginator'
import { FeeServiceService } from '@features/fee-service/services/fee-service.service'
import { MatIconButton } from '@angular/material/button'
import { TablerIconComponent } from 'angular-tabler-icons'
import { MatTooltip } from '@angular/material/tooltip'
import { AppTableTreeComponent } from '@shared/components/data-display'
import { RouterModule } from '@angular/router'
import { MaterialModule } from 'src/app/material.module'
import { finalize } from 'rxjs'
import { formatCurrency } from '@shared/utils/utils.function'

@Component({
  selector: 'app-fee-service-list',
  templateUrl: './fee-service-list.component.html',
  standalone: true,
  imports: [
    FlexModule,
    FormsModule,
    MatCard,
    MatCell,
    MatCellDef,
    MatColumnDef,
    MatHeaderCell,
    MatHeaderRow,
    MatTable,
    MatSort,
    MatHeaderCellDef,
    MatRow,
    MatRowDef,
    MatHeaderRowDef,
    MatIconButton,
    TablerIconComponent,
    MatTooltip,
    NgForOf,
    MatCheckbox,
    RouterModule,
    MaterialModule
,  ],
  styleUrls: ['./fee-service-list.component.scss']
})
export class FeeServiceListComponent extends ComponentAbstract {
  @ViewChild('table') table: AppTableTreeComponent
  allComplete: boolean = false
  feeList: MatTableDataSource<any[]>
  selectedRows = []
  displayedColumns: string[] = ['id', 'feeName', 'min', 'max', 'fee', 'feePercent', 'status', 'action']
  actionButtons = [
    {
      icon: 'edit',
      type: 'edit',
      class: '',
      scope: '00_VIEW',
      title: 'Chỉnh sửa',
      routerName: '/fee-service/edit/:id',
      navigationType: 'nav'
    },
    {
      icon: 'trash',
      type: 'delete',
      class: '',
      scope: '03_DELETE',
      title: 'Xóa',
      routerName: ':id',
      navigationType: 'delete'
    }
  ]
  @ViewChild(MatSort) sort: MatSort = Object.create(null)
  @ViewChild(MatPaginator) paginator: MatPaginator = Object.create(null)

  form = new FormGroup({
    feeIds: new FormControl([], Validators.required)
  })

  private isInitialLoad: boolean = true

  constructor(
    protected override injector: Injector,
    private service: FeeServiceService
  ) {
    super(injector)
  }

  componentInit(): void {
    this.getList()
  }

  getList() {
    this.service.getListFee().subscribe({
      next: (res: any) => {
        console.log('res:', res?.data?.content)
        const data: any[] = res?.data?.content
        const flatData: any[] = data.map((e: any) => {
          const lengthRange = e?.feeRangeList?.length
          console.log('lengthRange', e.feeRangeList)
          if (lengthRange > 0) {
            return {
              id: e.id,
              feeName: e.feeName,
              min: formatCurrency(e.feeRangeList[0].fromValue),
              max: formatCurrency(e.feeRangeList[lengthRange - 1].toValue),
              fee: `${formatCurrency(e.feeRangeList[0].feeRate)}đ - ${formatCurrency(e.feeRangeList[lengthRange - 1].feeRate)}đ`,
              feePercent: formatCurrency(e.feeRangeList[0].feePercentage),
              status: e.status,
              selected: e.status === 'ACTIVE'
            }
          }
        })
        this.feeList = new MatTableDataSource(flatData)
        this.updateAllComplete()
        this.isInitialLoad = false
      },
      error: (err: any) => {},
      complete: () => {}
    })
  }

  filter(filterValue: string): void {
    this.feeList.filter = filterValue.trim().toLowerCase()
  }

  updateAllComplete(): void {
    this.allComplete = this.feeList.data.every((row: any) => row.selected)
    this.updateSelectedRows()
  }

  someComplete(): any {
    if (this.feeList && this.feeList.data) {
      return this.feeList.data.filter((t) => t).length > 0 && !this.allComplete
    } else {
      return false
    }
  }

  setAll(selected: boolean): void {
    this.allComplete = selected
    this.feeList.data.forEach((row: any) => {
      row.selected = selected
      if (!this.isInitialLoad) {
        // Chỉ gọi API sau khi khởi tạo
        this.applyFee(row)
      }
    })
    this.updateSelectedRows()
  }

  // Xử lý khi một checkbox thay đổi
  onCheckboxChange(row: any): void {
    if (!this.isInitialLoad) {
      // Chỉ gọi API sau khi khởi tạo
      this.applyFee(row)
    }
    this.updateAllComplete()
  }

  updateSelectedRows(): void {
    this.selectedRows = this.feeList.data.filter((row: any) => row.selected)
    const formData = this.selectedRows.map((e) => e.id)
    this.form.controls['feeIds'].setValue(formData)
    this.form.controls['feeIds'].updateValueAndValidity()
  }

  updateSelectedCheckboxes(): void {
    const selectedFeeIds = this.form.controls['feeIds'].value || []
    this.feeList.data.forEach((row: any) => {
      row.selected = selectedFeeIds.includes(row.id)
    })
    this.selectedRows = selectedFeeIds
  }

  applyFee(row: any): void {
    this.service.applyFee({ id: row.id, isApply: row.selected }).subscribe({
      next: (res: any) => {},
      error: (err: any) => {
        this.toastr.showToastri18n(`Có lỗi xảy ra khi thay đổi trạng thái phí: ${row.feeName}`, '', MessageSeverity.error)
      },
      complete: () => {}
    })
  }

  /**
   * Hiển thị dialog xác nhận xóa và thực hiện gọi API xóa nếu xác nhận.
   * @param entityName Tên thực thể (ví dụ: 'loại phí')
   * @param deleteApi Hàm gọi API xóa, trả về Observable
   * @param afterDelete Callback sau khi xóa thành công (ví dụ: reload list)
   */
  confirmDelete(
    entityName: string,
    deleteApi: () => any,
    afterDelete: () => void
  ) {
    this.dialogService.confirm(
      {
        title: `Bạn có chắc muốn xóa <br/>${entityName}?`,
        message: '',
        textButtonRight: 'btn.delete'
      },
      (result: boolean, _dialogRef?: any, _context?: any) => {
        if (result) {
          deleteApi()
            .pipe(finalize(() => {/* hide loading nếu có */}))
            .subscribe(
              () => {
                afterDelete()
                this.toastr.showToastri18n('', `Xóa ${entityName} thành công`, MessageSeverity.success)
              },
              (error: any) => {
                this.toastr.showToastri18n('dialog.delete-data-error', error?.error?.message || 'Có lỗi xảy ra khi xóa', MessageSeverity.error)
              }
            )
        }
      }
    )
  }

  onTableActionClick(button: any, row: any) {
    const { type } = button
    const { id } = row
    if (type === 'edit') {
      this.router.navigate([`/fee-service/edit/${id}`])
    } else if (type === 'delete') {
      this.onDeleteFee(id, row)
    }
  }

  onDeleteFee(id: number, row: any) {
    this.confirmDelete(
      row.feeName || 'loại phí',
      () => this.service.deleteFee(id),
      () => this.getList()
    )
  }
}
