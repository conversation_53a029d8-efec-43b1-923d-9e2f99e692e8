.btn-toggle-menu {
  background-color: #e6e8ee;
  width: 24px !important;
  height: 24px !important;
  border-radius: 50%;
  padding: 0 !important;
  border: 1px solid #e2e2e2;
  position: absolute;
  z-index: 9999;
  &:hover {
    background-color: #d8deef;
  }
  &.left {
    right: 0;
    top: 27px;
    z-index: 99999;
  }
  &.right {
    left: 14px;
    top: 21px;
    z-index: 99999;
  }
}

.tree-scroll {
  height: 100%;
  max-height: 670px;
  min-width: 288px;
}

.closed {
  .btn-toggle-menu {
    display: none;
  }
}

::ng-deep .as-split-gutter-icon {
  background-image: unset !important;
  width: 2px !important;
  &:hover {
    background-color: var(--color-blue-primary);
  }
}
as-split,
as-split-area {
  overflow: visible !important;
}
