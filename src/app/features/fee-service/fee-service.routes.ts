import { Routes } from '@angular/router'

export const routes: Routes = [
  {
    path: '',
    title: '<PERSON>h sách phí dịch vụ',
    loadComponent: async () => (await import('./pages/list/fee-service-list.component')).FeeServiceListComponent,
    data: {
      breadcrumb: '<PERSON>h sách phí dịch vụ'
    }
  },
  {
    path: 'create',
    title: 'Tạo phí dịch vụ',
    loadComponent: async () => (await import('./pages/create/create-fee-service.component')).CreateFeeServiceComponent,
    data: {
      breadcrumb: 'Tạo phí dịch vụ'
    }
  },
  {
    path: 'edit/:id',
    title: 'Chỉnh sửa phí dịch vụ',
    loadComponent: async () => (await import('./pages/edit/edit-fee-service.component')).EditFeeServiceComponent,
    data: {
      breadcrumb: 'Chỉnh sửa phí dịch vụ'
    }
  }
]
