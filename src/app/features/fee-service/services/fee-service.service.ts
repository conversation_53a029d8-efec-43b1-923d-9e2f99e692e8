import { Injectable } from '@angular/core'
import { environment } from '@env/environment'
import { HttpClientService, HttpOptions } from '@shared'
import { AppService } from '@services/app.service'
import { PATH_API } from '@features/fee-service/constants'

@Injectable({
  providedIn: 'root'
})
export class FeeServiceService {
  constructor(
    private httpClient: HttpClientService,
    private appService: AppService
  ) {}

  /**
   * danh sach
   * @param params
   */
  getListFee() {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.LIST
    }
    return this.httpClient.get(options)
  }

  applyFee(body: { id: number; isApply: boolean }) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.APPLY,
      body
    }
    return this.httpClient.put(options)
  }

  getFeeDetail(id: number) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.LIST + '/' + id
    }
    return this.httpClient.get(options)
  }

  createFee(body: any) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.LIST,
      body: body
    }
    return this.httpClient.post(options)
  }

  deleteFee(id: number) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.DELETE + '/' + id
    }
    return this.httpClient.delete(options)
  }

  updateFee(body: any) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.LIST + '/' + body.id,
      body: body
    }
    return this.httpClient.put(options)
  }
}
