import { Component, Injector, isDevMode, ViewChild } from '@angular/core'
import { ComponentAbstract, Status } from '@shared'
import { FlexModule } from '@angular/flex-layout'
import { AppTableBuilderComponent } from '@shared/components/page-builder/app-table-builder/table-builder.component'
import { EmployeeService } from '@features/employee/services/employee.service'
import { takeUntil, finalize } from 'rxjs'

@Component({
  selector: 'app-employee-list',
  templateUrl: './employee-list.component.html',
  standalone: true,
  imports: [AppTableBuilderComponent, FlexModule],
  styleUrls: ['./employee-list.component.scss']
})
export class EmployeeListComponent extends ComponentAbstract {
  @ViewChild('tableBuilderComponent') tableBuilderComponent: AppTableBuilderComponent

  constructor(
    protected override injector: Injector,
    private employeeService: EmployeeService
  ) {
    super(injector)
    this.getUIConfig('management-employee-list').then((r) => {
      this.tableBuilderComponent.panelOpenState = true
      this.tableBuilderComponent.hideButtonClosedAdvancedSearchBox = true
      // setTimeout(() => {
      //   this.tableBuilderComponent.form
      //     .get('createdAtFrom-createdAtTo')
      //     .patchValue({ start: dayjs().subtract(3, 'days'), end: dayjs().add(3, 'days') })
      // }, 500)
    })
  }

  componentInit(): void {}

  onBuilderChanged($event: any) {
    isDevMode() && console.log($event)
  }

  onTableActionClick($event: any) {
    switch ($event.type) {
      case 'delete': {
        this.openDeleteConfirmDialog($event.row)
        break
      }
      default:
        break
    }
  }

  onButtonClick($event: any) {
    console.log($event)
  }

  actionContextMenuClick($event) {
    console.log('somebody', $event)
    switch ($event.type) {
      case 0: {
        break
      }
      default:
        break
    }
  }

  openDeleteConfirmDialog(row: any) {
    this.dialogService.confirm(
      {
        title: 'Xác nhận xóa',
        message: `Bạn có chắc chắn muốn xóa nhân viên "${row.name}" không?`,
        textButtonLeft: 'Hủy',
        textButtonRight: 'Xóa'
      },
      (result) => {
        if (result) {
          this.deleteSalaryAdvanceCategory(row)
        }
      }
    )
  }

  deleteSalaryAdvanceCategory(row: any) {
    this.indicator.showActivityIndicator(true)
    this.employeeService
      .delete(row.id)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe({
        next: (res: any) => {
          if (res?.httpStatusCode === Status.SUCCESS) {
            this.showDialogSuccessI18n('', 'Xóa nhân viên <br/>thành công')
            this.tableBuilderComponent.search({})
          }
        },
        error: (err) => {
          this.showDialogErrorI18n(err?.error?.data?.message, 'Xóa nhân viên <br/>thất bại')
        }
      })
  }
}
