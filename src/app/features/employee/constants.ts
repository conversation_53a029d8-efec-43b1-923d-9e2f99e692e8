/**
 * API PATH
 */
import { environment } from '@env/environment'

export const PATH_API = {
  LIST: `${environment.services.portal}/v1/employee`,
  DELETE: (id: string) => `${environment.services.portal}/v1/employee/${id}/force`,
  CREATE: `${environment.services.portal}/v1/employee`,
  IMPORT_EMPLOYEE: `${environment.services.portal}/v1/employee/import`
}

export const ROUTES_NAME_SYSTEM_MANAGEMENT_EMPLOYEE = {
  LIST: environment.base_path + '/employee',
  CREATE: environment.base_path + '/employee/create',
  EDIT: environment.base_path + '/employee/edit'
}
