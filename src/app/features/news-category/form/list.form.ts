import { FormType } from '@app/shared/models/form.model'
import { FORM_CONTROL_TYPE } from '@app/shared/constants'

export const NEWS_CATEGORY_LIST_FORM: FormType<string>[] = [
  {
    value: '',
    key: 'name',
    label: 'Tên danh mục',
    order: 1,
    controlType: FORM_CONTROL_TYPE.TEXT_BOX,
    focus: false,
    type: 'text',
    title: false,
    customValidate: '',
    countMaxLength: false
  },
  {
    value: '',
    key: 'parentId',
    label: 'Danh mục cha',
    order: 2,
    controlType: FORM_CONTROL_TYPE.DROPDOWN,
    focus: false,
    type: 'text',
    title: false,
    customValidate: '',
    countMaxLength: false
  }
]
