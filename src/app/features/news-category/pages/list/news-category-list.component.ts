import { Component, Injector, ViewChild, isDevMode } from '@angular/core'
import { ComponentAbstract } from '@shared'
import { FlexModule } from '@angular/flex-layout'
import { AppTableBuilderComponent } from '@shared/components/page-builder/app-table-builder/table-builder.component'

import { HttpOptions } from '@shared/models'
import { ROUTES_NEWS_CATEGORY } from '@features/news-category/constants'

@Component({
  selector: 'app-news-category-list',
  templateUrl: './news-category-list.component.html',
  standalone: true,
  imports: [AppTableBuilderComponent, FlexModule],
  styleUrls: ['./news-category-list.component.scss']
})
export class NewsCategoryListComponent extends ComponentAbstract {
  @ViewChild('tableBuilderComponent') tableBuilderComponent: AppTableBuilderComponent

  constructor(protected override injector: Injector) {
    super(injector)
    this.getUIConfig('management-news-category-list').then(() => {
      if (this.tableBuilderComponent) {
        this.tableBuilderComponent.panelOpenState = true
        this.tableBuilderComponent.hideButtonClosedAdvancedSearchBox = true
      }
    })
  }

  componentInit(): void {}

  onBuilderChanged($event: any) {
    isDevMode() && console.log($event)
  }

  onTableActionClick($event: any) {
    switch ($event.type) {
      case 'cellClick': {
        break
      }
      case 'edit':
        this.router.navigate([ROUTES_NEWS_CATEGORY.EDIT($event.row.id)])
        break
      default:
        break
    }
  }

  onButtonClick($event: any) {
    switch ($event.type) {
      case 'create': {
        this.router.navigate([ROUTES_NEWS_CATEGORY.CREATE])
        break
      }
      default:
        console.log($event)
        break
    }
  }

  actionContextMenuClick($event: any) {
    switch ($event.type) {
      case 'edit':
        this.router.navigate([ROUTES_NEWS_CATEGORY.EDIT($event.row.id)])
        break
      case 'delete':
        this.confirmDelete($event.data.id)
        break
      default:
        break
    }
  }

  confirmDelete(id: number): void {
    this.dialogService.confirm(
      {
        title: 'Xác nhận xóa',
        message: 'Bạn có chắc chắn muốn xóa danh mục tin tức này?',
        acceptBtn: 'Xóa',
        closeBtn: 'Hủy'
      },
      (result: boolean) => {
        if (result) {
          this.delete(id)
        }
      }
    )
  }

  delete(id: number): void {
    const options: HttpOptions = {
      url: `/api/v1/news-categories/${id}`
    }
    this.httpClient.delete(options).subscribe(() => {
      if (this.tableBuilderComponent?._appTableBodyComponent) {
        // Use indexing syntax to avoid TypeScript errors
        const tableBody = this.tableBuilderComponent._appTableBodyComponent as any
        if (typeof tableBody.loadData === 'function') {
          tableBody.loadData()
        }
      }
    })
  }
}
