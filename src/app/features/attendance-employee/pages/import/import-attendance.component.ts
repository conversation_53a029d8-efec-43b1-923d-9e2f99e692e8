import { AfterViewInit, Component, Injector, ViewChild } from '@angular/core'
import { BUTTON_TYPE_CANCEL, BUTTON_UPDATE, ComponentAbstract, SharedModule } from '@shared'
import { FormFooterComponent } from '@shared/components/section/form-footer/form-footer.component'
import { ROUTES_NAME_BUSINESS } from '../../../../app.routes'
import { DragDropUploadFileComponent } from '@shared/components/data-input/drag-drop-upload-file/drag-drop-upload-file.component'
import { PATH_API, ROUTES_ATTENDANCE } from '@features/attendance-employee/constants'

@Component({
  selector: 'app-import-employee',
  templateUrl: './import-attendance.component.html',
  standalone: true,
  imports: [FormFooterComponent, DragDropUploadFileComponent, SharedModule],
  styleUrls: ['./import-attendance.component.scss']
})
export class ImportAttendanceComponent extends ComponentAbstract implements AfterViewInit {
  @ViewChild('uploadFileComponent') uploadFileComponent: DragDropUploadFileComponent

  urlImport = PATH_API.IMPORT_DATA

  constructor(protected override injector: Injector) {
    super(injector)
  }

  override ngAfterViewInit() {
    super.ngAfterViewInit()
  }

  componentInit(): void {
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_CANCEL, BUTTON_UPDATE)
  }

  onSave() {
    if (!this.uploadFileComponent.fileUpload?.length) {
      this.showDialogErrorI18n('validations.required-import', '')
    } else {
      this.uploadFileComponent.onImportFile()
    }
  }

  onCancel() {
    this.dialogService.confirm(
      {
        title: this.translateService.instant('dialog.confirm'),
        message: this.translateService.instant('dialog.confirm-cancel-import')
      },
      (result) => {
        if (result) {
          this.goTo(ROUTES_ATTENDANCE.LIST)
        }
      }
    )
  }

  onClickBtn($event: any) {
    switch ($event) {
      case BUTTON_TYPE_CANCEL.typeBtn:
        this.onCancel()
        break
      case BUTTON_UPDATE.typeBtn:
        this.onSave()
        break
    }
  }

  fileChanged($event: any[]) {}

  onChangedImport($event: any) {
    if ($event?.import) {
      this.goTo(ROUTES_ATTENDANCE.LIST)
    }
  }
}
