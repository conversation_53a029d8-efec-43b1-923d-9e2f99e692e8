import { CheckboxItem, DateRangeItem, NgSelectItem, TextAreaItem, TextboxItem, TreeDropdownItem } from '@shared'
import { environment } from '@env/environment'

export const formName = () =>
  new TextboxItem({
    key: 'name',
    label: 'Tên nhân viên',
    placeholder: 'Nhập tên nhân viên',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
export const formEmployeeCode = () =>
  new TextboxItem({
    key: 'code',
    label: 'Mã nhân viên',
    placeholder: 'Nhập mã nhân viên',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
export const formPhone = () =>
  new TextboxItem({
    key: 'phoneNumber',
    label: 'SĐT',
    placeholder: 'Nhập SĐT',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
export const formEmail = () =>
  new TextboxItem({
    key: 'email',
    label: 'Email',
    placeholder: 'Nhập email',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
export const formGender = () =>
  new NgSelectItem({
    key: 'gender',
    label: 'Giới tính',
    placeholder: 'Chọn giới tính',
    value: '',
    options: [
      {
        key: '0',
        value: 'Nam',
        checked: false
      },
      {
        key: '1',
        value: 'Nữ',
        checked: false
      }
    ],
    required: true,
    controlType: 'ngselect',
    type: 'single',
    clearable: true,
    customValidate: 'customMessageError'
  })
export const formBirthDate = () =>
  new DateRangeItem({
    key: 'dateOfBirth',
    label: 'Ngày sinh',
    placeholder: 'Chọn ngày sinh',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    singleDatePicker: true,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
export const formContractType = () =>
  new NgSelectItem({
    key: 'contractType',
    label: 'Loại hợp đồng',
    placeholder: 'Chọn loại hợp đồng',
    value: '',
    options: [
      {
        key: 'INTERN',
        value: 'Thực tập sinh',
        checked: false
      },
      {
        key: 'PROBATION',
        value: 'Thử việc',
        checked: false
      },
      {
        key: 'LONG_TERM',
        value: 'Dài hạn',
        checked: false
      },
      {
        key: 'SHORT_TERM',
        value: 'Ngắn hạn',
        checked: false
      },
      {
        key: 'PERMANENT',
        value: 'Vô thời hạn',
        checked: false
      }
    ],
    required: true,
    controlType: 'ngselect',
    type: 'single',
    clearable: true,
    customValidate: 'customMessageError'
  })
export const fieldStatus = () =>
  new NgSelectItem({
    key: 'status',
    label: 'Trạng thái',
    placeholder: 'Chọn trạng thái',
    value: '0',
    options: [
      {
        key: 'ACTIVE',
        value: 'Đang làm việc',
        checked: true
      },
      {
        key: 'INACTIVE',
        value: 'Nghỉ việc',
        checked: true
      },
      {
        key: 'PAUSED',
        value: 'Pending',
        checked: true
      },
      {
        key: 'BLOCKED',
        value: 'Blocked',
        checked: true
      }
    ],
    required: true,
    controlType: 'ngselect',
    type: 'single',
    clearable: true,
    customValidate: 'customMessageError'
  })
export const formGTTT = () =>
  new TextboxItem({
    key: 'identifierNo',
    label: 'Số CCCD/Hộ chiếu',
    placeholder: 'Nhập số CCCD/Hộ chiếu',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
export const formIssueDate = () =>
  new DateRangeItem({
    key: 'issueDate',
    label: 'Ngày cấp',
    placeholder: 'Chọn ngày cấp',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    singleDatePicker: true,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
export const formIssuePlace = () =>
  new TextboxItem({
    key: 'issuePlace',
    label: 'Nơi cấp',
    placeholder: 'Nhập nơi cấp',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })

export const formLimit = () =>
  new NgSelectItem({
    key: 'creditlimit',
    label: 'Hạn mức',
    placeholder: 'Chọn hạn mức',
    value: '75',
    options: [
      {
        key: '50',
        value: '50%',
        checked: true
      },
      {
        key: '55',
        value: '55%',
        checked: true
      },
      {
        key: '60',
        value: '60%',
        checked: true
      },
      {
        key: '65',
        value: '65%',
        checked: true
      },
      {
        key: '70',
        value: '70%',
        checked: true
      },
      {
        key: '75',
        value: '75%',
        checked: true
      },
      {
        key: '80',
        value: '80%',
        checked: true
      },
      {
        key: '85',
        value: '85%',
        checked: true
      },
      {
        key: '90',
        value: '90%',
        checked: true
      },
      {
        key: '95',
        value: '95%',
        checked: true
      },
      {
        key: '100',
        value: '100%',
        checked: true
      }
    ],
    required: true,
    controlType: 'ngselect',
    type: 'single',
    clearable: true,
    customValidate: 'customMessageError'
  })
export const formSalary = () =>
  new TextboxItem({
    key: 'salary',
    label: 'Lương (NET)',
    placeholder: 'Nhập lương',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
export const formPosition = () =>
  new TextboxItem({
    key: 'position',
    label: 'Chức vụ',
    placeholder: 'Nhập chức vụ',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
export const formTeam = () =>
  new TextboxItem({
    key: 'team',
    label: 'Tổ/Nhóm/Đội',
    placeholder: 'Nhập tổ/nhóm/đội',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
export const formDepartment = () =>
  new TreeDropdownItem({
    key: 'departmentId',
    label: 'Phòng ban',
    placeholder: 'Chọn phòng ban',
    value: undefined,
    options: [],
    required: true,
    paramData: {
      url: `${environment.services.portal}/v1/department`,
      key: 'id',
      value: ['name'],
      typeheadKey: 'name',
      preLoad: true
    },
    type: 'single',
    clearable: true,
    customValidate: 'customMessageError'
  })
export const fieldBank = () =>
  new TreeDropdownItem({
    key: 'bankCode',
    label: 'Ngân hàng',
    placeholder: 'Chọn ngân hàng',
    value: undefined,
    options: [],
    required: true,
    paramData: {
      url: `${environment.services.salary}/v1/operations/bank`,
      key: 'code',
      value: ['shortName'],
      subValue: ['fullName'],
      typeheadKey: 'name',
      preLoad: true
    },
    type: 'single',
    clearable: true,
    customValidate: 'customMessageError'
  })
export const formAccountNumber = () =>
  new TextboxItem({
    key: 'bankAccountNumber',
    label: 'Số tài khoản',
    placeholder: 'Nhập số tài khoản',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
export const formAllowChange = () =>
  new CheckboxItem({
    key: 'canEditAccount',
    label: 'Cho phép thay đổi',
    checkBoxKey: 'key',
    hideValueCheckBox: true,
    options: [
      {
        key: '1',
        value: ''
      }
    ],
    value: undefined,
    required: true
  })
export const formNote = () =>
  new TextAreaItem({
    key: 'note',
    label: 'Ghi chú',
    placeholder: 'Nhập ghi chú',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 1000,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập thông tin bắt buộc'
  })
export const txtAddressField = () =>
  new TextAreaItem({
    key: 'address',
    label: 'Địa chỉ',
    placeholder: 'Nhập địa chỉ',
    value: '',
    minRow: '1',
    required: false,
    maxLength: 200,
    countMaxLength: true
  })
