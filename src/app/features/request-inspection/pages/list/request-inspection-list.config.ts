import { environment } from '@env/environment'
import { REQUEST_INSPECTION_STATUS } from '../../constants'

export const requestInspectionListConfig: any = [
  {
    id: 'content',
    type: '_container',
    items: ['table-1'],
    classes: 'flex-column panel'
  },
  {
    id: 'searchAdvanced',
    type: '_container',
    items: ['formControl-1', 'formControl-2'],
    classes: 'd-flex flex-row'
  },
  {
    id: 'formControl-1',
    data: {
      key: 'employeeId',
      type: 'single',
      focus: false,
      label: 'ID nhân viên',
      order: 1,
      reset: false,
      title: false,
      value: '',
      layout: '100',
      minRow: '2',
      checked: false,
      options: [],
      pattern: '',
      readOnly: false,
      required: false,
      template: '',
      updateOn: '',
      clearable: false,
      maxLength: 200,
      minLength: null,
      paramData: {
        key: '',
        url: '',
        isTag: false,
        value: '',
        preLoad: true,
        typeheadKey: '',
        defaultKeySearch: ''
      },
      upperCase: false,
      directives: '',
      checkBoxKey: 'value',
      controlType: 'textbox',
      placeholder: 'Nhập ID nhân viên',
      countMaxLength: true,
      customValidate: ['customMessageError'],
      tooltipMessage: '',
      requiredMessage: '',
      customDirectives: '',
      placeHolderSearch: ''
    },
    type: 'formControl',
    classes: 'w-50'
  },
  {
    id: 'formControl-2',
    data: {
      key: 'status',
      type: 'single',
      focus: false,
      label: 'Trạng thái',
      order: 2,
      reset: false,
      title: false,
      value: null,
      layout: '100',
      minRow: '2',
      checked: false,
      options: [
        {
          key: REQUEST_INSPECTION_STATUS.NEW,
          value: 'Mới'
        },
        {
          key: REQUEST_INSPECTION_STATUS.PROCESSING,
          value: 'Đang xử lý'
        },
        {
          key: REQUEST_INSPECTION_STATUS.COMPLETED,
          value: 'Hoàn thành'
        }
      ],
      pattern: '',
      readOnly: false,
      required: false,
      template: '',
      updateOn: '',
      clearable: true,
      maxLength: 200,
      minLength: null,
      paramData: {},
      upperCase: false,
      directives: '',
      controlType: 'ngselect',
      placeholder: 'Chọn trạng thái',
      countMaxLength: false,
      customValidate: ['customMessageError'],
      tooltipMessage: '',
      requiredMessage: '',
      customDirectives: '',
      placeHolderSearch: ''
    },
    type: 'formControl',
    classes: 'w-50'
  },
  {
    id: 'table-1',
    data: {
      apiList: `${environment.services.salary}/request-inspection`,
      pageSize: 10,
      apiCreate: '',
      apiDelete: `${environment.services.salary}/request-inspection`,
      isTreeData: false,
      tableTitle: 'Quản lý yêu cầu tra soát',
      buttonLists: [
        {
          icon: 'add',
          name: 'Thêm mới',
          show: true,
          type: 'buttonCreate'
        }
      ],
      apiListMethod: 'GET',
      quickSearchFields: [
        {
          key: 'employeeId',
          text: 'ID nhân viên'
        }
      ],
      displayedColumns: [
        {
          name: 'ID',
          path: 'id',
          show: true,
          field: 'id',
          sort: false,
          filter: true
        },
        {
          name: 'Mã nhân viên',
          path: 'transactionDetail.employeeCode',
          show: true,
          sort: false,
          filter: true,
          type: 'textbox',
          field: 'employeeCode'
        },
        {
          name: 'Tên nhân viên',
          path: 'transactionDetail.employeeName',
          show: true,
          sort: false,
          filter: true,
          type: 'textbox',
          field: 'employeeName'
        },
        {
          name: 'Số tiền ứng',
          path: 'transactionDetail.advanceAmount',
          show: true,
          sort: false,
          filter: true,
          type: 'decimal',
          field: 'advanceAmount'
        },
        {
          name: 'Tài khoản nhận',
          path: 'transactionDetail.destinationAccount',
          show: true,
          sort: false,
          filter: true,
          type: 'textbox',
          field: 'destinationAccount'
        },
        {
          name: 'Ngân hàng nhận',
          path: 'transactionDetail.destinationBankName',
          show: true,
          sort: false,
          filter: true,
          type: 'textbox',
          field: 'destinationBankName'
        },
        {
          name: 'Phí',
          path: 'transactionDetail.totalFee',
          show: true,
          sort: false,
          filter: true,
          type: 'textbox',
          field: 'totalFee'
        },
        {
          name: 'Thực nhận',
          path: 'transactionDetail.actualReceivedAmount',
          show: true,
          sort: false,
          filter: true,
          type: 'decimal',
          field: 'actualReceivedAmount'
        },
        {
          name: 'Trạng thái GD',
          path: 'transactionDetail.status',
          show: true,
          sort: false,
          filter: true,
          type: 'textbox',
          field: 'transactionStatus'
        },
        {
          name: 'Lý do tra soát',
          path: 'reason',
          show: true,
          sort: false,
          filter: true,
          type: 'textbox',
          field: 'reason'
        },
        {
          name: 'Kết quả xử lý',
          path: 'resolutionDetail',
          show: true,
          sort: false,
          filter: true,
          type: 'textbox',
          field: 'resolutionDetail'
        },
        {
          name: 'Trạng thái tra soát',
          path: 'status',
          show: true,
          sort: false,
          filter: true,
          type: 'enumText',
          enumText: [
            {
                "key": "NEW",
                "text": "Mới",
                "class": "app-status-inprocess"
            },
            {
                "key": "PROCESSING",
                "text": "Đang xử lý",
                "class": "app-status-waiting"
            },
            {
                "key": "COMPLETED",
                "text": "Hoàn thành",
                "class": "app-status-completed"
            }
        ],
        field: 'requestStatus'
        },
        {
          name: 'Thao tác',
          path: '',
          show: true,
          field: 'action'
        }
      ],
      columnActionLists: [
        {
          icon: 'edit',
          type: 'buttonEdit',
          navigationType: 'emit',
          routerName: ':component',
          class: '',
          title: 'Chỉnh sửa'
        },
        // {
        //   icon: 'trash',
        //   type: 'buttonDelete',
        //   class: 'text-danger',
        //   navigationType: 'emit',
        //   routerName: ':component',
        //   title: 'Xóa'
        // }
      ]
    },
    type: 'table',
    classes: ''
  }
]
