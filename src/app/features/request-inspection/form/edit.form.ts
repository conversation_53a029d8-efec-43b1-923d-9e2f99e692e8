import { TextAreaItem, NgSelectItem } from '@shared'
import { REQUEST_INSPECTION_STATUS } from '../constants'

export const formStatus = () =>
  new NgSelectItem({
    key: 'status',
    label: 'Trạng thái',
    placeholder: 'Chọn trạng thái',
    value: null,
    required: true,
    options: [
      {
        key: REQUEST_INSPECTION_STATUS.NEW,
        value: 'Mới'
      },
      {
        key: REQUEST_INSPECTION_STATUS.PROCESSING,
        value: 'Đang xử lý'
      },
      {
        key: REQUEST_INSPECTION_STATUS.COMPLETED,
        value: 'Hoàn thành'
      }
    ],
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng chọn trạng thái'
  })

export const formResolutionDetail = () =>
  new TextAreaItem({
    value: '',
    key: 'resolutionDetail',
    label: 'Nội dung xử lý',
    order: 1,
    controlType: 'textarea',
    focus: true,
    type: 'text',
    title: false,
    customValidate: '',
    countMaxLength: false
  })
