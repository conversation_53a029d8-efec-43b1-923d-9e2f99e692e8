/**
 * API PATH
 */
import { environment } from '@env/environment'

export const PATH_API = {
  LIST: `${environment.services.portal}/v1/partner`,
  LIST_FEE: `${environment.services.portal}/v1/fee`,
  CREATE: `${environment.services.portal}/v1/partner`,
  IMPORT_PARTNER: `${environment.services.portal}/v1/partner/import`
}

export const ROUTES_NAME_SYSTEM_MANAGEMENT_PARTNER = {
  LIST: environment.base_path + '/partner',
  CREATE: environment.base_path + '/partner/create',
  EDIT: environment.base_path + '/partner/edit',
  IMPORT: environment.base_path + '/partner/import'
}
