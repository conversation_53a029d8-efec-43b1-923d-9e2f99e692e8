<div class="table-responsive">
  <table mat-table [dataSource]="feeList" matSort class="app-table table-info no-wrap m-t-0 v-middle w-100">
    <ng-container matColumnDef="id">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="f-w-600 f-s-15">Id</th>
      <td mat-cell *matCellDef="let element" class="f-s-14">
        {{ element.id }}
      </td>
    </ng-container>

    <ng-container matColumnDef="feeName">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="f-w-600 f-s-15">Loại phí</th>
      <td mat-cell *matCellDef="let element" class="f-s-14">
        {{ element.feeName }}
      </td>
    </ng-container>

    <ng-container matColumnDef="min">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="f-w-600 f-s-15">Min</th>
      <td mat-cell *matCellDef="let element" class="f-s-14">
        {{ element.min | number: '1.0-0' }}
      </td>
    </ng-container>
    <ng-container matColumnDef="max">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="f-w-600 f-s-15">Max</th>
      <td mat-cell *matCellDef="let element" class="f-s-14">
        {{ element.max | number: '1.0-0' }}
      </td>
    </ng-container>
    <ng-container matColumnDef="fee">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="f-w-600 f-s-15">Phí</th>
      <td mat-cell *matCellDef="let element" class="f-s-14">
        {{ element.fee }}
      </td>
    </ng-container>
    <ng-container matColumnDef="chk">
      <th mat-header-cell *matHeaderCellDef>
        Chọn
      </th>
      <td mat-cell *matCellDef="let element" class="f-s-14">
        <mat-checkbox [(ngModel)]="element.selected" (ngModelChange)="updateAllComplete()" color="primary"></mat-checkbox>
      </td>
    </ng-container>
    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>
</div>
<p *ngIf="form.controls['feeIds'].invalid && form.controls['feeIds'].touched" style="color: red; margin-top: 10px">
  Vui lòng chọn ít nhất một loại phí giao dịch!
</p>
