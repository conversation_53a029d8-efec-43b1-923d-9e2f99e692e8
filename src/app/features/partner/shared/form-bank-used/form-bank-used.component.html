<form [formGroup]="form" class="form-partner-container">
  <div formArrayName="accounts">
    <div *ngFor="let account of accounts.controls; let i = index" [formGroup]="account" class="account-row">
      <app-tree-select-control class="col-3" [item]="$fieldBank" [form]="account"></app-tree-select-control>
      <app-text-control [item]="$formAccountNumber" class="col-3" [form]="account"></app-text-control>
      <app-select-control [item]="$fieldAccountType" class="col-3" [form]="account"></app-select-control>
      <div class="col-3 d-flex align-items-center">
        <mat-checkbox formControlName="isUsed" (change)="onSelectChange(i)">
          Sử dụng
        </mat-checkbox>
      </div>
    </div>
  </div>
</form>
