import { Component, Injector, ViewChild } from '@angular/core'
import { BUTTON_SAVE, BUTTON_TYPE_BACK, ComponentAbstract, HttpOptions, MessageSeverity } from '@shared'
import {
  fieldAddress,
  fieldStatus,
  formAbbreviatedName,
  formBusinessName,
  formCompleteDeadline,
  formContractNumber,
  formDateEstablishment,
  formEmail,
  formLimit,
  formLockDay,
  formMST,
  formNote,
  formPayday,
  formPhone,
  formSignedDate,
  formStandardWorkingDay
} from '@features/partner/shared/config'
import { DateRangeControlComponent, SelectControlComponent, TextControlComponent } from '@shared/components/data-input'
import { MatNativeDateModule } from '@angular/material/core'
import { TextareaControlComponent } from '@shared/components/data-input/textarea-control/textarea-control.component'
import { FormBankUsedComponent } from '@features/partner/shared/form-bank-used/form-bank-used.component'
import { TableFeeServiceComponent } from '@features/partner/shared/table-fee-service/table-fee-service.component'
import { ContactInfoComponent } from '@features/partner/shared/contact-info/contact-info.component'
import { FormFooterComponent } from '@shared/components/section/form-footer/form-footer.component'
import { finalize, Observable, takeUntil } from 'rxjs'
import { BtnFooter } from '../../../../shared/models'
import { PartnerService } from '@features/partner/services/partner.service'
import { ROUTES_NAME_SYSTEM_MANAGEMENT_PARTNER } from '@features/partner/constants'
import { SectionTitleComponent } from '@shared/components/section-title/section-title.component'
import { ImageFileDragDropComponent } from '@shared/components/data-input/image-file-drag-drop/image-file-drag-drop.component'
import { environment } from '@env/environment'
import { blobToJson, parseTimeStamp } from '../../../../shared/utils'
import { AppSelectAddressControlComponent } from '@shared/components/data-input/app-select-address-control/app-select-address-control.component'
import { DateControlComponent } from '@shared/components/data-input/date-control/date-control.component'

@Component({
  selector: 'app-create-partner',
  imports: [
    TextControlComponent,
    SelectControlComponent,
    MatNativeDateModule,
    TextareaControlComponent,
    FormBankUsedComponent,
    FormBankUsedComponent,
    TableFeeServiceComponent,
    ContactInfoComponent,
    FormFooterComponent,
    ImageFileDragDropComponent,
    AppSelectAddressControlComponent,
    SectionTitleComponent,
    DateControlComponent
  ],
  templateUrl: './create-partner.component.html',
  standalone: true,
  styleUrl: './create-partner.component.scss'
})
export class CreatePartnerComponent extends ComponentAbstract {
  $formBusinessName = formBusinessName()
  $formAbbreviatedName = formAbbreviatedName()
  $formMST = formMST()
  $formPhone = formPhone()
  $formEmail = formEmail()
  $formDateEstablishment = formDateEstablishment()
  $fieldStatus = fieldStatus()

  $formStandardWorkingDay = formStandardWorkingDay()
  $formPayday = formPayday()
  $formLockDay = formLockDay()
  $formCompleteDeadline = formCompleteDeadline()
  $formLimit = formLimit()
  $formContractNumber = formContractNumber()
  $formSigningDate = formSignedDate()
  $fieldAddress = fieldAddress()

  listButton: Observable<BtnFooter[]>

  $formNote = formNote()

  @ViewChild('address') address: AppSelectAddressControlComponent
  @ViewChild('bank') bank: FormBankUsedComponent
  @ViewChild('fee') fee: TableFeeServiceComponent
  @ViewChild('userInfo') userInfo: ContactInfoComponent
  private fileAvatar: File

  constructor(
    protected override injector: Injector,
    private service: PartnerService
  ) {
    super(injector)
  }

  protected componentInit(): void {
    // this.form = this.itemControl.toFormGroup([
    //   this.$formBusinessName,
    //   this.$formAbbreviatedName,
    //   this.$formMST,
    //   this.$formPhone,
    //   this.$formEmail,
    //   this.$formDateEstablishment,
    //   this.$fieldStatus,
    //   this.$formStandardWorkingDay,
    //   this.$formPayday,
    //   this.$formLockDay,
    //   this.$formCompleteDeadline,
    //   this.$formLimit,
    //   this.$formContractNumber,
    //   this.$formSigningDate,
    //   this.$fieldAddress
    // ])

    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_BACK, BUTTON_SAVE)
  }

  onClickBtn($event: any) {
    switch ($event) {
      case BUTTON_SAVE.typeBtn:
        this.saveForm()
        break
      case BUTTON_TYPE_BACK.typeBtn:
        this.location.back()
        break
    }
  }

  async saveForm() {
    let isValid = true

    this.validateAllFields(this.form)
    const formData = this.form.getRawValue()
    if (this.form.invalid) {
      isValid = false
    }

    this.validateAllFields(this.bank.form)
    if (this.bank.form.invalid) {
      isValid = false
    }

    this.validateAllFields(this.userInfo.form)
    // this.userInfo.validateForm()
    if (this.userInfo.form.invalid) {
      isValid = false
    }

    // this.validateAllFormFields(this.fee.form);
    // if (this.fee.form.invalid) {
    //   isValid = false;
    // }
    const data = {
      ...formData,
      ...this.bank.form.getRawValue(),
      ...this.userInfo.form.getRawValue(),
      ...this.fee.form.getRawValue(),
      foundingDate: parseTimeStamp(formData[this.$formDateEstablishment.key]),
      contract: {
        contractNo: formData[this.$formContractNumber.key],
        signedDate: parseTimeStamp(formData[this.$formSigningDate.key]),
        attachedFile: 'contract-isi-2024-005.pdf'
      },
      address: [formData.address]
    }
    if (isValid) {
      if (this.fileAvatar instanceof File) {
        const rsUploadFile: any = await this.uploadImage(this.fileAvatar)
        if (rsUploadFile?.data.details?.path) {
          const fileId = rsUploadFile?.data?.details?.id
          // const filePath = rsUploadFile?.data.details?.path
          this.createPartner({ ...data, logoId: fileId })
        }
      } else {
        this.createPartner(data)
      }
    }
  }

  createPartner(data: any) {
    this.indicator.showActivityIndicator(true)
    this.service
      .createPartner(data)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe({
        next: (value) => {
          this.goTo(`${ROUTES_NAME_SYSTEM_MANAGEMENT_PARTNER.LIST}`)
          this.showDialogSuccessI18n('', 'Thêm mới đối tác <br/>thành công')
        },
        error: (err) => {
          this.showDialogErrorI18n(err?.error?.message, 'Thêm mới đối tác <br/>thất bại')
        },
        complete: () => {}
      })
  }

  fileChanged($event: File[]) {
    if ($event) {
      this.fileAvatar = $event[0]
    }
  }

  uploadImage($event: any) {
    return new Promise((resolve, reject) => {
      const formData: FormData = new FormData()
      formData.append('file', $event)

      const options: HttpOptions = {
        url: environment.hostApi,
        // path: `${this.urlImport}?preview=${preview}`,
        path: `${environment.services.file}/v1/file/upload`,
        params: {}
      }

      this.httpClient
        .uploadFormData(options, formData)
        .pipe(
          takeUntil(this.ngUnsubscribe),
          finalize(() => this.indicator.hideActivityIndicator(true))
        )
        .subscribe({
          next: (res) => {
            console.log('wth omg', res)
            if (res?.body?.type === 'application/json') {
              blobToJson(res.body)
                .then((jsonObject) => {
                  console.log(jsonObject)
                  //save file id to logoId
                  resolve(jsonObject)
                })
                .catch((error) => {
                  console.error('Lỗi khi chuyển đổi Blob sang JSON:', error)
                })
            }
          },
          error: (res) => {
            if (res?.error?.type === 'application/json') {
              blobToJson(res?.error)
                .then((jsonObject) => {
                  this.toastr.showToastri18n(jsonObject?.message, '', MessageSeverity.error)
                })
                .catch((error) => {
                  console.error('Lỗi khi chuyển đổi Blob sang JSON:', error)
                })
            }
          }
        })
    })
  }
}
