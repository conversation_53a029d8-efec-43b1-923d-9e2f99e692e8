import { Injectable } from '@angular/core'
import { environment } from '@env/environment'
import { HttpClientService, HttpOptions } from '@shared'
import { PATH_API } from '../constants'
import { v4 as uuidv4 } from 'uuid'

@Injectable({
  providedIn: 'root'
})
export class ApproveSalaryAdvanceService {
  constructor(
    private httpClient: HttpClientService,
  ) {}

  /**
   * tạo
   * @param body
   */
  retryTransaction(body: any) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.RETRY_TRANSACTION,
      body,
      headers: {
        'traceId': uuidv4()
      }
    }
    return this.httpClient.post(options)
  }
}
