<h2 class="f-w-500 m-b-24" mat-dialog-title>
  Chi tiết giao dịch ứng lương
  <button mat-icon-button class="close-button" (click)="onClose()">
    <i-tabler name="x"></i-tabler>
  </button>
</h2>

<mat-dialog-content class="mat-typography">
  <div class="detail-container">
    <div class="transaction-section">
      <h3>Thông tin giao dịch</h3>
      <div class="info-row">
        <div class="info-label">Mã giao dịch:</div>
        <div class="info-value">{{transactionData?.id}}</div>
      </div>
      <div class="info-row">
        <div class="info-label">Loại giao dịch:</div>
        <div class="info-value">{{transactionData?.transTypeInfo?.transName || 'Dịch vụ ứng lương'}}</div>
      </div>
      <div class="info-row">
        <div class="info-label">Số tiền:</div>
        <div class="info-value">{{formatAmount(transactionData?.advanceAmount)}}</div>
      </div>
      <div class="info-row">
        <div class="info-label">Trạng thái:</div>
        <div class="info-value" [ngClass]="{
          'status-processing': transactionData?.status === 'PROCESSING',
          'status-success': transactionData?.status === 'SUCCESS',
          'status-failed': transactionData?.status === 'FAILED'
        }">{{formatStatus(transactionData?.status)}}</div>
      </div>
      <div class="info-row">
        <div class="info-label">Thời gian tạo:</div>
        <div class="info-value">{{formatDate(transactionData?.createdAt)}}</div>
      </div>
    </div>

    <div class="employee-section">
      <h3>Thông tin nhân viên</h3>
      <div class="info-row">
        <div class="info-label">Mã nhân viên:</div>
        <div class="info-value">{{transactionData?.employeeCode}}</div>
      </div>
      <div class="info-row">
        <div class="info-label">Tên nhân viên:</div>
        <div class="info-value">{{transactionData?.employeeName}}</div>
      </div>
      <div class="info-row">
        <div class="info-label">Phòng ban:</div>
        <div class="info-value">{{transactionData?.departmentName}} {{transactionData?.departmentCode ? '(' + transactionData?.departmentCode + ')' : ''}}</div>
      </div>
    </div>

    <div class="banking-section">
      <h3>Thông tin ngân hàng</h3>
      <div class="info-row">
        <div class="info-label">Tài khoản nhận:</div>
        <div class="info-value">{{transactionData?.destinationAccount}}</div>
      </div>
      <div class="info-row">
        <div class="info-label">Ngân hàng:</div>
        <div class="info-value">{{transactionData?.destinationBankName}}</div>
      </div>
    </div>

    <div class="trans-info-section" *ngIf="transactionData?.transInfo">
      <h3>Thông tin giao dịch chi tiết</h3>
      
      <div class="trans-info-container" *ngIf="transactionData?.transInfo?.transferMoneyReq">
        <h4>Thông tin yêu cầu</h4>
        <div class="info-row" *ngIf="transactionData?.transInfo?.transferMoneyReq?.Data?.TransId">
          <div class="info-label">TransId:</div>
          <div class="info-value">{{transactionData?.transInfo?.transferMoneyReq?.Data?.TransId}}</div>
        </div>
        <div class="info-row" *ngIf="transactionData?.transInfo?.transferMoneyReq?.Data?.DateTime">
          <div class="info-label">Thời gian:</div>
          <div class="info-value">{{transactionData?.transInfo?.transferMoneyReq?.Data?.DateTime}}</div>
        </div>
        <div class="info-row" *ngIf="transactionData?.transInfo?.transferMoneyReq?.Data?.CreditorAccount?.SourceName">
          <div class="info-label">Tên nguồn:</div>
          <div class="info-value">{{transactionData?.transInfo?.transferMoneyReq?.Data?.CreditorAccount?.SourceName}}</div>
        </div>
        <div class="info-row" *ngIf="transactionData?.transInfo?.transferMoneyReq?.Data?.CreditorAccount?.SourceNumber">
          <div class="info-label">Số tài khoản:</div>
          <div class="info-value">{{transactionData?.transInfo?.transferMoneyReq?.Data?.CreditorAccount?.SourceNumber}}</div>
        </div>
        <div class="info-row" *ngIf="transactionData?.transInfo?.transferMoneyReq?.Data?.InstructedAmount?.Amount">
          <div class="info-label">Số tiền:</div>
          <div class="info-value">{{formatAmount(transactionData?.transInfo?.transferMoneyReq?.Data?.InstructedAmount?.Amount)}}</div>
        </div>
        <div class="info-row" *ngIf="transactionData?.transInfo?.transferMoneyReq?.Risk?.TransDesc">
          <div class="info-label">Mô tả:</div>
          <div class="info-value">{{transactionData?.transInfo?.transferMoneyReq?.Risk?.TransDesc}}</div>
        </div>
      </div>
      
      <div class="trans-info-container" *ngIf="transactionData?.transInfo?.transferMoneyReply">
        <h4>Thông tin phản hồi</h4>
        <div *ngIf="transactionData?.transInfo?.transferMoneyReply?.errorRes">
          <div class="info-row">
            <div class="info-label">Mã lỗi:</div>
            <div class="info-value">{{transactionData?.transInfo?.transferMoneyReply?.errorRes?.code}}</div>
          </div>
          <div class="info-row">
            <div class="info-label">Thông báo:</div>
            <div class="info-value">{{transactionData?.transInfo?.transferMoneyReply?.errorRes?.message}}</div>
          </div>
        </div>
        <div *ngIf="editorValue">
          <div class="info-row">
            <div class="info-label">Dữ liệu phản hồi:</div>
            <div style="width: 100%; height: 600px">
              <ngx-monaco-editor style="width: 100%; height: 100%" [options]="editorOptions" [(ngModel)]="editorValue"></ngx-monaco-editor>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</mat-dialog-content>