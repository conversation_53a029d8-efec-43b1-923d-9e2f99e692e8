import { Component, Injector, isDevMode, ViewChild } from '@angular/core';
import { ComponentAbstract } from '@shared';
import { FlexModule } from '@angular/flex-layout';
import { AppTableBuilderComponent } from '@shared/components/page-builder/app-table-builder/table-builder.component';
import { DepartmentService } from '../../services/department.service';

@Component({
  selector: 'app-list-department',
  templateUrl: './list-department.component.html',
  standalone: true,
  imports: [AppTableBuilderComponent, FlexModule],
  providers: [DepartmentService],
  styleUrls: ['./list-department.component.scss']
})
export class ListDepartmentComponent extends ComponentAbstract {
  @ViewChild('tableBuilderComponent') tableBuilderComponent: AppTableBuilderComponent;

  constructor(protected override injector: Injector, 
    private departmentService: DepartmentService
  ) {
    super(injector);
    this.getUIConfig('management-department-list').then((r) => {
      this.tableBuilderComponent.panelOpenState = true;
      this.tableBuilderComponent.hideButtonClosedAdvancedSearchBox = true;
    });
  }

  componentInit(): void {}

  onBuilderChanged($event: any) {
    isDevMode() && console.log($event);
  }

  onTableActionClick($event: any) {
    switch ($event.type) {
      case 'cellClick': {
        break;
      }
      default:
        break;
    }
  }

  onButtonClick($event: any) {
    console.log($event);
  }

  actionContextMenuClick($event) {
    console.log('somebody', $event);
    switch ($event.type) {
      case 'edit': {
        this.router.navigate(['/department/create'], {
          queryParams: { id: $event.element.id }
        });
        break;
      }
      case 'delete': {
        if (confirm('Bạn có chắc chắn muốn xóa phòng ban này?')) {
          this.departmentService.deleteDepartment($event.element.id).subscribe(() => {
            this.tableBuilderComponent.handlerLoadData();
          });
        }
        break;
      }
      default:
        break;
    }
  }
}
