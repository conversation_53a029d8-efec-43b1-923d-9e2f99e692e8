import { DateRangeItem, NgSelectItem, TreeDropdownItem } from '@shared'
import { environment } from '@env/environment'
import { FORMAT_DATE_TIME_M } from '../../../shared/utils'

export const fromTimeToTime = () =>
  new DateRangeItem({
    key: 'fromTime-toTime',
    label: 'Từ ngày - Đến ngày',
    placeholder: 'Từ ngày - Đến ngày',
    value: '',
    required: true,
    readOnly: false,
    tooltipMessage: 'Chọn khoảng thời gian tìm kiếm trong tối đa 90 ngày',
    displayFormat: FORMAT_DATE_TIME_M,
    dateLimit: 89,
    timePicker: true
  })

export const campaignId = () =>
  new TreeDropdownItem({
    key: 'campaignIds',
    label: 'Tên campaign/Mã campaign',
    placeholder: 'Chọn tên campaign/mã campaign',
    value: undefined,
    options: [],
    required: false,
    paramData: {
      url: `${environment.services.portal}/v1/campaigns/search`,
      key: 'campaignId',
      value: ['campaignName'],
      subValue: ['campaignCode'],
      typeheadKey: 'keyword',
      preLoad: true
    },
    type: 'multiple',
    clearable: true,
    customValidate: 'customMessageError'
  })

export const productCode = () =>
  new TreeDropdownItem({
    key: 'productCode',
    label: 'Sản phẩm',
    placeholder: 'Chọn tên sản phẩm',
    placeHolderSearch: 'Nhập tên sản phẩm',
    value: undefined,
    options: [],
    required: false,
    paramData: {
      url: `${environment.services.portal}/v1/product-category`,
      key: 'productCode',
      value: ['productName'],
      typeheadKey: 'productName',
      preLoad: true
    },
    type: 'single',
    clearable: true,
    customValidate: 'customMessageError'
  })

export const businessCode = () =>
  new TreeDropdownItem({
    key: 'businessCode',
    label: 'Nghiệp vụ',
    placeholder: 'Chọn tên nghiệp vụ',
    placeHolderSearch: 'Nhập tên nghiệp vụ',
    value: undefined,
    options: [],
    required: false,
    paramData: {
      url: `${environment.services.portal}/v1/business-category`,
      key: 'businessCode',
      value: ['businessName'],
      typeheadKey: 'businessName',
      preLoad: true
    },
    type: 'single',
    clearable: true,
    customValidate: 'customMessageError'
  })

export const departmentCode = () =>
  new TreeDropdownItem({
    key: 'departmentCode',
    label: 'Đơn vị yêu cầu',
    placeholder: 'Chọn đơn vị yêu cầu',
    value: undefined,
    options: [],
    required: false,
    paramData: {
      url: `${environment.services.portal}/v1/department`,
      key: 'departmentCode',
      value: ['departmentName'],
      typeheadKey: 'departmentName',
      preLoad: true
    },
    type: 'single',
    clearable: true,
    customValidate: 'customMessageError'
  })

export const createdBy = () =>
  new TreeDropdownItem({
    key: 'createdBy',
    label: 'Người tạo',
    placeholder: 'Chọn người tạo',
    value: undefined,
    options: [],
    required: false,
    paramData: {
      url: `${environment.services.portal}/v1/employee?isActiveDCMS=1`,
      key: 'adUser',
      value: ['fullName'],
      subValue: ['adUser'],
      typeheadKey: 'keyword',
      preLoad: true
    },
    type: 'multiple',
    clearable: true,
    customValidate: 'customMessageError'
  })

export const campaignStatus = () =>
  new NgSelectItem({
    key: 'campaignStatus',
    label: 'Trạng thái',
    placeholder: 'Chọn trạng thái',
    value: undefined,
    options: [
      {
        key: '1',
        value: 'Triển khai'
      },
      {
        key: '2',
        value: 'Kết thúc'
      }
    ],
    required: false,
    clearable: true
  })
