import { Component, ViewChild, Injector, OnInit } from '@angular/core';
import { ComponentAbstract } from '@shared/abstract/component.abstract';
import { FlexModule } from '@angular/flex-layout';
import { MatButton } from '@angular/material/button';
import { TablerIconComponent } from 'angular-tabler-icons';
import { AppReportTableBuilderComponent } from '@shared/components/page-builder/app-report-table-builder/report-table-builder.component';
import { isDevMode } from '@angular/core';
import { PATH_API } from '@features/report/constants';


@Component({
  selector: 'app-salary-advance-report-list',
  templateUrl: './salary-advance-report-list.component.html',
  standalone: true,
  imports: [FlexModule, AppReportTableBuilderComponent, MatButton, TablerIconComponent],
  styleUrls: ['./salary-advance-report-list.component.scss']
})
export class SalaryAdvanceReportListComponent extends ComponentAbstract implements OnInit {
  @ViewChild('reportTableBuilderComponent') reportTableBuilderComponent: AppReportTableBuilderComponent;

  constructor(protected override injector: Injector) {
    super(injector);
    this.getUIConfig('salary-advance-report-admin-list').then(() => {
      // Configuration initialized
    });
  }

  ngOnInit(): void {
    super.initData();
  }

  componentInit(): void {
    this.elementService.checkViewChildExists('reportTableBuilderComponent', this).then(() => {
      this.reportTableBuilderComponent.panelOpenState = true;
      this.reportTableBuilderComponent.hideButtonClosedAdvancedSearchBox = true;
    });
  }

  onBuilderChanged($event: any) {
    isDevMode() && console.log($event);
  }

  onTableActionClick($event: any) {
    console.log($event);
  }

  onButtonClick($event: any) {
    console.log($event);
  }

  onClickExport() {
    this.reportTableBuilderComponent.onExportExcel(PATH_API.EXPORT_REPORT_LIST_SALARY_ADVANCE_ADMIN, 'BaoCaoUngLuongTongHop')  
  }
}
