import { Component, Injector, ViewChild, isDevMode } from '@angular/core'
import { ComponentAbstract } from '@shared'
import { FlexModule } from '@angular/flex-layout'
import { AppTableBuilderComponent } from '@shared/components/page-builder/app-table-builder/table-builder.component'
import { NewsService } from '../../services/news.service'
import { ROUTES_NEWS } from '../../constants'
import { takeUntil, finalize } from 'rxjs'

@Component({
  selector: 'app-news-list',
  templateUrl: './news-list.component.html',
  standalone: true,
  imports: [AppTableBuilderComponent, FlexModule],
  styleUrls: ['./news-list.component.scss']
})
export class NewsListComponent extends ComponentAbstract {
  @ViewChild('tableBuilderComponent') tableBuilderComponent: AppTableBuilderComponent

  constructor(
    protected override injector: Injector,
    private newsService: NewsService
  ) {
    super(injector)
    this.getUIConfig('management-news-list').then(() => {
      if (this.tableBuilderComponent) {
        this.tableBuilderComponent.panelOpenState = true
        this.tableBuilderComponent.hideButtonClosedAdvancedSearchBox = true
      }
    })
  }

  componentInit(): void {}

  onBuilderChanged($event: any) {
    isDevMode() && console.log($event)
  }

  onTableActionClick($event: any) {
    switch ($event.type) {
      case 'cellClick': {
        // Navigate to edit screen when clicking on table cell
        this.goTo(ROUTES_NEWS.EDIT($event.row.id))
        break
      }
      case 'edit': {
        // Handle edit button click from table actions
        this.goTo(ROUTES_NEWS.EDIT($event.row.id))
        break
      }
      case 'delete': {
        // Handle delete button click from table actions
        this.confirmDelete($event.row.id)
        break
      }
      default:
        break
    }
  }

  onButtonClick($event: any) {
    if ($event.type === 'create') {
      this.goTo(ROUTES_NEWS.CREATE)
    }
  }

  confirmDelete(id: number): void {
    this.dialogService.confirm(
      {
        title: 'Xác nhận xóa',
        message: 'Bạn có chắc chắn muốn xóa tin tức này?',
        acceptBtn: 'Xóa',
        closeBtn: 'Hủy'
      },
      (result: boolean) => {
        if (result) {
          this.delete(id)
        }
      }
    )
  }

  delete(id: number): void {
    this.indicator.showActivityIndicator(true)
    this.newsService
      .deleteNews(id)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe({
        next: () => {
          this.showDialogSuccessI18n('', 'Xóa tin tức <br/>thành công')
          if (this.tableBuilderComponent?._appTableBodyComponent) {
            const tableBody = this.tableBuilderComponent._appTableBodyComponent as any
            if (typeof tableBody.loadData === 'function') {
              tableBody.loadData()
            }
          }
        },
        error: (err) => {
          this.showDialogErrorI18n(err?.error?.message, 'Xóa tin tức <br/>thất bại')
        }
      })
  }
}
