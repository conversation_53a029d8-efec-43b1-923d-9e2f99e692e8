import { Routes } from '@angular/router'

export const routes: Routes = [
  {
    path: '',
    title: '<PERSON>h sách tin tức',
    loadComponent: async () => (await import('./pages/list/news-list.component')).NewsListComponent,
    data: {
      breadcrumb: 'Danh sách tin tức'
    }
  },
  {
    path: 'create',
    title: 'Tạo tin tức',
    loadComponent: async () => (await import('./pages/create/create-news.component')).CreateNewsComponent,
    data: {
      breadcrumb: 'Tạo tin tức'
    }
  },
  {
    path: 'edit/:id',
    title: 'Chỉnh sửa tin tức',
    loadComponent: async () => (await import('./pages/edit/edit-news.component')).EditNewsComponent,
    data: {
      breadcrumb: 'Chỉnh sửa tin tức'
    }
  }
]
