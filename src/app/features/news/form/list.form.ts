import { FormType } from '@app/shared/models/form.model';
import { FORM_CONTROL_TYPE } from '@app/shared/constants';

export const NEWS_LIST_FORM: FormType<string>[] = [
  {
    value: '',
    key: 'title',
    label: 'Tiêu đề tin tức',
    order: 1,
    controlType: FORM_CONTROL_TYPE.TEXT_BOX,
    focus: false,
    type: 'text',
    title: false,
    customValidate: '',
    countMaxLength: false
  },
  {
    value: '',
    key: 'category',
    label: 'Danh mục tin tức',
    order: 2,
    controlType: FORM_CONTROL_TYPE.DROPDOWN,
    focus: false,
    type: 'text',
    title: false,
    customValidate: '',
    countMaxLength: false
  },
  {
    value: '',
    key: 'featured',
    label: 'Tin nổi bật',
    order: 3,
    controlType: FORM_CONTROL_TYPE.DROPDOWN,
    focus: false,
    type: 'boolean',
    title: false,
    customValidate: '',
    countMaxLength: false
  },
  {
    value: '',
    key: 'published',
    label: 'Đ<PERSON> xuất bản',
    order: 4,
    controlType: FORM_CONTROL_TYPE.DROPDOWN,
    focus: false,
    type: 'boolean',
    title: false,
    customValidate: '',
    countMaxLength: false
  }
];