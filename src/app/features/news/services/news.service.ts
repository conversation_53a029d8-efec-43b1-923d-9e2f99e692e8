import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { PATH_API } from '../constants';
import { NewsCreateModel } from '../models/create.model';
import { NewsEditModel } from '../models/edit.model';
import { NewsListModel } from '../models/list.model';
import { environment } from '@env/environment';
import { HttpClientService, HttpOptions } from '@shared';

@Injectable({
  providedIn: 'root'
})
export class NewsService {
  constructor(private httpClient: HttpClientService) {}

  /**
   * Lấy danh sách tin tức có phân trang
   * @param params Tham số tìm kiếm
   * @returns Danh sách tin tức có phân trang
   */
  getNews(params: any): Observable<any> {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.LIST
    };

    if (params) {
      options.params = params;
    }

    return this.httpClient.get(options);
  }

  /**
   * <PERSON><PERSON>y danh sách tin tức nổi bật
   * @param params Tham số tìm kiếm
   * @returns Danh sách tin tức nổi bật
   */
  getFeaturedNews(params: any): Observable<any> {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.FEATURED
    };

    if (params) {
      options.params = params;
    }

    return this.httpClient.get(options);
  }

  /**
   * Lấy thông tin chi tiết tin tức
   * @param id ID tin tức
   * @returns Thông tin chi tiết tin tức
   */
  getNewsById(id: number): Observable<any> {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.DETAIL(id)
    };

    return this.httpClient.get(options);
  }

  /**
   * Tạo mới tin tức
   * @param params Thông tin tin tức cần tạo
   * @returns Thông tin tin tức đã tạo
   */
  createNews(params: NewsCreateModel): Observable<any> {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.CREATE
    };

    if (params) {
      options.body = params;
    }

    return this.httpClient.post(options);
  }

  /**
   * Cập nhật thông tin tin tức
   * @param id ID tin tức
   * @param params Thông tin tin tức cần cập nhật
   * @returns Thông tin tin tức đã cập nhật
   */
  updateNews(id: number, params: Partial<NewsEditModel>): Observable<any> {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.UPDATE(id)
    };

    if (params) {
      options.body = params;
    }

    return this.httpClient.put(options);
  }

  /**
   * Xóa tin tức
   * @param id ID tin tức
   * @returns Kết quả xóa
   */
  deleteNews(id: number): Observable<any> {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.DELETE(id)
    };

    return this.httpClient.delete(options);
  }
}