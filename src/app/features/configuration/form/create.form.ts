import {
  CheckboxItem,
  TextAreaItem,
  TextboxItem
} from '@shared'

export const formKey = () =>
  new TextboxItem({
    key: 'key',
    label: 'Mã cấu hình',
    placeholder: 'Nhập mã cấu hình',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 100,
    upperCase: true,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập mã cấu hình'
  })

export const formValue = () =>
  new TextboxItem({
    key: 'value',
    label: 'Giá trị',
    placeholder: 'Nhập giá trị cấu hình',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 500,
    customValidate: 'customMessageError',
    requiredMessage: 'Vui lòng nhập giá trị cấu hình'
  })

export const formDescription = () =>
  new TextAreaItem({
    key: 'description',
    label: '<PERSON><PERSON> tả',
    placeholder: 'Nhập mô tả cấu hình',
    value: '',
    required: false,
    countMaxLength: true,
    maxLength: 500,
    customValidate: 'customMessageError'
  })

export const formIsActive = () =>
  new CheckboxItem({
    key: 'status',
    label: 'Kích hoạt',
    value: 'false',
    required: false
  })
