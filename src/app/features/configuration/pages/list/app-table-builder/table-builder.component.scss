.search-advanced-panel {
  border: 1px solid #e6e8ee;
  border-radius: 8px;
  padding: 10px;
}
.search-title {
  font-size: 16px;
}

.button-search-advanced {
  height: 40px;
  background-color: #daddfa;
  color: #4f5b89;
  border: 1px solid #b2b8cc;
  margin-bottom: 15px;
  border-radius: 8px;
  width: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
}

::ng-deep .mat-expansion-panel-body {
  padding: 15px 0 !important;
}

.search-box {
  padding: 0;
}
.quick-search-input {
  min-width: 400px;
}
:host ::ng-deep .search-advanced .form-group {
  //padding:0 8px;
}

:host .unset-overflow {
  overflow: unset;
}
