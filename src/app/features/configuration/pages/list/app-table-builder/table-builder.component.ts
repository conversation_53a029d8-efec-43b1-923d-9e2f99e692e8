import { Component, EventEmitter, Injector, Input, Output, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core'
import { MatTableModule } from '@angular/material/table'
import { ComponentAbstract, EVENT_FORM_CONTROL, fields, FORM_CONTROL_TYPE, MessageSeverity, Scopes, search } from '@shared'
import { debounceTime } from 'rxjs'
import { environment } from '@env/environment'
import _ from 'lodash'
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms'
import FileSaver from 'file-saver'
import { MatTooltipModule } from '@angular/material/tooltip'
import { MatButtonModule } from '@angular/material/button'
import { CdkTableModule } from '@angular/cdk/table'
import { MatSortModule } from '@angular/material/sort'
import { MatExpansionModule } from '@angular/material/expansion'
import { MatIconModule } from '@angular/material/icon'
import { FlexModule } from '@angular/flex-layout/flex'
import { CommonModule, NgIf } from '@angular/common'
import { SelectControlComponent, TextControlComponent } from '@shared/components/data-input'
import { ConfigService, TemplateNameDirective, ZoneComponent } from '@tnx/ngx-ui-builder'

import { TableComponentConfig } from '@shared/components/data-display/app-table-tree/app-table.model'
import dayjs from 'dayjs/esm'
import { DynamicSelectColumnComponent } from '@shared/components/data-display/dynamic-select-column/dynamic-select-column.component'
import { MatCardModule } from '@angular/material/card'
import { MatFormFieldModule } from '@angular/material/form-field'
import { RouterModule } from '@angular/router'
import { TablerIconsModule } from 'angular-tabler-icons'
import { MaterialModule } from 'src/app/material.module'
import { FormControlRendererComponent } from '@shared/components/page-builder/form-control-render/form-control-renderer.component'
import { AppTableExpandedComponent } from '@shared/components/data-display/app-table-expand/app-table-expand.component'
import { checkSomeScopes, getQueryParams, splitAndAssignFields, updatedArrayConfig } from '@shared/utils'
import { AppImportExcelDialogComponent } from '@shared/components/data-input/dialog-upload-excel/app-import-excel-dialog.component'
import { AppTableTreeComponent } from '@shared/components/data-display'

@Component({
  selector: 'app-table-builder',
  templateUrl: './table-builder.component.html',
  styleUrls: ['./table-builder.component.scss'],
  standalone: true,

  imports: [
    NgIf,
    FlexModule,
    FormsModule,
    ReactiveFormsModule,
    TextControlComponent,
    SelectControlComponent,
    MatIconModule,
    MatExpansionModule,
    MatTableModule,
    MatCardModule,
    MatSortModule,
    CdkTableModule,
    MatButtonModule,
    MatTooltipModule,
    ZoneComponent,
    TemplateNameDirective,
    AppTableTreeComponent,
    FormControlRendererComponent,
    DynamicSelectColumnComponent,
    MatFormFieldModule,
    RouterModule,
    MatButtonModule,
    TablerIconsModule,
    MaterialModule,
    CommonModule
]
})
export class AppTableBuilderComponent extends ComponentAbstract {
  @Input() prefix = ''
  @Input() configComponent: any
  @Input() hideSearch = false
  @Input() checkScope = false //TODO: true nếu check rule theo scope
  @Input() hideButtonClosedAdvancedSearchBox = false
  @Input() currentQueryFilter = {} // use in case default external query
  @Output() onButtonClick = new EventEmitter<any>()
  @Output() onTableActionClick = new EventEmitter<any>()
  @Output() onChanged = new EventEmitter<any>()
  @Output() onFormInit = new EventEmitter<any>()
  @Input() hideBreadCrumb = false
  @ViewChild('appTableBodyComponent') _appTableBodyComponent: AppTableTreeComponent
  @ViewChild('appTableExpandedComponent') _appTableExpandedComponent: AppTableExpandedComponent
  @ViewChild('outlet', { read: ViewContainerRef }) outletRef: ViewContainerRef
  @ViewChild('content', { read: TemplateRef }) contentRef: TemplateRef<any>
  @ViewChild('dynamicColumnSelector') dynamicColumnSelector: DynamicSelectColumnComponent
  @Output() responseSearchChanged = new EventEmitter<any>()
  @Output() contextMenuClick = new EventEmitter()

  visibleColumns = []
  dataTable = []
  panelOpenState = false
  isSearchAdvanced = false
  isSearchByKeyword = false
  formQuickSearch: FormGroup
  $keyword = search()
  $fields = fields()
  tableConfig: TableComponentConfig
  /**
   * trường hợp thay đổi object config mà ko muốn recall api get database của table thì set true.
   */
  flagPreventRender = false
  configId = ''
  userInfo: any
  constructor(
    protected override injector: Injector,
    private serviceConfig: ConfigService
  ) {
    super(injector)
    this.configId = this.serviceConfig.getComponentId()
    this.store.subscribe((value1: any) => {
      this.userInfo = value1.auth.userInfo
    })
    this.initQuickSearchForm()
    this.form = this.itemControl.toFormGroup([])
    this.formQuickSearch.get(this.$fields.key).valueChanges.subscribe((f) => {
      this.onChangeFields(f)
    })
  }

  private rePaint() {
    this.outletRef?.clear()
    this.outletRef.createEmbeddedView(this.contentRef)
    this.cdRef.markForCheck()
  }

  initQuickSearchForm() {
    this.formQuickSearch = this.itemControl.toFormGroup([this.$keyword, this.$fields])
  }

  get buttonLists() {
    return this.configComponent?.data?.buttonLists || []
  }

  get columnActionLists() {
    return this.configComponent?.data?.columnActionLists || []
  }

  get advancedSearchFields() {
    return this.configComponent?.data?.displayedColumns || []
  }

  get displayedColumns(): any[] {
    return this.visibleColumns.map((c) => c.field)
  }

  componentInit(): void {
    if (this.checkScope) {
      let scopes = this.route.snapshot.data.scopes as Array<Scopes>
      let resources = this.route.snapshot.data.resources as Array<string>
      this.getUserPermissions(resources)
    }
  }

  protected override afterView() {
    super.afterView()
    console.log('current config', this.configService.getAllConfig())

    this.configService
      .watchAllConfig()
      .pipe(debounceTime(500))
      .subscribe((cfg) => {
        if (this.flagPreventRender) return
        if (_.isEqual(this.configComponent, cfg)) return

        this.configComponent = cfg
        this.handlerLoadData()
        this.rePaint()
        this.form = this.itemControl.createFormWithConfigByFormId(cfg, this.configService.buildPrefix(this.prefix, 'searchAdvanced'))

        // Kích hoạt change detection sau khi cập nhật xong
        this.cdRef.detectChanges()
      })
  }

  handlerLoadData() {
    let tableConfigCurrentValue: TableComponentConfig = this.configComponent.find(
      (x) => x.id === this.configService.buildPrefix(this.prefix, 'table-1')
    )
    if (!tableConfigCurrentValue) {
      console.warn('Table config not found, please check')
    }
    const currConfig = JSON.parse(localStorage.getItem(`column-${this.configId}`))
    if (tableConfigCurrentValue?.data?.displayedColumns && Array.isArray(tableConfigCurrentValue?.data?.displayedColumns)) {
      tableConfigCurrentValue = {
        ...tableConfigCurrentValue,
        data: {
          ...tableConfigCurrentValue?.data,
          displayedColumns: updatedArrayConfig(tableConfigCurrentValue.data.displayedColumns, currConfig)
        }
      }
    }

    // const newTableConfigCurrentValue = { ...tableConfigCurrentValue, data: updatedData }
    if (_.isEqual(this.tableConfig, tableConfigCurrentValue)) return
    this.tableConfig = tableConfigCurrentValue
    if (this.tableConfig?.data?.quickSearchFields?.length) {
      this.isSearchByKeyword = false
      this.$fields.options = this.tableConfig?.data.quickSearchFields.map((x) => ({
        key: x.key,
        value: x.text
      }))
      if (this.$fields.options?.length > 0) {
        this.$fields.value = this.$fields.options[0].key
        this.formQuickSearch.get(this.$fields.key).setValue(this.$fields.value)
      }
    } else {
      this.isSearchByKeyword = true
      this.$fields.options = [
        {
          key: 'keyword',
          value: 'Từ khóa'
        }
      ]
      if (this.$fields.options?.length > 0) {
        this.formQuickSearch.get(this.$fields.key).setValue(this.$fields.options[0].key)
      }
    }
  }
  /**
   * handler quick search
   */
  quickSearch(pageIndex: number) {
    if (this.formQuickSearch.invalid) return
    this.pageIndex = pageIndex
    this.isSearchAdvanced = false
    this.filterQuery = this.itemControl.getQueryQuickSearchForm(this.formQuickSearch)
    this.search({ ...this.filterQuery, ...this.currentQueryFilter })
  }

  /**
   * handler search nâng cao
   */
  searchAdvanced(pageIndex: number) {
    if (this.form.invalid) return
    this.pageIndex = pageIndex
    this.isSearchAdvanced = true
    const dataForm = this.form.getRawValue()
    this.filterQuery = {
      ...dataForm
    }
    this.search(splitAndAssignFields(this.filterQuery))
  }

  /**
   * call API get list
   * @param filter
   */
  search(filter: any) {
    this.filterQuery = filter
    this.indicator.showActivityIndicator(true)
    if (this.tableConfig.type === 'table-expand') {
      this._appTableExpandedComponent.filterQuery = this.filterQuery
      this._appTableExpandedComponent.pageIndex = this.pageIndex
      this._appTableExpandedComponent.search(this.filterQuery).then((r: any) => {
        this.responseSearchChanged.emit(r?.data)
        try {
          this._appTableExpandedComponent.expandNode(0)
        } catch (e) {}
      })
    } else {
      this._appTableBodyComponent.filterQuery = this.filterQuery
      this._appTableBodyComponent.pageIndex = this.pageIndex
      this._appTableBodyComponent.search(this.filterQuery).then((r: any) => {
        if (this._appTableBodyComponent.selection) {
          this._appTableBodyComponent.selection.clear()
        }
        this._appTableBodyComponent.isCheckAll = false

        this.responseSearchChanged.emit(r?.data)
        try {
          this._appTableBodyComponent.expandNode(0)
        } catch (e) {}
      })
    }
  }
  /**
   * export excel
   * @param fileName
   */
  onExportExcel(url, fileName = null) {
    let filter = {}
    if (!this.panelOpenState) {
      filter = this.itemControl.getQueryQuickSearchForm(this.formQuickSearch)
    } else {
      filter = this.form.getRawValue()
    }
    this.appService.download(url, filter).subscribe(
      (res) => {
        if (res) {
          FileSaver.saveAs(res.body, `${fileName || ''}_${dayjs().format('YYYYMMDD')}_${new Date().getTime()}.xlsx`)
          this.toastr.showToastri18n('dialog.export-data-success', '', MessageSeverity.success)
        } else {
          this.toastr.showToastri18n('dialog.export-data-error', '', MessageSeverity.error)
        }
      },
      (error) => {
        this.toastr.showToastri18n('dialog.export-data-error', '', MessageSeverity.error)
      }
    )
  }

  openDialogImport(urlImport) {
    this.dialogService.componentDialog(
      AppImportExcelDialogComponent,
      {
        width: '40%',
        maxWidth: '500px',
        height: '45%',
        data: {
          urlDownload: urlImport + '/template',
          urlImport: urlImport
        }
      },
      (data) => {}
    )
  }

  formChanged($event: any) {
    if ($event?.data == EVENT_FORM_CONTROL.CREATE_CONTROL) {
      this.form.setControl($event?.item.key, $event.form.get($event?.item.key))
      this.onFormInit.emit({
        type: EVENT_FORM_CONTROL.CREATE_CONTROL,
        form: this.form,
        items: this.configService.getAllConfig().filter((x) => x.type === 'formControl')
      })
    } else {
      this.onChanged.emit({ type: 'formData', event: $event })
    }
  }

  clickMessageError($event: any) {}
  /**
   * thanhnx
   * xử lý sự kiện bấm vào button
   * @param button
   */
  onClickButton({ data }) {
    if (data?.navigationType === 'nav' && data?.routerName) {
      this.goTo(`${environment.base_path}${data.routerName}`)
    } else if (data?.navigationType === 'popup' && data?.routerName) {
      //TODO: call popup từ string
    } else if (data?.navigationType === 'emit') {
      this.onButtonClick.emit(data)
    } else if (data?.navigationType === 'export-excel') {
      const fileName = getQueryParams(data?.routerName)['fileName']
      this.onExportExcel(data?.routerName, fileName)
    } else if (data?.navigationType === 'import-excel') {
      this.openDialogImport(data?.routerName)
    } else {
      this.toastr.showToastr('', 'Chưa cài đặt để xử lý sự kiện', MessageSeverity.warning)
    }
  }

  onTableActionHandler($event) {
    this.onTableActionClick.emit($event)
  }

  protected readonly FORM_CONTROL_TYPE = FORM_CONTROL_TYPE

  onChangeFields($event: any) {
    const configSelected = this.tableConfig?.data?.quickSearchFields?.find((x) => x.key === $event)
    const item = this.$fields.options.find((x) => x.key === $event)

    if (configSelected && item) {
      this.$keyword.placeholder = configSelected?.placeHolder ? `Nhập ${configSelected?.placeHolder || ''}` : `Nhập ${item?.value || ''}`
      this.cdRef.detectChanges()
    }
  }

  onKeyDownPress($event: any) {
    if ($event.keyCode === 13) {
      this.quickSearch(0)
    }
  }

  resetForm() {
    this.form.reset()
    this.itemControl.markUnTouchAllFormGroup(this.form, true)
    this.searchAdvanced(0)
  }

  onChangeDebounced($event: any) {}

  protected readonly checkSomeScopes = checkSomeScopes

  isShowButton(inputScope: Scopes[]) {
    if (this.checkScope) {
      return this.checkSomeScopes(this.objFunction?.allowScopes, inputScope)
    }
    return true
  }

  changeDisplayColumn($event) {
    // localStorage.setItem(`column-${this.configId}-${this.userInfo.username}`, JSON.stringify($event))
    const data = [...$event]
    console.log('dataaaa', data)
    localStorage.setItem(`column-${this.configId}`, JSON.stringify(data))
    // this.tableConfig = {
    //   ...this.tableConfig,
    //   data: {
    //     ...this.tableConfig.data,
    //     displayedColumns: data
    //   }
    // }
    if (this.tableConfig.type === 'table-expand') {
      this._appTableExpandedComponent.visibleColumns = _.cloneDeep($event)
    } else {
      this._appTableBodyComponent.visibleColumns = _.cloneDeep($event)
    }
  }
  actionContextMenuClick($event) {
    this.contextMenuClick.emit($event)
  }
}
