import { Component, Injector, ViewChild } from '@angular/core';
import { ComponentAbstract, Status } from '@shared';
import { MaterialModule } from 'src/app/material.module';
import { FlexModule } from '@angular/flex-layout';
import { Router } from '@angular/router';
import { ConfigurationService } from '../../services/configuration.service';
import { takeUntil, finalize } from 'rxjs';
import { configurationListConfig } from './configuration-list.config';
import { AppTableBuilderComponent } from '@features/configuration/pages/list/app-table-builder/table-builder.component'
import { TablerIconComponent } from 'angular-tabler-icons'
import { MatButtonModule } from '@angular/material/button';
import { ConfigurationCreateComponent } from '../../pages/create/configuration-create.component';
import { ConfigurationEditComponent } from '../../pages/edit/configuration-edit.component';

@Component({
  selector: 'app-configuration-list',
  templateUrl: './configuration-list.component.html',
  standalone: true,
  imports: [AppTableBuilderComponent, FlexModule, MaterialModule, TablerIconComponent, MatButtonModule],
  styleUrls: ['./configuration-list.component.scss']
})
export class ConfigurationListComponent extends ComponentAbstract {
  @ViewChild('tableBuilderComponent') tableBuilderComponent: AppTableBuilderComponent;

  constructor(
    protected override injector: Injector, 
    private configurationService: ConfigurationService,
    protected router: Router
  ) {
    super(injector);
    // this.getUIConfig('management-configuration-list').then((r) => {
    //   this.tableBuilderComponent.panelOpenState = true;
    //   this.tableBuilderComponent.hideButtonClosedAdvancedSearchBox = true;
    // });
    this.configService.init(configurationListConfig);

  }

  componentInit(): void {}

  onBuilderChanged($event: any) {
    console.log($event);
  }

  onTableActionClick($event: any) {
    switch ($event.type) {
      case 'cellClick': {
        this.router.navigate(['/system-management/configuration/edit', $event.row.key]);
        break;
      }
      case 'buttonCreate': {
        this.openCreateDialog();
        break;
      }
      case 'buttonEdit': {
        this.openEditDialog($event.row.key);
        break;
      }
      case 'buttonDelete': {
        this.openDeleteConfirmDialog($event.row);
        break;
      }
      default:
        break;
    }
  }

  onClickButton($event: any) {
    switch ($event.type) {
      case 'buttonCreate': {
        this.openCreateDialog();
        break;
      }      
      case 'buttonEdit': {
        this.openCreateDialog();
        break;
      }      
      default:
        console.log($event);
        break;
    }
  }

  openDeleteConfirmDialog(row: any) {
    this.dialogService.confirm(
      {
        title: 'Xác nhận xóa',
        message: `Bạn có chắc chắn muốn xóa cấu hình "${row.key}" không?`,
        textButtonLeft: 'Hủy',
        textButtonRight: 'Xóa'
      },
      (result) => {
        if (result) {
          this.deleteConfiguration(row);
        }
      }
    );
  }

  openCreateDialog() {
    this.dialogService.componentDialog(
      ConfigurationCreateComponent,
      {
        width: '700px',
        disableClose: true,
        data: {}
      },
      (result) => {
        if (result) {
          this.tableBuilderComponent.search({});
        }
      }
    );
  }

  openEditDialog(key: string) {
    this.dialogService.componentDialog(
      ConfigurationEditComponent,
      {
        width: '700px',
        disableClose: true,
        data: { key }
      },
      (result) => {
        if (result) {
          this.tableBuilderComponent.search({});
        }
      }
    );
  }

  deleteConfiguration(row: any) {
    this.indicator.showActivityIndicator(true);
    this.configurationService.deleteConfiguration(row.key)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe({
        next: (res: any) => {
          if (res?.httpStatusCode === Status.SUCCESS) {
            this.showDialogSuccessI18n('', 'Xóa cấu hình <br/>thành công');
            this.tableBuilderComponent.search({});
          }
        },
        error: err => {
          this.showDialogErrorI18n(err?.error?.data?.message, 'Xóa cấu hình <br/>thất bại');
        }
      });
  }
}
