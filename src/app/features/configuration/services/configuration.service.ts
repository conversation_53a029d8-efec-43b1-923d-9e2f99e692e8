import { Injectable } from '@angular/core';
import { HttpClientService, HttpOptions } from '@shared';
import { environment } from '@env/environment';
import { Observable } from 'rxjs';
import { CONFIG_ENDPOINT } from '../constants';
import { ConfigurationCreateModel, ConfigurationListModel, ConfigurationModel, ConfigurationUpdateModel } from '../models/configuration.model';

@Injectable({
  providedIn: 'root'
})
export class ConfigurationService {
  constructor(private httpClient: HttpClientService) {}

  getConfigurations(params: any): Observable<any> {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: CONFIG_ENDPOINT.LIST,
      params
    };
    return this.httpClient.get(options);
  }

  getConfigurationDetail(key: string): Observable<ConfigurationModel> {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${CONFIG_ENDPOINT.DETAIL}/${key}`
    };
    return this.httpClient.get(options);
  }

  createConfiguration(data: ConfigurationCreateModel): Observable<any> {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: CONFIG_ENDPOINT.CREATE,
      body: data
    };
    return this.httpClient.post(options);
  }

  updateConfiguration(key: string, data: ConfigurationUpdateModel): Observable<any> {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${CONFIG_ENDPOINT.UPDATE}/${key}`,
      body: data
    };
    return this.httpClient.put(options);
  }

  deleteConfiguration(key: string): Observable<any> {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${CONFIG_ENDPOINT.DELETE}/${key}`
    };
    return this.httpClient.delete(options);
  }
}
