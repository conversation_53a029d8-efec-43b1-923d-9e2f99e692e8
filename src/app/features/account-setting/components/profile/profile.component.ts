import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { UserInfo, UserInfoResponse } from '@shared/models/user-info.model';
import { AppService } from '../../../../services/app.service';
import { finalize, Subject, takeUntil } from 'rxjs';
import { ActivityIndicatorSingletonService } from '@shared/components/feedback/loader/activity-indicator-singleton.service';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule
  ],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit, OnDestroy {
  userInfo: UserInfo | null = null;
  error = false;
  private unsubscribe$ = new Subject<void>();

  constructor(
    private appService: AppService,
    private loaderService: ActivityIndicatorSingletonService
  ) {}

  ngOnInit(): void {
    this.loadUserInfo();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  loadUserInfo(): void {
    this.loaderService.showActivityIndicator();
    this.error = false;
    
    this.appService.getUserInfo()
      .pipe(
        takeUntil(this.unsubscribe$),
        finalize(() => {
          this.loaderService.hideActivityIndicator();
        })
      )
      .subscribe({
        next: (response: UserInfoResponse) => {
          this.userInfo = response.data;
          console.log('Profile: User info loaded:', this.userInfo);
        },
        error: (err) => {
          this.error = true;
          console.error('Profile: Error loading user info:', err);
        }
      });
  }
}
