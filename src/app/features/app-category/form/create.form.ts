import { NgSelectItem, TextAreaItem, TextboxItem } from '@shared/models/item-form.model'

export const fieldName = () =>
  new TextboxItem({
    key: 'name',
    label: 'Tên danh mục',
    placeholder: 'Nhập tên danh mục',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 200,
    order: 1,
    type: 'text',
    requiredMessage: 'Vui lòng nhập tên danh mục'
  })

export const fieldIconPath = () =>
  new TextboxItem({
    key: 'iconPath',
    label: 'Đường dẫn icon',
    placeholder: 'Nhập đường dẫn icon',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 500,
    order: 2,
    type: 'text',
    requiredMessage: 'Vui lòng nhập đường dẫn icon'
  })

export const fieldDescription = () =>
  new TextAreaItem({
    key: 'description',
    label: 'Mô tả',
    placeholder: 'Nhập mô tả',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 500,
    order: 3,
    type: 'textarea',
    requiredMessage: '<PERSON><PERSON> lòng nhập mô tả'
  })

export const fieldSortOrder = () =>
  new TextboxItem({
    key: 'sortOrder',
    label: 'Thứ tự sắp xếp',
    placeholder: 'Nhập thứ tự sắp xếp',
    value: '1',
    required: true,
    order: 4,
    type: 'number',
    requiredMessage: 'Vui lòng nhập thứ tự sắp xếp'
  })

export const fieldSection = () =>
  new NgSelectItem({
    key: 'section',
    label: 'Phân loại',
    placeholder: 'Chọn phân loại',
    value: 'finance',
    options: [
      {
        key: 'finance',
        value: 'Tài chính',
        checked: true
      },
      {
        key: 'utility',
        value: 'Tiện ích',
        checked: false
      },
      {
        key: 'other',
        value: 'Khác',
        checked: false
      }
    ],
    required: true,
    order: 5,
    type: 'select',
    requiredMessage: 'Vui lòng chọn phân loại'
  })

export const fieldSectionTitle = () =>
  new TextboxItem({
    key: 'sectionTitle',
    label: 'Tiêu đề phân loại',
    placeholder: 'Tiêu đề phân loại',
    value: 'Tài chính',
    required: true,
    readOnly: true,
    order: 6,
    type: 'text',
    requiredMessage: 'Vui lòng nhập tiêu đề phân loại'
  })

export const fieldActionType = () =>
  new NgSelectItem({
    key: 'actionType',
    label: 'Loại hành động',
    placeholder: 'Chọn loại hành động',
    value: 'navigate',
    options: [
      {
        key: 'navigate',
        value: 'Navigate',
        checked: true
      },
      {
        key: 'link',
        value: 'Link',
        checked: false
      }
    ],
    required: true,
    order: 7,
    type: 'select',
    requiredMessage: 'Vui lòng chọn loại hành động'
  })

export const fieldActionTarget = () =>
  new TextboxItem({
    key: 'actionTarget',
    label: 'Đích đến',
    placeholder: 'Nhập đích đến',
    value: '',
    required: true,
    order: 8,
    type: 'text',
    requiredMessage: 'Vui lòng nhập đích đến'
  })

export const fieldStatus = () =>
  new NgSelectItem({
    key: 'status',
    label: 'Trạng thái',
    placeholder: 'Chọn trạng thái',
    value: 'INACTIVE',
    options: [
      {
        key: 'ACTIVE',
        value: 'Hoạt động',
        checked: false
      },
      {
        key: 'INACTIVE',
        value: 'Không hoạt động',
        checked: true
      }
    ],
    required: true,
    order: 9,
    type: 'select',
    requiredMessage: 'Vui lòng chọn trạng thái'
  })

export const CREATE_APP_CATEGORY_FORM = [
  fieldName,
  fieldIconPath,
  fieldDescription,
  fieldSortOrder,
  fieldSection,
  fieldSectionTitle,
  fieldActionType,
  fieldActionTarget,
  fieldStatus
]
