import { Injectable } from '@angular/core'
import { environment } from '@env/environment'
import { HttpClientService, HttpOptions } from '@shared'
import { AppService } from '@services/app.service'
import { PATH_API } from '@features/partner/constants'

@Injectable({
  providedIn: 'root'
})
export class SalaryService {
  constructor(
    private httpClient: HttpClientService,
    private appService: AppService
  ) {}

  /**
   * danh sach
   * @param params
   */
  getListFee() {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.LIST_FEE
    }
    return this.httpClient.get(options)
  }
  createPartner(body) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: PATH_API.CREATE,
      body
    }
    return this.httpClient.post(options)
  }
  getDetailPartner(id: string) {
    const options: HttpOptions = {
      url: environment.hostApi,
      path: `${PATH_API.LIST}/${id}`
    }
    return this.httpClient.get(options)
  }
}
