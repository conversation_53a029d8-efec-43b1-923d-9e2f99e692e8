export const fieldBuilderConfig: any = [
  {
    id: 'button-list',
    type: '_container',
    items: [],
    classes: 'd-flex'
  },
  {
    id: 'searchAdvanced',
    type: '_container',
    items: [],
    classes: 'd-flex flex-row'
  },
  {
    id: 'content',
    type: '_container',
    items: ['table-1'],
    classes: 'flex-column panel'
  },
  {
    type: 'table',
    id: 'table-1',
    data: {
      quickSearchFields: [
        {
          key: 'component',
          text: 'Tên Component'
        },
        {
          key: 'createdBy',
          text: 'Người tạo'
        }
      ],
      apiList: 'dcmcore-services-management/v1/config-builder/component/tree',
      tableTitle: 'mẫu nhập liệu',
      apiCreate: '',
      displayedColumns: [
        {
          name: 'STT',
          field: 'no',
          path: 'no',
          show: true
        },
        {
          name: 'Tên component',
          field: 'component',
          path: 'component',
          show: true,
          type: 'textbox'
        },
        {
          name: 'Version',
          field: 'dataVersion',
          path: 'dataVersion',
          show: true,
          type: 'textbox',
          isQuickSearch: true,
          isAdvancedSearch: true,
          maxLength: '60'
        },
        {
          show: true,
          type: 'textbox',
          isQuickSearch: true,
          isAdvancedSearch: true,
          name: 'Người tạo',
          field: 'createdBy',
          path: 'createdBy'
        },
        {
          show: true,
          type: 'textbox',
          isQuickSearch: true,
          isAdvancedSearch: true,
          name: 'Người cập nhật',
          field: 'updatedBy',
          path: 'updatedBy'
        },
        {
          name: 'Hành động',
          field: 'action',
          path: '',
          show: true
        }
      ],
      pageSize: 10,
      buttonLists: [],
      columnActionLists: [
        {
          title: 'Xem',
          type: 'VIEW',
          // icon: "ic-delete",
          icon: 'ic-eye',
          navigationType: 'emit',
          routerName: ':component'
        },
        {
          title: 'Xóa',
          type: 'DELETE',
          scope: '03_DELETE',
          // icon: "ic-delete",
          icon: 'ic-delete',
          navigationType: 'emit',
          routerName: ':component'
          // condition: {
          //   condition: "and",
          //   rules: [
          //     {
          //       field: "children",
          //       operator: "!=",
          //       value: undefined
          //     }
          //   ]
          // }
        },
        {
          title: 'Export JSON',
          type: 'EXPORT',
          // icon: "ic-delete",
          icon: 'ic-download',
          navigationType: 'emit',
          routerName: ':component'
        }
      ],
      apiDelete: 'dcmcore-services-management/v1/config-builder',
      isTreeData: true,
      hideSearchAdvanced: true
    },
    classes: ''
  },
  {
    id: 'text',
    type: 'text',
    content: 'Danh sách sản phẩm'
  }
]
