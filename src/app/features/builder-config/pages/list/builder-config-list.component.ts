import { Component, Injector, ViewChild } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { ComponentAbstract, SharedModule, Status } from '@shared'
import { fieldBuilderConfig } from '@features/builder-config/pages/list/config'
import { AppTableBuilderComponent } from '@shared/components/page-builder/app-table-builder/table-builder.component'
import { ConfigBuilderService } from '@features/builder-config/services/builder-config.service'
import { finalize, takeUntil } from 'rxjs'
import { DialogConfigJsonComponent } from '@features/builder-config/pages/list/dialog-config-json/dialog-config-json.component'

@Component({
  selector: 'app-builder-config-list',
  standalone: true,
  imports: [FormsModule, SharedModule, AppTableBuilderComponent],
  templateUrl: './builder-config-list.component.html',
  styleUrl: './builder-config-list.component.scss'
})
export class BuilderConfigListComponent extends ComponentAbstract {
  @ViewChild('builderChild') builderChild: AppTableBuilderComponent

  config = fieldBuilderConfig
  constructor(
    protected override injector: Injector,
    private _configService: ConfigBuilderService
  ) {
    super(injector)
    this.initBuilderUIConfigFromConfig('table-builder-list', fieldBuilderConfig).then(() => {})
  }
  protected componentInit(): void {}

  onTableActionClick($event: any) {
    console.log('hí hí', $event)
    const event = String($event?.type || '').toUpperCase()
    if (event === 'VIEW') {
      this.getDetailConfig($event.row?.id, event)
    } else if (event === 'EXPORT') {
      this.getDetailConfig($event.row?.id, event)
    } else if (event === 'DELETE') {
      this.deleteConfigBuilder($event.row?.component, $event.row?.dataVersion)
    }
  }

  showPopupJson() {}
  deleteConfigBuilder(component, version) {
    this._configService
      .delete(component, version)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => {
          this.builderChild.searchAdvanced(this.builderChild.pageIndex)
          return this.indicator.hideActivityIndicator(true)
        })
      )
      .subscribe(
        (res: any) => {
          if (res?.httpStatusCode === Status.SUCCESS) {
            this.showDialogSuccessI18n('', `Xóa cấu hình thành công`)
          }
        },
        (response) => {
          this.showDialogErrorI18n(response?.error?.message, `Xóa cấu hình thất bại`)
        }
      )
  }

  getDetailConfig(id: string, type: string) {
    this._configService
      .detail(id)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => {
          return this.indicator.hideActivityIndicator(true)
        })
      )
      .subscribe(
        (res: any) => {
          if (res?.httpStatusCode === Status.SUCCESS) {
            if (type === 'VIEW') {
              this.dialogService.componentDialog(
                DialogConfigJsonComponent,
                {
                  width: '850px',
                  data: res?.data
                },
                (res) => {}
              )
            } else {
              this.exportJSON(res?.data)
            }
          }
        },
        (response) => {
          this.showDialogErrorI18n(response?.error?.message, `Xóa cấu hình thất bại`)
        }
      )
  }

  exportJSON(data) {
    const jsonString = JSON.stringify(data, null, 2)
    const blob = new Blob([jsonString], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${data.component}-${data.id}.json`
    a.click()
    URL.revokeObjectURL(url)
    a.remove()
  }
}
