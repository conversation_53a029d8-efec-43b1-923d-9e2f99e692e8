import { Routes } from '@angular/router'

export const routes: Routes = [
  {
    path: '',
    title: 'Quản lý user',
    loadComponent: async () => (await import('./pages/list/user-list.component')).UserListComponent,
    data: {
      breadcrumb: 'Quản lý user',
      showBreadcrumb: false
    }
  },
  {
    path: 'create',
    title: 'Thêm mới user',
    loadComponent: async () => (await import('./pages/create/user-create.component')).UserCreateComponent,
    data: { breadcrumb: 'Thêm mới user' }
  }
]
