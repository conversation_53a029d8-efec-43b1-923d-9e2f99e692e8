import { Component, Injector, isDevMode, ViewChild } from '@angular/core'
import { ComponentAbstract } from '@shared'
import { AppTableBuilderComponent } from '@shared/components/page-builder/app-table-builder/table-builder.component'
import { FlexModule } from '@angular/flex-layout'
import { ROUTES_NAME_SERVICE } from '../../../../app.routes'
import { finalize, takeUntil } from 'rxjs'
import { UserService } from '@features/user/services/user.service'

@Component({
  selector: 'app-user-list',
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.scss'],
  standalone: true,
  imports: [AppTableBuilderComponent, FlexModule]
})
export class UserListComponent extends ComponentAbstract {
  @ViewChild('tableBuilderComponent') tableBuilderComponent: AppTableBuilderComponent

  constructor(
    protected override injector: Injector,
    private service: UserService
  ) {
    super(injector)
    this.getUIConfig('USER_LIST').then((r) => {})
    // this.initBuilderUIConfigLocal('management-product-list').then((r) => {
    //   this.config = r
    // })
  }

  componentInit(): void {}

  onBuilderChanged($event: any) {
    isDevMode() && console.log($event)
  }

  onTableActionClick($event: any) {
    if ($event.type === 'copy') {
      this.goTo(ROUTES_NAME_SERVICE.CREATE, { action: $event.type, id: $event?.row?.serviceCode })
    }

    if (String($event?.type || '').toLowerCase() === 'delete') {
      debugger
      this.dialogService.confirm(
        {
          title: `Bạn có chắc muốn xóa người dùng này?`,
          message: '',
          textButtonRight: 'btn.delete'
        },
        (result) => {
          if (result) {
            this.indicator.showActivityIndicator()
            this.service
              .delete($event.row.user.id, true)
              .pipe(
                takeUntil(this.ngUnsubscribe),
                finalize(() => this.indicator.hideActivityIndicator())
              )
              .subscribe(
                (res: any) => {
                  if (res) {
                    this.tableBuilderComponent.search({})
                    this.showDialogSuccessI18n('', `Xóa người dùng <br/>thành công`)
                  }
                },
                (error) => {
                  debugger
                  this.showDialogErrorI18n(error?.error?.message, error?.error?.error)
                }
              )
          }
        }
      )
    }
  }

  onButtonClick($event: any) {}
}
