<span [dir]="options.dir!">
  <mat-sidenav-container
    class="mainWrapper"
    autosize
    autoFocus
    [ngClass]="{
      'sidebarNav-mini': options.sidenavCollapsed && options.navPos !== 'top' && !resView,
      'sidebarNav-horizontal': options.horizontal,
      cardBorder: options.cardBorder,
      orange_theme: options.activeTheme == 'orange_theme',
      blue_theme: options.activeTheme == 'blue_theme',
      aqua_theme: options.activeTheme == 'aqua_theme',
      purple_theme: options.activeTheme == 'purple_theme',
      green_theme: options.activeTheme == 'green_theme',
      cyan_theme: options.activeTheme == 'cyan_theme'
    }">
    <!-- ============================================================== -->
    <!-- Vertical Sidebar -->
    <!-- ============================================================== -->
    @if (!options.horizontal) {
      <mat-sidenav
        #leftsidenav
        [mode]="isOver ? 'over' : 'side'"
        [opened]="options.navPos === 'side' && options.sidenavOpened && !isOver && !resView"
        (openedChange)="onSidenavOpenedChange($event)"
        (closedStart)="onSidenavClosedStart()"
        class="sidebarNav">
        <div class="flex-layout">
          <app-sidebar (toggleMobileNav)="sidenav.toggle()" [showToggle]="isOver"></app-sidebar>
          <ng-scrollbar class="position-relative" style="height: 100%">
            <mat-nav-list class="sidebar-list">
              @for (item of navItems; track item) {
                <app-nav-item [item]="item" (notify)="sidenav.toggle()"></app-nav-item>
              }
            </mat-nav-list>
          </ng-scrollbar>
        </div>
      </mat-sidenav>
    }

    <!-- ============================================================== -->
    <!-- horizontal Sidebar -->
    <!-- ============================================================== -->
    @if (resView) {
      <mat-sidenav
        #leftsidenav
        [mode]="'over'"
        [opened]="options.sidenavOpened && !isTablet"
        (openedChange)="onSidenavOpenedChange($event)"
        (closedStart)="onSidenavClosedStart()"
        class="sidebarNav">
        <app-sidebar></app-sidebar>
        <ng-scrollbar class="position-relative" style="height: 100%">
          <mat-nav-list class="sidebar-list">
            @for (item of navItems; track item) {
              <app-nav-item [item]="item" (notify)="sidenav.toggle()"></app-nav-item>
            }
          </mat-nav-list>
        </ng-scrollbar>
      </mat-sidenav>
    }

    <!-- ============================================================== -->
    <!-- Main Content -->
    <!-- ============================================================== -->
    <mat-sidenav-content class="contentWrapper" #content>
      <!-- ============================================================== -->
      <!-- VerticalHeader -->
      <!-- ============================================================== -->
      <app-header
        [showToggle]="!isOver"
        (toggleCollapsed)="toggleCollapsed()"
        (toggleMobileNav)="sidenav.toggle()"
        (toggleMobileFilterNav)="filterNavRight.toggle()"></app-header>
      <div class="body-wrapper">
        <main
          #pageWrapper
          class="pageWrapper"
          [ngClass]="{
            maxWidth: options.boxed
          }">
          <!-- <app-breadcrumb></app-breadcrumb> -->
          <app-my-breadcrumb></app-my-breadcrumb>
          <!-- ============================================================== -->
          <!-- Outlet -->
          <!-- ============================================================== -->
          <router-outlet></router-outlet>
          <!--          <div class="customizerBtn">-->
          <!--            <button mat-fab color="warn" (click)="customizerRight.toggle()">-->
          <!--              <mat-icon>settings</mat-icon>-->
          <!--            </button>-->
          <!--          </div>-->
        </main>
      </div>
    </mat-sidenav-content>

    <mat-sidenav #customizerRight mode="push" position="end">
      <div class="p-x-16 p-y-20 d-flex align-items-center justify-content-between">
        <h3 class="mat-subtitle-1 f-s-21 f-w-600">Settings</h3>
        <button class="d-lg-none" mat-button (click)="customizerRight.toggle()">Close</button>
      </div>
      <mat-divider></mat-divider>
      <app-customizer (optionsChange)="receiveOptions($event)"></app-customizer>
    </mat-sidenav>
  </mat-sidenav-container>

  <!-- ------------------------------------------------------------------
    Mobile Apps Sidebar
    ------------------------------------------------------------------ -->
  <mat-drawer #filterNavRight mode="over" position="end" class="filter-sidebar">
    <div>
      <div class="d-flex justify-content-between align-items-center">
        <div class="branding">
          <a href="/">
            <img src="./assets/images/logos/logo.svg" class="align-middle m-2" alt="logo" />
          </a>
        </div>
        <button mat-icon-button (click)="filterNavRight.toggle()" class="d-flex justify-content-center">
          <i-tabler name="x" class="icon-18 d-flex"></i-tabler>
        </button>
      </div>
      <div class="row p-x-16">
        @for (appdd of apps; track appdd.title) {
          <div class="col-12 text-hover-primary">
            <a [routerLink]="[appdd.link]" class="d-flex align-items-center text-decoration-none m-b-24">
              <span class="text-{{ appdd.color }} bg-light-{{ appdd.color }} rounded icon-48 d-flex align-items-center justify-content-center">
                <span class="iconify icon-20" [attr.data-icon]="appdd.icon"></span>
              </span>

              <div class="m-l-16">
                <h5 class="f-s-15 f-w-600 m-0 textprimary mat-subtitle-1 hover-text">
                  {{ appdd.title }}
                </h5>
                <span class="mat-body-1 f-s-13">{{ appdd.subtitle }}</span>
              </div>
            </a>
          </div>
        }
      </div>
    </div>
  </mat-drawer>
</span>
<div class="ui-builder">
  <div class="uib-bootstrap">
    <app-ui-configurator></app-ui-configurator>
  </div>
</div>
<!-- app-loader replaced with MatDialog implementation -->
