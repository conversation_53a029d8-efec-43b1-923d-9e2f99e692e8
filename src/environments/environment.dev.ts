export const environment = {
  production: false,
  key: 'app_',
  uat: true,
  logger: true,
  skipAuth: false,
  origin: 'http://localhost:4200', // need for micro frontend
  base_path: '', // no set domain
  base_url: 'https://localhost:4200',
  hostApi: 'https://api.pronexus.vn',
  // hostApi: 'http://localhost:8000',
  urlConfigBuilder: 'http://localhost:3000',
  keepStateUrlLogin: false,
  appRootElement: 'app_tgone-app-element',
  services: {
    portal: 'portal/api',
    salary: 'salary-advance/api',
    user: 'user/api',
    file: 'file/api'
  },
  keycloak: {
    // url: '/auth',
    url: 'https://identity.pronexus.vn',
    realm: 'pronexus_dev',
    clientId: 'pronexus_dev_frontend',
    id: 'pronexus_dev_frontend'
    // relationShip: 'pronexus-service',
    // realmManagement: 'realm-management',
  },
  timeOut: 60000 * 4
}
