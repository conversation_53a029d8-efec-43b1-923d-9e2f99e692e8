# PVCombank Admin Frontend - Hướng dẫn Phát triển

## Cấu trúc Project

```
/src
  /app
    /features       # Các tính năng của ứng dụng
      /employee     # Tính năng quản lý nhân viên (mẫu để tham khảo)
      /report       # Tính năng báo cáo
    /shared         # Các thành phần dùng chung
      /abstract     # Các lớp abstract
      /components   # Các component dùng chung
        /data-input # Input controls
        /page-builder # Các component xây dựng giao diện
      /services     # Các service dùng chung
      /utils        # Các utility function
    /assets         # Tài nguyên tĩnh
    app.routes.ts   # Cấu hình route chính
```

## Mô hình phát triển

### Mẫu tính năng CRUD chuẩn

Mỗi tính năng CRUD cần có cấu trúc thư mục như sau:

```
/features
  /[featureName]/
    ├── constants.ts             # Constants cho feature
    ├── [featureName].routes.ts  # Cấu hình routes
    ├── form/                    # Cấu hình form
    │   └── create.form.ts
    │   └── edit.form.ts
    │   └── list.form.ts
    ├── models/                  # Model cho feature
    │   └── create.model.ts
    │   └── edit.model.ts
    │   └── list.model.ts
    ├── pages/                   # Components
    │   └── create/              # Component create
    │   └── edit/                # Component edit
    │   └── list/                # Component list
    ├── services/                # Service cho feature
    │   └── *.service.ts
    └── shared/                  # Component & model dùng chung
        └──
```

## Hướng dẫn phát triển tính năng Report

### 1. Tạo cấu trúc thư mục

```
src/app/features/report/pages/new-report-name/
├── list/
│   ├── new-report-name-list.component.ts
│   ├── new-report-name-list.component.html
│   └── new-report-name-list.component.scss
```

### 2. Cập nhật routing

Thêm route mới vào `src/app/features/report/report.routes.ts`:

```typescript
{
  path: 'new-report-name',
  title: 'Tiêu đề báo cáo',
  loadComponent: async () => 
    (await import('./pages/new-report-name/list/new-report-name-list.component'))
      .NewReportNameListComponent,
  data: {
    breadcrumb: ''
  }
}
```

### 3. Component HTML

Tạo file `.html` sử dụng mẫu từ các component report khác:

```html
<app-report-table-builder
  #reportTableBuilderComponent
  [componentName]="'NewReportNameListComponent'"
  (onButtonClick)="onButtonClick($event)"
  (onTableActionClick)="onTableActionClick($event)">
  <div buttons>
    <button mat-raised-button color="primary" type="button" (click)="onClickExport()">
      <div class="d-flex align-items-center">
        <i-tabler class="icon-20 m-r-4" name="download"></i-tabler>
        <span>Export dữ liệu</span>
      </div>
    </button>
  </div>
</app-report-table-builder>
```

### 4. Component TypeScript

Tạo file `.ts` với cấu trúc chuẩn:

```typescript
import { Component, ViewChild, Injector, OnInit } from '@angular/core';
import { ComponentAbstract } from '@shared/abstract/component.abstract';
import { FlexModule } from '@angular/flex-layout';
import { MatButton } from '@angular/material/button';
import { TablerIconComponent } from 'angular-tabler-icons';
import { AppReportTableBuilderComponent } from '@shared/components/page-builder/app-report-table-builder/report-table-builder.component';
import { isDevMode } from '@angular/core';

@Component({
  selector: 'app-new-report-name-list',
  templateUrl: './new-report-name-list.component.html',
  standalone: true,
  imports: [FlexModule, AppReportTableBuilderComponent, MatButton, TablerIconComponent],
  styleUrls: ['./new-report-name-list.component.scss']
})
export class NewReportNameListComponent extends ComponentAbstract implements OnInit {
  @ViewChild('reportTableBuilderComponent') reportTableBuilderComponent: AppReportTableBuilderComponent;

  constructor(protected override injector: Injector) {
    super(injector);
    this.getUIConfig('new-report-name-list').then(() => {
      // Cấu hình được khởi tạo
    });
  }

  ngOnInit(): void {
    super.initData();
  }

  componentInit(): void {
    this.elementService.checkViewChildExists('reportTableBuilderComponent', this).then(() => {
      this.reportTableBuilderComponent.panelOpenState = true;
      this.reportTableBuilderComponent.hideButtonClosedAdvancedSearchBox = true;
    });
  }

  onBuilderChanged($event: any) {
    isDevMode() && console.log($event);
  }

  onTableActionClick($event: any) {
    console.log($event);
  }

  onButtonClick($event: any) {
    console.log($event);
  }

  onClickExport() {
    console.log('Exporting data...');
    // Thêm chức năng xuất dữ liệu
  }
}
```

## Hướng dẫn phát triển tính năng CRUD đầy đủ

### 1. Cấu hình routing

Tạo file `[feature-name].routes.ts`:

```typescript
import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    title: 'Danh sách [tên tính năng]',
    loadComponent: async () => (await import('./pages/list/[feature-name]-list.component')).[FeatureName]ListComponent,
    data: {
      breadcrumb: ''
    }
  },
  {
    path: 'create',
    title: 'Tạo [tên tính năng]',
    loadComponent: async () => (await import('./pages/create/create-[feature-name].component')).Create[FeatureName]Component,
    data: {
      breadcrumb: ''
    }
  },
  {
    path: 'edit/:id',
    title: 'Chỉnh sửa [tên tính năng]',
    loadComponent: async () => (await import('./pages/edit/edit-[feature-name].component')).Edit[FeatureName]Component,
    data: {
      breadcrumb: ''
    }
  }
];
```

### 2. Tạo models

**list.model.ts**
```typescript
export class [FeatureName]ListModel {
  id: string;
  name: string;
  // các thuộc tính khác
}
```

**create.model.ts**
```typescript
export class [FeatureName]CreateModel {
  name: string;
  // các thuộc tính khác
}
```

**edit.model.ts**
```typescript
import { [FeatureName]CreateModel } from './create.model';

export class [FeatureName]EditModel extends [FeatureName]CreateModel {
  id: string;
}
```

### 3. Tạo form config

**create.form.ts**
```typescript
import { FORM_CONTROL_TYPE } from '@shared';
import { FormType } from '@shared/models';

export const CreateFormConfig: FormType<any>[] = [
  {
    value: '',
    key: 'name',
    label: 'Tên',
    order: 1,
    controlType: FORM_CONTROL_TYPE.TEXT_BOX,
    focus: true,
    type: 'text',
    title: false,
    customValidate: '',
    countMaxLength: true,
    required: true
  },
  // Thêm các trường khác
];
```

### 4. Tạo components

**list.component.ts**
```typescript
import { Component, Injector, OnInit } from '@angular/core';
import { ComponentAbstract } from '@shared/abstract/component.abstract';

@Component({
  selector: 'app-[feature-name]-list',
  templateUrl: './[feature-name]-list.component.html',
  styleUrls: ['./[feature-name]-list.component.scss']
})
export class [FeatureName]ListComponent extends ComponentAbstract implements OnInit {
  constructor(protected override injector: Injector) {
    super(injector);
  }

  ngOnInit(): void {
    super.initData();
  }

  componentInit(): void {
    // Thực hiện khởi tạo component
  }
}
```

**create.component.ts**
```typescript
import { Component, Injector, OnInit } from '@angular/core';
import { ComponentAbstract } from '@shared/abstract/component.abstract';
import { FormBuilder } from '@angular/forms';
import { CreateFormConfig } from '../../form/create.form';

@Component({
  selector: 'app-create-[feature-name]',
  templateUrl: './create-[feature-name].component.html',
  styleUrls: ['./create-[feature-name].component.scss']
})
export class Create[FeatureName]Component extends ComponentAbstract implements OnInit {
  constructor(protected override injector: Injector, private fb: FormBuilder) {
    super(injector);
  }

  ngOnInit(): void {
    super.initData();
    this.form = this.fb.group({
      // Cấu hình form controls
    });
  }

  componentInit(): void {
    // Khởi tạo form
    this.formControls = CreateFormConfig;
  }

  onSubmit(): void {
    // Xử lý submit form
  }
}
```

## Các Component và dịch vụ dùng chung

### Components

1. **ComponentAbstract**: Lớp trừu tượng cho tất cả các component
   - Phải implement `componentInit()`
   - Kế thừa từ `ComponentBaseAbstract`

2. **AppReportTableBuilderComponent**: Component xây dựng bảng báo cáo
   - Được sử dụng trong các trang báo cáo

3. **Data Input Components**:
   - `TextControlComponent`: Nhập liệu dạng text
   - `SelectControlComponent`: Component select

### Services

1. **HttpClientService**: Service gọi API
   - Path: `src/app/shared/services/httpclient.service.ts`

2. **SessionService**: Quản lý phiên làm việc
   - Lưu trữ thông tin phiên

3. **ElementService**: Dịch vụ thao tác với DOM
   - Cung cấp `checkViewChildExists`

## Form Configuration

Khi định nghĩa form (FormType<T>), cần cung cấp đầy đủ các thuộc tính:

- **value**: Giá trị của control
- **key**: Định danh của control
- **label**: Nhãn hiển thị
- **order**: Thứ tự hiển thị
- **controlType**: Loại control (FORM_CONTROL_TYPE)
- **focus**: Có focus hay không
- **type**: Loại input
- **title**: Hiển thị tiêu đề
- **customValidate**: Validate tùy chỉnh
- **countMaxLength**: Đếm ký tự tối đa

Các giá trị FORM_CONTROL_TYPE phổ biến:
- TEXT_BOX: 'textbox'
- TEXTAREA: 'textarea'
- DROPDOWN: 'ngselect'
- CHECKBOX: 'checkbox'

## Quy trình CI/CD

- Sử dụng Docker cho containerization
- Pipeline Jenkins cho CI/CD
- Nginx cho hosting production
- Cấu hình theo môi trường
- Kiểm thử tự động

## Bảo mật

- Thực hiện Auth guard
- Cưỡng chế HTTPS
- Phòng chống XSS
- Bảo vệ CSRF
- Headers bảo mật
