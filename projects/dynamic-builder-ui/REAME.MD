**MB UI Builder** là một thư viện hỗ trợ kéo thả, cấu hình thông số giao <PERSON>, logic để phát triển logic trực tiếp trên live thay vì fix bug xong rồi build.

## Hình thức áp dụng

Thêm thư viện vào ứng dụng

`app.module.ts`:

```ts
import { StoreModule } from "@ngrx/store";
import { ConfigModule } from '@tnx/ngx-ui-builder';

@NgModule({
    imports: [
        ...
        // Mandatory as we depends on NgRx to undo/redo actions
        StoreModule.forRoot({}),
        ConfigModule
    ]
})
```

Thêm thư viện css

```scss
@import 'bootstrap/dist/css/bootstrap-utilities.min'; // Unless you already use Bootstrap or Bootstrap utilities
@import '@tnx/ngx-ui-builder/styles/ui-builder';
```

```ts
import { ConfigService } from '@tnx/ngx-ui-builder';

...
export class AppComponent {

  constructor(
    public configService: ConfigService
  ){
    this.configService.init([]);
  }
```

- Trong bất kỳ component angular nào, khi muốn sử dụng kéo thả hoặc cấu hình thì sẽ định nghĩa 1 vùng zone:
- Trong vòng zone có thể có rất nhiều template khác nhau

```html
<uib-zone id="test">
  <ng-template uib-template="test">
    <h1>Hello world</h1>
  </ng-template>
</uib-zone>
```

Đây mà phần cấu hình các toolbar, config khi mà click vào template, cho phép dev sẽ thực hiện config trên UI

```html
<div class="uib-bootstrap">
  <uib-toolbar>
    <!-- Inject custom toolbar buttons here -->
  </uib-toolbar>

  <uib-configurator>
    <!-- Inject custom configurators here -->
  </uib-configurator>
</div>
```

```ts
import { Component, OnInit } from '@angular/core'
import { ConfigService } from 'ngx-ui-builder'

export class MyComponent implements OnInit {
  constructor(public configService: ConfigService) {}

  ngOnInit() {
    this.configService.init([
      {
        id: 'products',
        type: '_container',
        items: ['name', 'description', 'image', 'price']
      },
      {
        id: 'price',
        type: 'price',
        currency: 'EUR'
      }
    ])
  }
}
```
