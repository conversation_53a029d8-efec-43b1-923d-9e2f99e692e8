<div class="btn-group uib-toolbar uib-toolbar-anim" *ngIf="{ enabled: configurableService.editorEnabled$ | async } as editor">
  <ng-container *ngIf="editor.enabled">
    <ng-container [ngTemplateOutlet]="template"></ng-container>
    <button
      class="btn btn-light btn-sm"
      (click)="configService.undo()"
      [ngClass]="{ disabled: !(configService.canUndo$() | async) }"
      uib-tooltip="Undo"
      placement="top">
      <span>Undo</span>
    </button>
    <button
      class="btn btn-light btn-sm"
      (click)="configService.redo()"
      [ngClass]="{ disabled: !(configService.canRedo$() | async) }"
      uib-tooltip="Redo"
      placement="top">
      <span>Redo</span>
    </button>
    <button class="btn btn-light btn-sm" (click)="configService.exportConfiguration()" uib-tooltip="Download" placement="top">
      <span>Download config</span>
    </button>
    <button class="btn btn-light btn-sm" (click)="toggleJsonEditor()" uib-tooltip="Edit Json" placement="top">
      <span>Edit config</span>
    </button>
  </ng-container>
  <button class="btn btn-light btn-sm" (click)="toggleEditor()" uib-tooltip="Enable/Disable UI-Editor" placement="top" container="">
    <!--    <svg-icon name="{{editor.enabled ? 'eye_slash' : 'eye' }}"></svg-icon>-->
    {{ editor.enabled ? 'UI VIEW' : 'UI EDIT' }}
  </button>
</div>
