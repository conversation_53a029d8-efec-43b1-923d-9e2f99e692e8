.editor-line {
  display: grid;
  grid-template-columns: 105px 1fr;
  align-items: center;
  margin-bottom: 0.5rem;
}

/* ================ RADIO BUTTONS ================= */

.radio-group {
  // --rotate: 90deg;
  --bg-checked: #141ed2;
  --bg-hover: #d2d2d2;

  display: flex;
  align-items: center;
  gap: 0.2rem;
}

.radio-group label {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;

  width: 36px;
  height: 36px;

  > svg {
    transform: rotate(var(--rotate, 0deg));
    transition: transform 0.2s ease-in-out;
  }
}

.radio-group input[type='radio'] {
  display: none;
}

.radio-group input[type='radio'] + label {
  cursor: pointer;
  padding: 0.3rem 0.5rem;
  border-radius: 4px;
  border: 1px dashed #ccc;
  transition:
    background-color 0.2s ease-in-out,
    fill 0.2s ease-in-out,
    color 0.2s ease-in-out;
}

.radio-group input[type='radio']:checked + label {
  border: 1px solid #e2e2e2;
  background-color: var(--bg-checked);
  svg {
    fill: #fff;
  }
}

.radio-group input[type='radio'] + label:hover {
  color: white;
  fill: white;
  background-color: var(--bg-hover);
}
