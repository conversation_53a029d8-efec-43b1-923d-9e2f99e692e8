import { createAction, props } from '@ngrx/store'
import { ComponentConfig } from './config.model'

/**
 * Initialize the configuration
 */
export const initConfig = createAction('INIT', props<{ componentId: string; config: ComponentConfig[] }>())

/**
 * Remove the current configuration and set a new one
 */
export const setConfig = createAction('SET', props<{ componentId: string; config: ComponentConfig[] }>())

/**
 * Add a new configuration item
 */
export const addConfig = createAction('ADD', props<{ componentId: string; config: ComponentConfig }>())

/**
 * Remove an existing configuration item
 */
export const removeConfig = createAction('REMOVE', props<{ componentId: string; id: string }>())

/**
 * Update an existing configuration item (or list of items)
 */
export const updateConfig = createAction('UPDATE', props<{ componentId: string; config: ComponentConfig | ComponentConfig[] }>())
